# Image Processing and Classification Configuration
# ================================================

# Scanner Settings
# ---------------
validate_mime_type: true  # Validate MIME types for additional security

# Processor Settings
# -----------------
target_size: [224, 224]           # Target image dimensions [width, height]
maintain_aspect_ratio: true       # Maintain aspect ratio when resizing
convert_to_grayscale: false       # Convert images to grayscale
normalize: true                   # Apply normalization for deep learning
enhance_quality: true             # Apply quality enhancements
batch_size: 32                    # Batch size for processing
brightness_factor: 1.0            # Brightness adjustment factor (1.0 = no change)
contrast_factor: 1.0              # Contrast adjustment factor (1.0 = no change)
sharpness_factor: 1.0             # Sharpness adjustment factor (1.0 = no change)
noise_reduction: true             # Apply noise reduction filter

# Classifier Settings
# ------------------
use_pretrained_model: true        # Use pretrained model for classification
model_name: "resnet50"            # Pretrained model name (resnet50, efficientnet-b0, mobilenetv2)
confidence_threshold: 0.5         # Minimum confidence for high-confidence predictions
custom_categories: []             # Custom categories for classification
                                  # Example: ["photos", "graphics", "documents"]

# Enhanced File Size Classification (in bytes)
file_size_classification:
  enabled: true
  thresholds:
    small: 512000      # < 500KB
    medium: 2097152    # < 2MB (500KB to 2MB = medium)
    # large: > 2MB (anything above medium threshold)

# Enhanced Dimension Classification
dimension_classification:
  enabled: true
  resolution_thresholds:
    low_resolution: [640, 480]        # < 640x480 pixels
    standard_resolution: [1920, 1080] # 640x480 to 1920x1080 pixels
    # high_resolution: > 1920x1080 pixels (anything above standard)
  aspect_ratio_tolerance: 0.1         # Tolerance for square detection (1.0 ± tolerance)

# Content Classification Settings
content_classification:
  enabled: true
  available_models:
    - "resnet50"
    - "efficientnet-b0"
    - "mobilenetv2"
  model_confidence_threshold: 0.3    # Minimum confidence for content classification
  max_predictions: 5                 # Maximum number of top predictions to store

# Object Detection Settings
object_detection:
  enabled: false                     # Enable/disable object detection (requires additional dependencies)
  model_name: "yolov8n"             # Object detection model (yolov5s, yolov8n, yolov8s, yolov8m)
  confidence_threshold: 0.5          # Minimum confidence for object detection
  iou_threshold: 0.45               # IoU threshold for non-maximum suppression
  max_detections: 100               # Maximum number of detections per image
  save_detection_images: false      # Save images with detection bounding boxes
  object_categories:                # Categories for object-based classification
    contains_objects: "Contains Objects"
    object_free: "Object-Free"

# Legacy size thresholds (maintained for backward compatibility)
size_thresholds:
  small: 100000      # < 100KB
  medium: 1000000    # < 1MB
  large: 5000000     # < 5MB

# Legacy dimension thresholds (maintained for backward compatibility)
dimension_thresholds:
  thumbnail: [150, 150]
  small: [512, 512]
  medium: [1024, 1024]
  large: [2048, 2048]

# Dataset Manager Settings
# -----------------------
split_ratios: [0.7, 0.2, 0.1]     # Train/validation/test split ratios
naming_pattern: "{category}_{index:06d}"  # File naming pattern
preserve_original_names: false    # Keep original filenames
copy_images: true                 # Copy images to dataset (vs. symlinks)
create_symlinks: false            # Create symlinks instead of copying
export_formats: ["csv", "json", "yaml"]  # Metadata export formats

# General Settings
# ---------------
resume: false                     # Resume interrupted processing
log_level: "INFO"                 # Logging level (DEBUG, INFO, WARNING, ERROR)
log_file: null                    # Optional log file path
