#!/usr/bin/env python3
"""
سكريبت تدريب YOLO للإنقاذ والبحث
YOLO Training Script for Search and Rescue
=========================================

سكريبت شامل لتدريب نماذج YOLO على بيانات الإنقاذ والبحث
مع دعم واجهة سطر الأوامر والواجهة الرسومية.
"""

import os
import sys
import argparse
import yaml
import logging
from pathlib import Path
from datetime import datetime

# إضافة مسار src إلى Python path
sys.path.insert(0, str(Path(__file__).parent / 'src'))

try:
    from src.rescue.yolo_training_module import YOLOTrainingManager
    from src.rescue.training_interface import YOLOTrainingGUI, CommandLineTrainingInterface
except ImportError as e:
    print(f"خطأ في استيراد الوحدات: {e}")
    print("تأكد من وجود جميع ملفات الوحدات في المسار الصحيح")
    sys.exit(1)


def create_default_training_config():
    """إنشاء ملف تكوين افتراضي للتدريب"""
    config = {
        # إعدادات النموذج
        'model_version': 'yolov8m',
        'pretrained': True,
        'image_size': 640,
        
        # إعدادات التدريب
        'epochs': 100,
        'batch_size': 16,
        'learning_rate': 0.01,
        'momentum': 0.937,
        'weight_decay': 0.0005,
        
        # إعدادات البيانات
        'dataset_path': './rescue_dataset',
        'augmentation': True,
        'mosaic': 1.0,
        'mixup': 0.0,
        'copy_paste': 0.0,
        
        # إعدادات الحفظ والتقييم
        'save_period': 10,
        'save_best': True,
        'save_last': True,
        'val_period': 1,
        'patience': 50,
        
        # إعدادات الأداء
        'workers': 8,
        'device': 'auto',
        'amp': True,
        
        # إعدادات التسجيل
        'project': 'rescue_yolo_training',
        'name': f'training_{datetime.now().strftime("%Y%m%d_%H%M%S")}',
        'exist_ok': True,
        'verbose': True,
        
        # إعدادات متخصصة للإنقاذ
        'rescue_classes': [
            'person',           # شخص
            'life_jacket',      # سترة نجاة
            'life_boat',        # قارب نجاة
            'debris',           # حطام
            'aircraft_wreckage', # حطام طائرة
            'vehicle_wreckage', # حطام مركبة
            'emergency_signal', # إشارة طوارئ
            'rescue_equipment'  # معدات إنقاذ
        ],
        
        'environment_types': [
            'sea',      # بحر
            'desert',   # صحراء
            'coast',    # ساحل
            'urban',    # حضري
            'forest',   # غابة
            'mountain'  # جبل
        ]
    }
    
    config_path = Path('yolo_training_config.yaml')
    with open(config_path, 'w', encoding='utf-8') as f:
        yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
    
    print(f"تم إنشاء ملف التكوين الافتراضي: {config_path}")
    return str(config_path)


def setup_logging(log_level='INFO', log_file=None):
    """إعداد نظام التسجيل"""
    log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    
    handlers = [logging.StreamHandler(sys.stdout)]
    
    if log_file:
        log_dir = Path(log_file).parent
        log_dir.mkdir(parents=True, exist_ok=True)
        handlers.append(logging.FileHandler(log_file, encoding='utf-8'))
    
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format=log_format,
        handlers=handlers
    )


def validate_dataset(dataset_path):
    """التحقق من صحة مجموعة البيانات"""
    dataset_dir = Path(dataset_path)
    
    if not dataset_dir.exists():
        raise FileNotFoundError(f"مجموعة البيانات غير موجودة: {dataset_path}")
    
    # البحث عن هيكل YOLO
    required_dirs = ['train/images', 'val/images']
    yolo_structure_found = False
    
    for structure_base in [dataset_dir, dataset_dir / 'yolo_dataset']:
        if all((structure_base / req_dir).exists() for req_dir in required_dirs):
            yolo_structure_found = True
            break
    
    if not yolo_structure_found:
        print("⚠️  تحذير: لم يتم العثور على هيكل YOLO القياسي")
        print("   سيتم محاولة إنشاء ملف data.yaml تلقائياً")
    
    return True


def run_gui_training():
    """تشغيل الواجهة الرسومية للتدريب"""
    try:
        print("🖥️  تشغيل الواجهة الرسومية لتدريب YOLO...")
        
        gui = YOLOTrainingGUI()
        gui.run()
        
    except ImportError:
        print("❌ الواجهة الرسومية غير متوفرة (tkinter مفقود)")
        print("   يرجى استخدام واجهة سطر الأوامر بدلاً من ذلك")
        return False
    except Exception as e:
        print(f"❌ خطأ في تشغيل الواجهة الرسومية: {e}")
        return False
    
    return True


def run_cli_training(config_file, dataset_path, **kwargs):
    """تشغيل التدريب من سطر الأوامر"""
    try:
        print("💻 تشغيل تدريب YOLO من سطر الأوامر...")
        
        # تحميل التكوين
        if config_file and Path(config_file).exists():
            with open(config_file, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            print(f"✅ تم تحميل التكوين من: {config_file}")
        else:
            config = {}
            print("⚠️  لم يتم العثور على ملف التكوين، سيتم استخدام الإعدادات الافتراضية")
        
        # تحديث التكوين بالمعاملات المرسلة
        if dataset_path:
            config['dataset_path'] = dataset_path
        
        config.update(kwargs)
        
        # التحقق من مجموعة البيانات
        dataset_path = config.get('dataset_path', './rescue_dataset')
        validate_dataset(dataset_path)
        
        # إنشاء واجهة سطر الأوامر
        cli_interface = CommandLineTrainingInterface()
        
        # تشغيل التدريب
        results = cli_interface.run_training(config_file, **config)
        
        # عرض النتائج
        print("\n🎉 اكتمل التدريب بنجاح!")
        print("=" * 50)
        
        if 'training_stats' in results:
            stats = results['training_stats']
            print(f"⏱️  مدة التدريب: {stats.get('training_duration', 0):.1f} ثانية")
            print(f"📊 أفضل mAP: {stats.get('best_map', 0):.3f}")
            print(f"📉 الخسارة النهائية: {stats.get('final_loss', 0):.4f}")
        
        if 'saved_models' in results:
            print(f"💾 النماذج المحفوظة: {len(results['saved_models'])}")
            for model_path in results['saved_models']:
                print(f"   - {model_path}")
        
        if 'training_paths' in results:
            paths = results['training_paths']
            print(f"\n📁 مجلدات النتائج:")
            print(f"   - الأوزان: {paths.get('weights_dir', 'غير محدد')}")
            print(f"   - النتائج: {paths.get('results_dir', 'غير محدد')}")
            print(f"   - السجلات: {paths.get('logs_dir', 'غير محدد')}")
        
        return True
        
    except Exception as e:
        print(f"❌ فشل في التدريب: {e}")
        return False


def main():
    """الدالة الرئيسية"""
    parser = argparse.ArgumentParser(
        description='تدريب نماذج YOLO للإنقاذ والبحث',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
أمثلة الاستخدام:
================

1. تشغيل الواجهة الرسومية:
   python train_yolo_rescue.py --gui

2. تدريب من سطر الأوامر:
   python train_yolo_rescue.py --dataset ./rescue_dataset --epochs 100

3. استخدام ملف تكوين:
   python train_yolo_rescue.py --config yolo_config.yaml

4. إنشاء ملف تكوين افتراضي:
   python train_yolo_rescue.py --create-config

5. تدريب مخصص:
   python train_yolo_rescue.py --dataset ./data --model yolov8l --epochs 200 --batch-size 32

الميزات المتخصصة:
==================
- دعم فئات الإنقاذ المتخصصة (أشخاص، سترات نجاة، حطام، إلخ)
- تحسين للبيئات المختلفة (بحر، صحراء، ساحل)
- مراقبة التقدم في الوقت الفعلي
- تصدير النماذج بتنسيقات متعددة
- تقييم شامل للأداء
        """
    )
    
    # المعاملات الأساسية
    parser.add_argument(
        '--gui',
        action='store_true',
        help='تشغيل الواجهة الرسومية'
    )
    
    parser.add_argument(
        '--dataset', '-d',
        type=str,
        help='مسار مجموعة البيانات'
    )
    
    parser.add_argument(
        '--config', '-c',
        type=str,
        help='مسار ملف التكوين'
    )
    
    parser.add_argument(
        '--create-config',
        action='store_true',
        help='إنشاء ملف تكوين افتراضي'
    )
    
    # معاملات النموذج
    parser.add_argument(
        '--model',
        type=str,
        choices=['yolov8n', 'yolov8s', 'yolov8m', 'yolov8l', 'yolov8x'],
        help='إصدار نموذج YOLO'
    )
    
    parser.add_argument(
        '--epochs',
        type=int,
        help='عدد حقب التدريب'
    )
    
    parser.add_argument(
        '--batch-size',
        type=int,
        help='حجم الدفعة'
    )
    
    parser.add_argument(
        '--learning-rate',
        type=float,
        help='معدل التعلم'
    )
    
    parser.add_argument(
        '--device',
        type=str,
        choices=['auto', 'cpu', 'cuda', 'mps'],
        help='الجهاز المستخدم للتدريب'
    )
    
    # معاملات التسجيل
    parser.add_argument(
        '--log-level',
        type=str,
        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
        default='INFO',
        help='مستوى التسجيل'
    )
    
    parser.add_argument(
        '--log-file',
        type=str,
        help='ملف السجل'
    )
    
    parser.add_argument(
        '--project',
        type=str,
        help='اسم المشروع'
    )
    
    parser.add_argument(
        '--name',
        type=str,
        help='اسم التدريب'
    )
    
    args = parser.parse_args()
    
    # إعداد التسجيل
    setup_logging(args.log_level, args.log_file)
    
    print("🚁 تدريب نماذج YOLO للإنقاذ والبحث")
    print("=" * 50)
    
    try:
        # إنشاء ملف تكوين افتراضي
        if args.create_config:
            create_default_training_config()
            return 0
        
        # تشغيل الواجهة الرسومية
        if args.gui:
            success = run_gui_training()
            return 0 if success else 1
        
        # التحقق من وجود مجموعة البيانات لسطر الأوامر
        if not args.dataset and not args.config:
            parser.error("يجب تحديد مجموعة البيانات أو ملف التكوين")
        
        # تجميع المعاملات الإضافية
        extra_params = {}
        if args.model:
            extra_params['model_version'] = args.model
        if args.epochs:
            extra_params['epochs'] = args.epochs
        if args.batch_size:
            extra_params['batch_size'] = args.batch_size
        if args.learning_rate:
            extra_params['learning_rate'] = args.learning_rate
        if args.device:
            extra_params['device'] = args.device
        if args.project:
            extra_params['project'] = args.project
        if args.name:
            extra_params['name'] = args.name
        
        # تشغيل التدريب من سطر الأوامر
        success = run_cli_training(args.config, args.dataset, **extra_params)
        return 0 if success else 1
        
    except KeyboardInterrupt:
        print("\n⚠️  تم إيقاف العملية بواسطة المستخدم")
        return 1
    except Exception as e:
        print(f"\n❌ خطأ عام: {e}")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
