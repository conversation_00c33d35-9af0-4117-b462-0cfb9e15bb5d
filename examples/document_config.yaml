# Document Processing Configuration
# Optimized for document and text image processing

# Scanner Settings
validate_mime_type: true

# Processor Settings - Optimized for documents
target_size: [224, 224]           # Standard size for document classification
maintain_aspect_ratio: true
convert_to_grayscale: true        # Documents often work better in grayscale
normalize: true
enhance_quality: true
batch_size: 64                    # Larger batch for efficiency
brightness_factor: 1.1            # Brighten documents
contrast_factor: 1.3              # High contrast for text clarity
sharpness_factor: 1.2             # Sharpen text
noise_reduction: true

# Classifier Settings - Document-specific
use_pretrained_model: true
model_name: "resnet50"
confidence_threshold: 0.4         # Lower threshold for document variety
custom_categories:
  - "invoices"
  - "receipts"
  - "contracts"
  - "forms"
  - "letters"
  - "reports"
  - "certificates"
  - "handwritten"
  - "printed"

# Size thresholds for documents
size_thresholds:
  small: 200000      # < 200KB
  medium: 1000000    # < 1MB
  large: 5000000     # < 5MB

# Dimension thresholds for documents
dimension_thresholds:
  thumbnail: [150, 150]
  small: [600, 800]     # Portrait orientation common
  medium: [1200, 1600]
  large: [2400, 3200]

# Dataset Manager Settings
split_ratios: [0.7, 0.2, 0.1]     # Standard split
naming_pattern: "doc_{category}_{index:06d}"
preserve_original_names: false
copy_images: true
create_symlinks: false
export_formats: ["csv", "json", "yaml"]

# General Settings
resume: false
log_level: "INFO"
