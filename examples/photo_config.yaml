# Photo Processing Configuration
# Optimized for personal photo collections

# Scanner Settings
validate_mime_type: true

# Processor Settings - Optimized for photos
target_size: [512, 512]           # Larger size for photo quality
maintain_aspect_ratio: true
convert_to_grayscale: false       # Keep photos in color
normalize: true
enhance_quality: true
batch_size: 16                    # Smaller batch for memory efficiency
brightness_factor: 1.05           # Slight brightness boost
contrast_factor: 1.1              # Enhance contrast
sharpness_factor: 1.05            # Slight sharpening
noise_reduction: true

# Classifier Settings - Photo-specific
use_pretrained_model: true
model_name: "resnet50"
confidence_threshold: 0.6         # Higher threshold for photos
custom_categories:
  - "portraits"
  - "landscapes"
  - "architecture"
  - "animals"
  - "food"
  - "events"
  - "travel"

# Size thresholds for photos
size_thresholds:
  small: 500000      # < 500KB
  medium: 2000000    # < 2MB
  large: 8000000     # < 8MB

# Dimension thresholds for photos
dimension_thresholds:
  thumbnail: [200, 200]
  small: [800, 600]
  medium: [1920, 1080]
  large: [4000, 3000]

# Dataset Manager Settings
split_ratios: [0.8, 0.15, 0.05]   # More training data for photos
naming_pattern: "photo_{category}_{index:06d}"
preserve_original_names: false
copy_images: true
create_symlinks: false
export_formats: ["csv", "json"]

# General Settings
resume: false
log_level: "INFO"
