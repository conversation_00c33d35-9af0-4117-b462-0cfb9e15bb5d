# Medical Image Processing Configuration
# Optimized for medical imaging datasets

# Scanner Settings
validate_mime_type: true

# Processor Settings - Medical imaging specific
target_size: [256, 256]           # Common size for medical images
maintain_aspect_ratio: true
convert_to_grayscale: true        # Many medical images are grayscale
normalize: true
enhance_quality: false            # Preserve original medical data
batch_size: 32
brightness_factor: 1.0            # No brightness adjustment
contrast_factor: 1.0              # No contrast adjustment
sharpness_factor: 1.0             # No sharpening
noise_reduction: false            # Preserve original data

# Classifier Settings - Medical specific
use_pretrained_model: false       # Often need specialized models
model_name: "resnet50"
confidence_threshold: 0.7         # High confidence for medical
custom_categories:
  - "xray_chest"
  - "xray_bone"
  - "ct_scan"
  - "mri"
  - "ultrasound"
  - "microscopy"
  - "dermatology"
  - "retinal"
  - "normal"
  - "abnormal"

# Size thresholds for medical images
size_thresholds:
  small: 100000      # < 100KB
  medium: 1000000    # < 1MB
  large: 10000000    # < 10MB (DICOM files can be large)

# Dimension thresholds for medical images
dimension_thresholds:
  thumbnail: [128, 128]
  small: [256, 256]
  medium: [512, 512]
  large: [1024, 1024]

# Dataset Manager Settings
split_ratios: [0.6, 0.2, 0.2]     # More validation/test for medical
naming_pattern: "med_{category}_{index:06d}"
preserve_original_names: true     # Important for medical traceability
copy_images: true
create_symlinks: false
export_formats: ["csv", "json", "yaml"]

# General Settings
resume: true                      # Important for large medical datasets
log_level: "DEBUG"                # Detailed logging for medical data
