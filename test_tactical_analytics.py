#!/usr/bin/env python3
"""
اختبار شامل لوحدة التحليلات التكتيكية
Comprehensive Test for Tactical Analytics Module
==============================================

سكريبت اختبار شامل لجميع مكونات التحليلات التكتيكية المتقدمة
للإنقاذ والبحث مع إنشاء بيانات تجريبية واقعية.
"""

import os
import sys
import json
import pandas as pd
import numpy as np
from pathlib import Path
from datetime import datetime, timedelta
import logging

# إضافة مسار src إلى Python path
sys.path.insert(0, str(Path(__file__).parent / 'src'))

# استيراد الوحدات المطلوبة
try:
    from src.rescue.tactical_analytics import TacticalRescueAnalytics
    from src.rescue.advanced_tactical_analytics import AdvancedTacticalAnalytics
    from src.rescue.tactical_insights_generator import TacticalInsightsGenerator
    from src.rescue.tactical_visualization import TacticalVisualizationEngine
except ImportError as e:
    print(f"خطأ في استيراد الوحدات: {e}")
    print("تأكد من وجود جميع ملفات الوحدات في المسار الصحيح")
    sys.exit(1)


class TacticalAnalyticsTestSuite:
    """
    مجموعة اختبارات شاملة للتحليلات التكتيكية
    Comprehensive test suite for tactical analytics
    """
    
    def __init__(self):
        """تهيئة مجموعة الاختبارات"""
        self.test_dir = Path('tactical_analytics_test')
        self.test_dir.mkdir(exist_ok=True)
        
        # إعداد التسجيل
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(self.test_dir / 'test_log.log', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
        # إعدادات الاختبار
        self.config = self._create_test_config()
        
        # نتائج الاختبارات
        self.test_results = {
            'total_tests': 0,
            'passed_tests': 0,
            'failed_tests': 0,
            'test_details': []
        }
    
    def _create_test_config(self) -> dict:
        """إنشاء تكوين للاختبار"""
        return {
            # إعدادات التحليل الأساسي
            'log_level': 'INFO',
            'figure_size': [12, 8],
            'dpi': 150,  # دقة أقل للاختبار
            'enable_statistical_analysis': True,
            'enable_heatmaps': True,
            'enable_interactive_plots': True,
            
            # إعدادات التحليل المتقدم
            'enable_spatial_clustering': True,
            'clustering_algorithm': 'dbscan',
            'cluster_eps': 0.1,
            'min_samples': 3,
            'enable_hotspot_detection': True,
            'enable_trend_analysis': True,
            'enable_seasonality_detection': True,
            
            # إعدادات الرؤى التكتيكية
            'insight_confidence_threshold': 0.6,
            'max_insights_per_category': 3,
            'enable_predictive_insights': True,
            
            # إعدادات التصور
            'enable_arabic_support': True,
            'export_formats': ['png', 'html'],
            'interactive_plots': True
        }
    
    def create_test_dataset(self) -> str:
        """إنشاء مجموعة بيانات تجريبية واقعية"""
        self.logger.info("إنشاء مجموعة بيانات تجريبية...")
        
        # إنشاء بيانات واقعية للاختبار
        np.random.seed(42)  # للحصول على نتائج قابلة للتكرار
        
        n_samples = 500
        
        # توليد البيانات الأساسية
        data = {
            'filename': [f'rescue_image_{i:04d}.jpg' for i in range(n_samples)],
            'file_path': [f'/test/images/rescue_image_{i:04d}.jpg' for i in range(n_samples)],
            'file_size_bytes': np.random.randint(1000000, 10000000, n_samples),
            'width': np.random.choice([1920, 1280, 800, 640], n_samples),
            'height': np.random.choice([1080, 720, 600, 480], n_samples),
            'total_pixels': [],
            'modification_time': []
        }
        
        # حساب إجمالي البكسلات
        data['total_pixels'] = [w * h for w, h in zip(data['width'], data['height'])]
        
        # توليد أوقات المسح الواقعية
        start_date = datetime.now() - timedelta(days=30)
        for i in range(n_samples):
            # توزيع غير منتظم للأوقات (محاكاة أنماط العمل الحقيقية)
            days_offset = np.random.exponential(5)  # توزيع أسي
            hours_offset = np.random.normal(12, 4)  # توزيع طبيعي حول منتصف النهار
            
            scan_time = start_date + timedelta(days=days_offset, hours=hours_offset)
            data['modification_time'].append(scan_time.isoformat())
        
        data['scan_timestamp'] = data['modification_time'].copy()
        data['classification_timestamp'] = [
            (datetime.fromisoformat(ts) + timedelta(minutes=np.random.randint(1, 30))).isoformat()
            for ts in data['scan_timestamp']
        ]
        
        # توليد تصنيفات البيئة الواقعية
        environments = ['sea', 'desert', 'coast', 'urban', 'forest', 'mountain']
        env_weights = [0.3, 0.25, 0.2, 0.15, 0.07, 0.03]  # توزيع واقعي
        data['environment_class'] = np.random.choice(environments, n_samples, p=env_weights)
        
        # توليد ثقة التصنيف (مرتبطة بالبيئة)
        env_confidence_map = {
            'sea': (0.85, 0.1),      # ثقة عالية، تباين منخفض
            'desert': (0.80, 0.12),
            'coast': (0.75, 0.15),
            'urban': (0.70, 0.18),
            'forest': (0.65, 0.20),
            'mountain': (0.60, 0.25)
        }
        
        data['environment_confidence'] = []
        for env in data['environment_class']:
            mean_conf, std_conf = env_confidence_map[env]
            confidence = np.clip(np.random.normal(mean_conf, std_conf), 0.1, 1.0)
            data['environment_confidence'].append(confidence)
        
        # توليد كشف الأهداف (مرتبط بالبيئة)
        target_probability_map = {
            'sea': 0.35,      # احتمالية عالية في البحر
            'coast': 0.30,
            'desert': 0.15,
            'urban': 0.25,
            'forest': 0.10,
            'mountain': 0.08
        }
        
        data['targets_detected'] = []
        data['total_detections'] = []
        data['unique_rescue_classes'] = []
        data['detection_confidence_avg'] = []
        
        rescue_classes = ['person', 'life_jacket', 'life_boat', 'debris', 'aircraft_wreckage', 'vehicle_wreckage', 'emergency_signal']
        
        for env in data['environment_class']:
            has_targets = np.random.random() < target_probability_map[env]
            data['targets_detected'].append(has_targets)
            
            if has_targets:
                # عدد الكشوفات
                num_detections = np.random.poisson(2) + 1  # على الأقل كشف واحد
                data['total_detections'].append(num_detections)
                
                # أنواع الأهداف المكتشفة
                detected_classes = np.random.choice(
                    rescue_classes, 
                    size=min(num_detections, len(rescue_classes)), 
                    replace=False
                ).tolist()
                data['unique_rescue_classes'].append(detected_classes)
                
                # ثقة الكشف
                detection_conf = np.clip(np.random.normal(0.75, 0.15), 0.3, 1.0)
                data['detection_confidence_avg'].append(detection_conf)
            else:
                data['total_detections'].append(0)
                data['unique_rescue_classes'].append([])
                data['detection_confidence_avg'].append(0.0)
        
        # توليد البيانات الجغرافية
        data['has_geospatial_data'] = np.random.choice([True, False], n_samples, p=[0.7, 0.3])
        data['coordinate_system'] = ['EPSG:4326'] * n_samples
        
        # توليد الحدود الجغرافية للصور التي تحتوي على بيانات جغرافية
        data['bounds'] = []
        for has_geo in data['has_geospatial_data']:
            if has_geo:
                # إحداثيات واقعية للمنطقة العربية
                center_lat = np.random.uniform(15, 35)  # من اليمن إلى تركيا
                center_lon = np.random.uniform(35, 60)  # من البحر المتوسط إلى الخليج
                
                # حدود الصورة (تقريباً 1 كم × 1 كم)
                delta = 0.01
                bounds = [
                    center_lon - delta, center_lat - delta,
                    center_lon + delta, center_lat + delta
                ]
                data['bounds'].append(str(bounds))
            else:
                data['bounds'].append('[]')
        
        # إضافة معلومات إضافية
        data['processing_time'] = np.random.exponential(2.0, n_samples)  # ثواني
        data['needs_human_verification'] = [
            conf < 0.6 or (targets and det_conf < 0.7)
            for conf, targets, det_conf in zip(
                data['environment_confidence'], 
                data['targets_detected'], 
                data['detection_confidence_avg']
            )
        ]
        
        # تصنيف الإنقاذ العام
        data['rescue_classification'] = [
            'has_targets' if targets else 'no_targets'
            for targets in data['targets_detected']
        ]
        
        # إنشاء DataFrame وحفظه
        df = pd.DataFrame(data)
        
        # حفظ البيانات بتنسيقات متعددة
        csv_path = self.test_dir / 'test_rescue_metadata.csv'
        json_path = self.test_dir / 'test_rescue_metadata.json'
        
        df.to_csv(csv_path, index=False, encoding='utf-8')
        df.to_json(json_path, orient='records', indent=2, force_ascii=False)
        
        self.logger.info(f"تم إنشاء مجموعة بيانات تجريبية: {n_samples} سجل")
        self.logger.info(f"ملف CSV: {csv_path}")
        self.logger.info(f"ملف JSON: {json_path}")
        
        return str(csv_path)
    
    def test_basic_tactical_analytics(self, dataset_path: str) -> bool:
        """اختبار التحليلات التكتيكية الأساسية"""
        try:
            self.logger.info("اختبار التحليلات التكتيكية الأساسية...")
            
            # إنشاء محلل التحليلات الأساسي
            basic_analyzer = TacticalRescueAnalytics(self.config)
            
            # تشغيل التحليل
            results = basic_analyzer.analyze_rescue_dataset(str(self.test_dir), dataset_path)
            
            # التحقق من النتائج
            assert 'basic_statistics' in results, "الإحصائيات الأساسية مفقودة"
            assert 'environment_analysis' in results, "تحليل البيئة مفقود"
            assert 'target_detection_analysis' in results, "تحليل كشف الأهداف مفقود"
            assert 'quality_analysis' in results, "تحليل الجودة مفقود"
            
            # حفظ النتائج
            results_path = self.test_dir / 'basic_analytics_results.json'
            with open(results_path, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2, default=str)
            
            self.logger.info("✅ نجح اختبار التحليلات الأساسية")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ فشل اختبار التحليلات الأساسية: {e}")
            return False
    
    def test_advanced_tactical_analytics(self, dataset_path: str) -> bool:
        """اختبار التحليلات التكتيكية المتقدمة"""
        try:
            self.logger.info("اختبار التحليلات التكتيكية المتقدمة...")
            
            # إنشاء محلل التحليلات المتقدم
            advanced_analyzer = AdvancedTacticalAnalytics(self.config)
            
            # تشغيل التحليل الشامل
            results = advanced_analyzer.perform_comprehensive_tactical_analysis(str(self.test_dir), dataset_path)
            
            # التحقق من النتائج
            assert 'spatial_analysis' in results, "التحليل المكاني مفقود"
            assert 'temporal_analysis' in results, "التحليل الزمني مفقود"
            assert 'efficiency_analysis' in results, "تحليل الكفاءة مفقود"
            
            # حفظ النتائج
            results_path = self.test_dir / 'advanced_analytics_results.json'
            with open(results_path, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2, default=str)
            
            self.logger.info("✅ نجح اختبار التحليلات المتقدمة")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ فشل اختبار التحليلات المتقدمة: {e}")
            return False
    
    def test_tactical_insights_generation(self, analysis_results: dict) -> bool:
        """اختبار توليد الرؤى التكتيكية"""
        try:
            self.logger.info("اختبار توليد الرؤى التكتيكية...")
            
            # إنشاء مولد الرؤى
            insights_generator = TacticalInsightsGenerator(self.config)
            
            # توليد الرؤى
            insights = insights_generator.generate_comprehensive_insights(analysis_results)
            
            # التحقق من النتائج
            assert isinstance(insights, list), "الرؤى يجب أن تكون قائمة"
            assert len(insights) > 0, "لم يتم توليد أي رؤى"
            
            # تصدير تقرير الرؤى
            insights_report = insights_generator.export_insights_report(str(self.test_dir))
            
            assert Path(insights_report).exists(), "تقرير الرؤى لم يتم إنشاؤه"
            
            self.logger.info(f"✅ نجح اختبار توليد الرؤى: {len(insights)} رؤية")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ فشل اختبار توليد الرؤى: {e}")
            return False
    
    def test_tactical_visualization(self, analysis_results: dict) -> bool:
        """اختبار التصور التكتيكي"""
        try:
            self.logger.info("اختبار التصور التكتيكي...")
            
            # إنشاء محرك التصور
            viz_engine = TacticalVisualizationEngine(self.config)
            
            # إنشاء لوحة التحكم
            dashboard_components = viz_engine.create_tactical_dashboard(
                analysis_results, 
                str(self.test_dir / 'visualizations')
            )
            
            # التحقق من النتائج
            assert isinstance(dashboard_components, dict), "مكونات لوحة التحكم يجب أن تكون قاموس"
            assert len(dashboard_components) > 0, "لم يتم إنشاء أي مكونات تصور"
            
            # التحقق من وجود الملفات
            viz_dir = self.test_dir / 'visualizations'
            assert viz_dir.exists(), "دليل التصورات لم يتم إنشاؤه"
            
            viz_files = list(viz_dir.glob('*'))
            assert len(viz_files) > 0, "لم يتم إنشاء أي ملفات تصور"
            
            self.logger.info(f"✅ نجح اختبار التصور: {len(dashboard_components)} مكون")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ فشل اختبار التصور: {e}")
            return False
    
    def test_comprehensive_report_generation(self, dataset_path: str) -> bool:
        """اختبار توليد التقرير الشامل"""
        try:
            self.logger.info("اختبار توليد التقرير الشامل...")
            
            # إنشاء محلل التحليلات الأساسي
            basic_analyzer = TacticalRescueAnalytics(self.config)
            
            # توليد التقرير الشامل
            report_info = basic_analyzer.generate_comprehensive_tactical_report(
                str(self.test_dir),
                str(self.test_dir / 'comprehensive_report'),
                dataset_path
            )
            
            # التحقق من النتائج
            assert 'comprehensive_report_path' in report_info, "مسار التقرير الشامل مفقود"
            assert 'executive_summary_path' in report_info, "مسار الملخص التنفيذي مفقود"
            
            # التحقق من وجود الملفات
            report_path = Path(report_info['comprehensive_report_path'])
            summary_path = Path(report_info['executive_summary_path'])
            
            assert report_path.exists(), "ملف التقرير الشامل غير موجود"
            assert summary_path.exists(), "ملف الملخص التنفيذي غير موجود"
            
            self.logger.info("✅ نجح اختبار توليد التقرير الشامل")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ فشل اختبار توليد التقرير الشامل: {e}")
            return False
    
    def run_all_tests(self) -> dict:
        """تشغيل جميع الاختبارات"""
        self.logger.info("🚀 بدء تشغيل جميع اختبارات التحليلات التكتيكية...")
        
        # إنشاء مجموعة البيانات التجريبية
        dataset_path = self.create_test_dataset()
        
        # قائمة الاختبارات
        tests = [
            ('التحليلات الأساسية', lambda: self.test_basic_tactical_analytics(dataset_path)),
            ('التحليلات المتقدمة', lambda: self.test_advanced_tactical_analytics(dataset_path)),
            ('توليد التقرير الشامل', lambda: self.test_comprehensive_report_generation(dataset_path))
        ]
        
        # تشغيل الاختبارات
        for test_name, test_func in tests:
            self.test_results['total_tests'] += 1
            
            try:
                success = test_func()
                if success:
                    self.test_results['passed_tests'] += 1
                    self.test_results['test_details'].append({
                        'test_name': test_name,
                        'status': 'passed',
                        'timestamp': datetime.now().isoformat()
                    })
                else:
                    self.test_results['failed_tests'] += 1
                    self.test_results['test_details'].append({
                        'test_name': test_name,
                        'status': 'failed',
                        'timestamp': datetime.now().isoformat()
                    })
            except Exception as e:
                self.test_results['failed_tests'] += 1
                self.test_results['test_details'].append({
                    'test_name': test_name,
                    'status': 'error',
                    'error': str(e),
                    'timestamp': datetime.now().isoformat()
                })
        
        # حفظ نتائج الاختبارات
        results_path = self.test_dir / 'test_results.json'
        with open(results_path, 'w', encoding='utf-8') as f:
            json.dump(self.test_results, f, ensure_ascii=False, indent=2, default=str)
        
        # طباعة النتائج
        self._print_test_summary()
        
        return self.test_results
    
    def _print_test_summary(self):
        """طباعة ملخص نتائج الاختبارات"""
        print("\n" + "="*60)
        print("📊 ملخص نتائج اختبارات التحليلات التكتيكية")
        print("="*60)
        print(f"إجمالي الاختبارات: {self.test_results['total_tests']}")
        print(f"✅ نجح: {self.test_results['passed_tests']}")
        print(f"❌ فشل: {self.test_results['failed_tests']}")
        print(f"📈 معدل النجاح: {self.test_results['passed_tests']/self.test_results['total_tests']*100:.1f}%")
        
        print("\n📋 تفاصيل الاختبارات:")
        for test_detail in self.test_results['test_details']:
            status_icon = "✅" if test_detail['status'] == 'passed' else "❌"
            print(f"{status_icon} {test_detail['test_name']}: {test_detail['status']}")
            if 'error' in test_detail:
                print(f"   خطأ: {test_detail['error']}")
        
        print(f"\n📁 ملفات الاختبار محفوظة في: {self.test_dir}")
        print("="*60)


def main():
    """الدالة الرئيسية للاختبار"""
    print("🧪 اختبار شامل لوحدة التحليلات التكتيكية للإنقاذ والبحث")
    print("="*70)
    
    try:
        # إنشاء وتشغيل مجموعة الاختبارات
        test_suite = TacticalAnalyticsTestSuite()
        results = test_suite.run_all_tests()
        
        # تحديد رمز الخروج
        exit_code = 0 if results['failed_tests'] == 0 else 1
        
        if exit_code == 0:
            print("\n🎉 جميع الاختبارات نجحت!")
        else:
            print(f"\n⚠️  فشل {results['failed_tests']} اختبار من أصل {results['total_tests']}")
        
        return exit_code
        
    except Exception as e:
        print(f"\n💥 خطأ في تشغيل الاختبارات: {e}")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
