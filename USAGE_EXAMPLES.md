# Usage Examples

This document provides detailed examples of how to use the Image Processing and Classification Application.

## Basic Examples

### 1. Simple Photo Organization

Organize a collection of personal photos:

```bash
python main.py --input ~/Pictures/vacation_photos --output ~/Datasets/vacation_organized
```

This will:
- Scan all images in the vacation_photos directory
- Process and enhance the images
- Classify them automatically
- Create train/val/test splits
- Generate comprehensive metadata

### 2. Document Processing

Process scanned documents with custom configuration:

```bash
python main.py \
  --input ~/Documents/scanned \
  --output ~/Datasets/documents \
  --config examples/document_config.yaml \
  --verbose
```

### 3. Medical Image Dataset

Create a medical image dataset with specific requirements:

```bash
python main.py \
  --input /data/medical_images \
  --output /data/processed_medical \
  --config examples/medical_config.yaml \
  --split-ratios 0.6 0.2 0.2
```

## Advanced Examples

### 4. Custom Categories and High-Quality Processing

```bash
# Create custom config for art classification
cat > art_config.yaml << EOF
custom_categories:
  - "paintings"
  - "sculptures"
  - "drawings"
  - "photography"
  - "digital_art"

target_size: [512, 512]
enhance_quality: true
brightness_factor: 1.05
contrast_factor: 1.1
confidence_threshold: 0.7
EOF

python main.py \
  --input ~/Art_Collection \
  --output ~/Datasets/art_classified \
  --config art_config.yaml
```

### 5. Large Dataset Processing with Resume

For very large datasets, use resume functionality:

```bash
# Start processing
python main.py \
  --input /large_dataset \
  --output /processed_dataset \
  --resume

# If interrupted, resume with the same command
python main.py \
  --input /large_dataset \
  --output /processed_dataset \
  --resume
```

### 6. Memory-Efficient Processing

For systems with limited memory:

```bash
cat > memory_efficient_config.yaml << EOF
batch_size: 8
target_size: [224, 224]
enhance_quality: false
create_symlinks: true
copy_images: false
EOF

python main.py \
  --input /large_image_collection \
  --output /processed_collection \
  --config memory_efficient_config.yaml
```

## Configuration Examples

### 7. High-Quality Photo Processing

```yaml
# high_quality_photos.yaml
target_size: [1024, 1024]
maintain_aspect_ratio: true
enhance_quality: true
brightness_factor: 1.02
contrast_factor: 1.05
sharpness_factor: 1.03
noise_reduction: true
confidence_threshold: 0.8

custom_categories:
  - "portraits"
  - "landscapes"
  - "macro"
  - "street_photography"
  - "architecture"

split_ratios: [0.8, 0.15, 0.05]
naming_pattern: "hq_{category}_{index:06d}"
```

### 8. Scientific Image Analysis

```yaml
# scientific_config.yaml
target_size: [256, 256]
convert_to_grayscale: true
normalize: true
enhance_quality: false  # Preserve original data
brightness_factor: 1.0
contrast_factor: 1.0
sharpness_factor: 1.0

custom_categories:
  - "microscopy"
  - "astronomy"
  - "geology"
  - "biology"
  - "chemistry"

confidence_threshold: 0.9
preserve_original_names: true
export_formats: ["csv", "json", "yaml"]
```

## Programmatic Usage

### 9. Using the API Directly

```python
from pathlib import Path
from src.utils.config_manager import ConfigManager
from src.core.scanner import ImageScanner
from src.core.processor import ImageProcessor
from src.core.classifier import ImageClassifier
from src.core.dataset_manager import DatasetManager

# Load configuration
config_manager = ConfigManager('config.yaml')
config = config_manager.load_config()

# Customize configuration
config.update({
    'target_size': [384, 384],
    'custom_categories': ['cats', 'dogs', 'birds'],
    'confidence_threshold': 0.75
})

# Initialize components
scanner = ImageScanner(config)
processor = ImageProcessor(config)
classifier = ImageClassifier(config)
dataset_manager = DatasetManager(config)

# Process pipeline
input_path = Path('input_images')
output_path = Path('output_dataset')

print("Scanning images...")
image_files = scanner.scan_directory(input_path)

print("Processing images...")
processed_data = processor.process_images(image_files)

print("Classifying images...")
classified_data = classifier.classify_images(processed_data)

print("Creating dataset...")
dataset_manager.create_dataset(classified_data, output_path)

print("Pipeline completed!")
```

### 10. Custom Classification Logic

```python
from src.core.classifier import ImageClassifier

class CustomImageClassifier(ImageClassifier):
    def _classify_custom_categories(self, data):
        """Custom classification logic"""
        custom_classification = {}
        filename = data['filename'].lower()
        
        # Custom rules based on filename patterns
        if 'cat' in filename or 'kitten' in filename:
            custom_classification['custom_cats_score'] = 0.9
        elif 'dog' in filename or 'puppy' in filename:
            custom_classification['custom_dogs_score'] = 0.9
        elif 'bird' in filename:
            custom_classification['custom_birds_score'] = 0.8
        
        return custom_classification

# Use custom classifier
config['custom_categories'] = ['cats', 'dogs', 'birds']
custom_classifier = CustomImageClassifier(config)
```

## Output Analysis Examples

### 11. Analyzing Results

```python
import pandas as pd
import json

# Load metadata
with open('output_dataset/metadata.json', 'r') as f:
    metadata = json.load(f)

df = pd.DataFrame(metadata)

# Analyze category distribution
print("Category Distribution:")
print(df['primary_category'].value_counts())

# Analyze confidence scores
print("\nConfidence Statistics:")
print(df['prediction_confidence'].describe())

# Find low-confidence predictions
low_confidence = df[df['prediction_confidence'] < 0.5]
print(f"\nLow confidence predictions: {len(low_confidence)}")

# Analyze by split
print("\nSplit Distribution:")
print(df['split'].value_counts())
```

### 12. Quality Assessment

```python
# Load dataset manifest
with open('output_dataset/dataset_manifest.json', 'r') as f:
    manifest = json.load(f)

print("Dataset Quality Report:")
print(f"Total Images: {manifest['dataset_info']['total_images']}")
print(f"Categories: {len(manifest['dataset_info']['categories'])}")
print(f"Balance Ratio: {manifest['statistics'].get('balance_ratio', 'N/A')}")
print(f"Average File Size: {manifest['statistics']['size_statistics']['mean_mb']:.2f} MB")
```

## Troubleshooting Examples

### 13. Debugging Configuration Issues

```bash
# Test configuration validity
python -c "
from src.utils.config_manager import ConfigManager
try:
    config = ConfigManager('my_config.yaml').load_config()
    print('Configuration is valid!')
    print(f'Target size: {config[\"target_size\"]}')
    print(f'Split ratios: {config[\"split_ratios\"]}')
except Exception as e:
    print(f'Configuration error: {e}')
"
```

### 14. Memory Usage Monitoring

```python
import psutil
import os

def monitor_memory():
    process = psutil.Process(os.getpid())
    memory_mb = process.memory_info().rss / 1024 / 1024
    print(f"Memory usage: {memory_mb:.1f} MB")

# Add this to your processing loop
for i, batch in enumerate(batches):
    process_batch(batch)
    if i % 10 == 0:
        monitor_memory()
```

### 15. Performance Optimization

```bash
# Profile the application
python -m cProfile -o profile_output.prof main.py --input test_images --output test_output

# Analyze profile
python -c "
import pstats
p = pstats.Stats('profile_output.prof')
p.sort_stats('cumulative').print_stats(20)
"
```

These examples should help you get started with various use cases and configurations of the image processing application.
