# How to Run the GUI - Complete Instructions

## 🎯 **Quick Start Guide**

### **Step 1: Install GUI Dependencies**

The GUI requires tkinter, which is usually included with Python but may need separate installation on some systems.

#### **Ubuntu/Debian:**
```bash
sudo apt-get update
sudo apt-get install python3-tk
```

#### **CentOS/RHEL/Fedora:**
```bash
# CentOS/RHEL
sudo yum install tkinter
# or for newer versions
sudo dnf install python3-tkinter

# Fedora
sudo dnf install python3-tkinter
```

#### **macOS:**
```bash
# If using Homebrew
brew install python-tk

# tkinter is usually included with Python from python.org
```

#### **Windows:**
```bash
# tkinter is included with standard Python installations
# No additional installation needed
```

### **Step 2: Verify Installation**

Test if tkinter is properly installed:
```bash
python3 -c "import tkinter; print('✓ tkinter is available!')"
```

### **Step 3: Launch the GUI**

#### **Method 1: Smart Launcher (Recommended)**
```bash
python3 launch_gui.py
```

The smart launcher will:
- ✅ Check Python version compatibility
- ✅ Verify all dependencies are installed
- ✅ Offer to install missing packages automatically
- ✅ Launch the GUI with comprehensive error handling

#### **Method 2: Direct Launch**
```bash
python3 gui_main.py
```

#### **Method 3: Make Executable**
```bash
chmod +x launch_gui.py
./launch_gui.py
```

## 🎨 **GUI Interface Overview**

### **Main Processing Tab**
The primary interface for processing images:

```
┌─────────────────────────────────────────────────────────────┐
│                Advanced Image Processing GUI                │
├─────────────────────────────────────────────────────────────┤
│ [Main] [Configuration] [Advanced] [Logs]                   │
├─────────────────────────────────────────────────────────────┤
│ Input/Output Settings:                                      │
│ • Input Directory: Browse and select image folder          │
│ • Output Directory: Choose where to save results           │
│ • Config File: Load saved configuration                    │
│                                                             │
│ Quick Settings:                                             │
│ • Target Size: Set output image dimensions                 │
│ • Split Ratios: Configure train/val/test percentages       │
│ • Processing Options: Quality, normalization, grayscale    │
│                                                             │
│ Progress Monitoring:                                        │
│ • Real-time progress bar                                   │
│ • Current processing status                                │
│ • Live statistics display                                  │
│                                                             │
│ [Start Processing] [Stop] [Clear Log] [Save Config]        │
└─────────────────────────────────────────────────────────────┘
```

### **Configuration Tab**
Advanced settings for fine-tuning:

- **Processing Settings**: Batch size, enhancement factors
- **Classification Settings**: Confidence threshold, custom categories
- **Quality Controls**: Brightness, contrast, sharpness adjustments

### **Advanced Tab**
Power user features:

- **Export Settings**: Choose metadata formats (CSV, JSON, YAML)
- **Dataset Settings**: Naming patterns, file handling options
- **Preset Configurations**: Quick-load optimized settings

### **Logs Tab**
Real-time monitoring:

- **Live Logs**: Processing events with timestamps
- **Error Tracking**: Detailed error messages
- **Log Management**: Save and export logs

## 🚀 **Step-by-Step Usage**

### **Basic Image Processing Workflow**

1. **Launch GUI**:
   ```bash
   python3 launch_gui.py
   ```

2. **Select Input Directory**:
   - Click "Browse" next to "Input Directory"
   - Navigate to your folder containing images
   - Click "Select Folder"

3. **Choose Output Directory**:
   - Click "Browse" next to "Output Directory"
   - Select where to save the processed dataset
   - Click "Select Folder"

4. **Configure Settings** (optional):
   - Adjust target image size (default: 224x224)
   - Set split ratios (default: 70% train, 20% val, 10% test)
   - Enable/disable processing options

5. **Start Processing**:
   - Click "Start Processing"
   - Monitor progress in real-time
   - View statistics as processing completes

6. **Review Results**:
   - Check the output directory for organized dataset
   - Review processing logs for any issues
   - Examine generated metadata files

### **Advanced Configuration Workflow**

1. **Switch to Configuration Tab**:
   - Click on "Configuration" tab

2. **Adjust Processing Settings**:
   - Set batch size based on available memory
   - Fine-tune enhancement factors:
     - Brightness Factor: 1.0 = no change, >1.0 = brighter
     - Contrast Factor: 1.0 = no change, >1.0 = more contrast
     - Sharpness Factor: 1.0 = no change, >1.0 = sharper

3. **Configure Classification**:
   - Set confidence threshold (0.0-1.0)
   - Add custom categories (one per line):
     ```
     portraits
     landscapes
     architecture
     animals
     food
     ```

4. **Use Advanced Features**:
   - Switch to "Advanced" tab
   - Choose export formats
   - Configure dataset options
   - Load preset configurations

### **Using Preset Configurations**

The GUI includes three optimized presets:

#### **Photo Processing Preset**
```bash
# Optimized for personal photos
- Target Size: 512x512 (higher quality)
- Enhanced quality settings
- Photo-specific categories
- Color preservation
```

#### **Document Processing Preset**
```bash
# Optimized for scanned documents
- Grayscale conversion enabled
- High contrast enhancement
- Document-specific categories
- Text clarity optimization
```

#### **Medical Imaging Preset**
```bash
# Optimized for medical images
- Preserves original data integrity
- No quality enhancements
- Medical imaging categories
- High confidence thresholds
```

To use a preset:
1. Go to "Advanced" tab
2. Click the appropriate preset button
3. Settings are automatically loaded
4. Customize further if needed

## 🔧 **Troubleshooting**

### **Common Issues and Solutions**

#### **"No module named 'tkinter'" Error**
```bash
# Ubuntu/Debian
sudo apt-get install python3-tk

# CentOS/RHEL
sudo dnf install python3-tkinter

# macOS
brew install python-tk
```

#### **GUI Appears but Crashes**
```bash
# Check all dependencies
python3 -c "
import sys
sys.path.insert(0, 'src')
from src.utils.config_manager import ConfigManager
print('✓ All imports successful')
"
```

#### **"Permission denied" Errors**
```bash
chmod +x launch_gui.py
chmod +x gui_main.py
```

#### **GUI is Slow or Unresponsive**
- Reduce batch size in Configuration tab
- Close other applications
- Use smaller target image sizes
- Disable quality enhancement for faster processing

### **Performance Optimization**

#### **For Large Datasets**
- Set batch size to 8-16 (instead of default 32)
- Enable "Create Symlinks" to save disk space
- Process in smaller chunks if needed

#### **For Limited Memory**
- Reduce batch size to 4-8
- Use smaller target image sizes (e.g., 128x128)
- Close other applications during processing

#### **For Maximum Quality**
- Increase target size to 512x512 or higher
- Enable all quality enhancements
- Use higher confidence thresholds

## 🎯 **GUI vs Command Line**

### **When to Use GUI**
- ✅ First-time users or occasional use
- ✅ Visual feedback and progress monitoring needed
- ✅ Interactive configuration preferred
- ✅ Real-time error handling desired

### **When to Use Command Line**
- ✅ Automated scripts and batch processing
- ✅ Server environments without display
- ✅ Integration with other tools
- ✅ Reproducible processing workflows

### **Feature Comparison**

| Feature | GUI | Command Line |
|---------|-----|--------------|
| Ease of Use | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ |
| Visual Feedback | ⭐⭐⭐⭐⭐ | ⭐⭐ |
| Automation | ⭐⭐ | ⭐⭐⭐⭐⭐ |
| Configuration | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ |
| Error Handling | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ |
| Performance | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ |

## 🎉 **Success Indicators**

When the GUI is working correctly, you should see:

✅ **Startup**: Clean launch without error messages
✅ **Interface**: All tabs and controls visible and responsive
✅ **Directory Selection**: Browse buttons open file dialogs
✅ **Processing**: Progress bars update in real-time
✅ **Logging**: Messages appear in the Logs tab
✅ **Output**: Organized dataset created successfully

## 📞 **Getting Help**

If you encounter issues:

1. **Check the Logs tab** for detailed error messages
2. **Review the GUI_GUIDE.md** for comprehensive usage instructions
3. **Run the test script**: `python3 test_application.py`
4. **Verify dependencies**: Use the smart launcher for automatic checking

The GUI provides the same powerful functionality as the command-line interface but with an intuitive, user-friendly visual interface! 🚀
