#!/usr/bin/env python3
"""
اختبار مبسط للتحليلات التكتيكية
Simple Test for Tactical Analytics
=================================

اختبار أساسي للتأكد من صحة هيكل الكود والوظائف الأساسية
بدون الاعتماد على مكتبات خارجية معقدة.
"""

import os
import sys
import json
from pathlib import Path
from datetime import datetime
import logging

# إعداد التسجيل البسيط
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

def test_module_imports():
    """اختبار استيراد الوحدات"""
    logger.info("اختبار استيراد الوحدات...")
    
    try:
        # اختبار استيراد الوحدات الأساسية
        sys.path.insert(0, str(Path(__file__).parent / 'src'))
        
        # محاولة استيراد الوحدات
        modules_to_test = [
            'src.rescue.tactical_analytics',
            'src.rescue.advanced_tactical_analytics', 
            'src.rescue.tactical_insights_generator',
            'src.rescue.tactical_visualization'
        ]
        
        imported_modules = []
        failed_imports = []
        
        for module_name in modules_to_test:
            try:
                __import__(module_name)
                imported_modules.append(module_name)
                logger.info(f"✅ نجح استيراد: {module_name}")
            except ImportError as e:
                failed_imports.append((module_name, str(e)))
                logger.warning(f"⚠️  فشل استيراد: {module_name} - {e}")
        
        return {
            'total_modules': len(modules_to_test),
            'successful_imports': len(imported_modules),
            'failed_imports': len(failed_imports),
            'imported_modules': imported_modules,
            'failed_details': failed_imports
        }
        
    except Exception as e:
        logger.error(f"خطأ في اختبار الاستيراد: {e}")
        return {'error': str(e)}

def test_class_instantiation():
    """اختبار إنشاء كائنات الفئات"""
    logger.info("اختبار إنشاء كائنات الفئات...")
    
    try:
        # تكوين اختبار بسيط
        test_config = {
            'log_level': 'INFO',
            'enable_statistical_analysis': True,
            'figure_size': [10, 6],
            'dpi': 100
        }
        
        instantiation_results = {}
        
        # اختبار TacticalRescueAnalytics
        try:
            from src.rescue.tactical_analytics import TacticalRescueAnalytics
            analytics = TacticalRescueAnalytics(test_config)
            instantiation_results['TacticalRescueAnalytics'] = 'success'
            logger.info("✅ نجح إنشاء TacticalRescueAnalytics")
        except Exception as e:
            instantiation_results['TacticalRescueAnalytics'] = f'failed: {e}'
            logger.warning(f"⚠️  فشل إنشاء TacticalRescueAnalytics: {e}")
        
        # اختبار AdvancedTacticalAnalytics
        try:
            from src.rescue.advanced_tactical_analytics import AdvancedTacticalAnalytics
            advanced_analytics = AdvancedTacticalAnalytics(test_config)
            instantiation_results['AdvancedTacticalAnalytics'] = 'success'
            logger.info("✅ نجح إنشاء AdvancedTacticalAnalytics")
        except Exception as e:
            instantiation_results['AdvancedTacticalAnalytics'] = f'failed: {e}'
            logger.warning(f"⚠️  فشل إنشاء AdvancedTacticalAnalytics: {e}")
        
        # اختبار TacticalInsightsGenerator
        try:
            from src.rescue.tactical_insights_generator import TacticalInsightsGenerator
            insights_gen = TacticalInsightsGenerator(test_config)
            instantiation_results['TacticalInsightsGenerator'] = 'success'
            logger.info("✅ نجح إنشاء TacticalInsightsGenerator")
        except Exception as e:
            instantiation_results['TacticalInsightsGenerator'] = f'failed: {e}'
            logger.warning(f"⚠️  فشل إنشاء TacticalInsightsGenerator: {e}")
        
        # اختبار TacticalVisualizationEngine
        try:
            from src.rescue.tactical_visualization import TacticalVisualizationEngine
            viz_engine = TacticalVisualizationEngine(test_config)
            instantiation_results['TacticalVisualizationEngine'] = 'success'
            logger.info("✅ نجح إنشاء TacticalVisualizationEngine")
        except Exception as e:
            instantiation_results['TacticalVisualizationEngine'] = f'failed: {e}'
            logger.warning(f"⚠️  فشل إنشاء TacticalVisualizationEngine: {e}")
        
        return instantiation_results
        
    except Exception as e:
        logger.error(f"خطأ في اختبار إنشاء الكائنات: {e}")
        return {'error': str(e)}

def test_basic_functionality():
    """اختبار الوظائف الأساسية"""
    logger.info("اختبار الوظائف الأساسية...")
    
    try:
        # إنشاء بيانات اختبار بسيطة
        test_data = {
            'basic_statistics': {
                'total_images': 100,
                'images_with_targets': 30,
                'unique_environments': 4,
                'total_dataset_size_gb': 2.5
            },
            'environment_analysis': {
                'distribution_counts': {
                    'sea': 40,
                    'desert': 30,
                    'coast': 20,
                    'urban': 10
                },
                'most_common_environment': 'sea'
            },
            'target_detection_analysis': {
                'detection_rate_percentage': 30.0,
                'total_detections_across_dataset': 45
            }
        }
        
        functionality_results = {}
        
        # اختبار معالجة البيانات الأساسية
        try:
            from src.rescue.tactical_analytics import TacticalRescueAnalytics
            analytics = TacticalRescueAnalytics({'log_level': 'INFO'})
            
            # اختبار دالة توليد الملخص التنفيذي
            summary = analytics._generate_executive_summary(test_data)
            
            if isinstance(summary, dict) and 'dataset_overview' in summary:
                functionality_results['executive_summary_generation'] = 'success'
                logger.info("✅ نجح توليد الملخص التنفيذي")
            else:
                functionality_results['executive_summary_generation'] = 'failed: invalid output'
                
        except Exception as e:
            functionality_results['executive_summary_generation'] = f'failed: {e}'
            logger.warning(f"⚠️  فشل توليد الملخص التنفيذي: {e}")
        
        # اختبار توليد التوصيات
        try:
            from src.rescue.tactical_analytics import TacticalRescueAnalytics
            analytics = TacticalRescueAnalytics({'log_level': 'INFO'})
            
            recommendations = analytics._generate_tactical_recommendations(test_data)
            
            if isinstance(recommendations, list):
                functionality_results['recommendations_generation'] = 'success'
                logger.info("✅ نجح توليد التوصيات")
            else:
                functionality_results['recommendations_generation'] = 'failed: invalid output'
                
        except Exception as e:
            functionality_results['recommendations_generation'] = f'failed: {e}'
            logger.warning(f"⚠️  فشل توليد التوصيات: {e}")
        
        # اختبار تقييم جاهزية البيانات
        try:
            from src.rescue.tactical_analytics import TacticalRescueAnalytics
            analytics = TacticalRescueAnalytics({'log_level': 'INFO'})
            
            readiness = analytics._assess_data_readiness(test_data)
            
            if isinstance(readiness, dict) and 'overall_readiness' in readiness:
                functionality_results['data_readiness_assessment'] = 'success'
                logger.info("✅ نجح تقييم جاهزية البيانات")
            else:
                functionality_results['data_readiness_assessment'] = 'failed: invalid output'
                
        except Exception as e:
            functionality_results['data_readiness_assessment'] = f'failed: {e}'
            logger.warning(f"⚠️  فشل تقييم جاهزية البيانات: {e}")
        
        return functionality_results
        
    except Exception as e:
        logger.error(f"خطأ في اختبار الوظائف الأساسية: {e}")
        return {'error': str(e)}

def test_data_structures():
    """اختبار هياكل البيانات"""
    logger.info("اختبار هياكل البيانات...")
    
    try:
        structure_results = {}
        
        # اختبار TacticalInsight
        try:
            from src.rescue.tactical_insights_generator import TacticalInsight
            
            insight = TacticalInsight(
                id='test_001',
                category='operational',
                priority='high',
                title='اختبار الرؤية التكتيكية',
                description='هذه رؤية تكتيكية للاختبار',
                confidence=0.85,
                supporting_data={'test': 'data'},
                recommendations=['توصية 1', 'توصية 2'],
                impact_assessment={'operational': 'high'},
                timeline='immediate',
                stakeholders=['فريق الاختبار'],
                timestamp=datetime.now().isoformat()
            )
            
            # اختبار تحويل إلى قاموس
            insight_dict = insight.to_dict()
            
            if isinstance(insight_dict, dict) and 'id' in insight_dict:
                structure_results['TacticalInsight'] = 'success'
                logger.info("✅ نجح اختبار TacticalInsight")
            else:
                structure_results['TacticalInsight'] = 'failed: invalid conversion'
                
        except Exception as e:
            structure_results['TacticalInsight'] = f'failed: {e}'
            logger.warning(f"⚠️  فشل اختبار TacticalInsight: {e}")
        
        return structure_results
        
    except Exception as e:
        logger.error(f"خطأ في اختبار هياكل البيانات: {e}")
        return {'error': str(e)}

def run_comprehensive_test():
    """تشغيل اختبار شامل مبسط"""
    logger.info("🚀 بدء الاختبار الشامل المبسط للتحليلات التكتيكية")
    print("="*70)
    
    test_results = {
        'test_timestamp': datetime.now().isoformat(),
        'total_test_categories': 0,
        'successful_categories': 0,
        'failed_categories': 0,
        'detailed_results': {}
    }
    
    # قائمة الاختبارات
    tests = [
        ('استيراد الوحدات', test_module_imports),
        ('إنشاء الكائنات', test_class_instantiation),
        ('الوظائف الأساسية', test_basic_functionality),
        ('هياكل البيانات', test_data_structures)
    ]
    
    # تشغيل الاختبارات
    for test_name, test_func in tests:
        test_results['total_test_categories'] += 1
        
        logger.info(f"\n📋 تشغيل اختبار: {test_name}")
        print("-" * 50)
        
        try:
            result = test_func()
            test_results['detailed_results'][test_name] = result
            
            # تحديد نجاح أو فشل الاختبار
            if isinstance(result, dict) and 'error' not in result:
                # فحص النتائج الفرعية
                if any('failed' in str(v) for v in result.values()):
                    test_results['failed_categories'] += 1
                    logger.warning(f"⚠️  اختبار {test_name}: نجح جزئياً")
                else:
                    test_results['successful_categories'] += 1
                    logger.info(f"✅ اختبار {test_name}: نجح كاملاً")
            else:
                test_results['failed_categories'] += 1
                logger.error(f"❌ اختبار {test_name}: فشل")
                
        except Exception as e:
            test_results['failed_categories'] += 1
            test_results['detailed_results'][test_name] = {'error': str(e)}
            logger.error(f"💥 اختبار {test_name}: خطأ - {e}")
    
    # حفظ النتائج
    results_file = Path('tactical_analytics_test_results.json')
    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump(test_results, f, ensure_ascii=False, indent=2, default=str)
    
    # طباعة الملخص النهائي
    print("\n" + "="*70)
    print("📊 ملخص نتائج الاختبار الشامل")
    print("="*70)
    print(f"إجمالي فئات الاختبار: {test_results['total_test_categories']}")
    print(f"✅ نجح كاملاً: {test_results['successful_categories']}")
    print(f"❌ فشل أو نجح جزئياً: {test_results['failed_categories']}")
    
    success_rate = (test_results['successful_categories'] / test_results['total_test_categories']) * 100
    print(f"📈 معدل النجاح: {success_rate:.1f}%")
    
    print(f"\n📁 النتائج التفصيلية محفوظة في: {results_file}")
    
    # تحديد حالة النجاح العامة
    if test_results['failed_categories'] == 0:
        print("\n🎉 جميع الاختبارات نجحت! التحليلات التكتيكية جاهزة للاستخدام.")
        return 0
    elif test_results['successful_categories'] > 0:
        print("\n⚠️  بعض الاختبارات نجحت. قد تحتاج لتثبيت مكتبات إضافية للوظائف المتقدمة.")
        return 1
    else:
        print("\n❌ فشلت معظم الاختبارات. يرجى مراجعة الأخطاء وإصلاحها.")
        return 2

def main():
    """الدالة الرئيسية"""
    try:
        return run_comprehensive_test()
    except Exception as e:
        logger.error(f"💥 خطأ عام في الاختبار: {e}")
        return 3

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
