- absolute_path: stress_test/large.jpg
  aspect_ratio: 1.33
  channels: 3
  dataset_filename: size_extra_large_000000.jpg
  dataset_path: test/size_extra_large/size_extra_large_000000.jpg
  dimension_category: extra_large
  directory: .
  enhanced: true
  filename: large.jpg
  format: jpg
  high_confidence: false
  normalized: true
  orientation: landscape
  original_height: 3000
  original_path: large.jpg
  original_width: 4000
  predicted_class: parking_meter
  prediction_confidence: 0.024884405591801485
  primary_category: size_extra_large
  processed: true
  size_bytes: 9059333
  size_category: extra_large
  split: test
- absolute_path: stress_test/medium_square.jpg
  aspect_ratio: 1.0
  channels: 3
  dataset_filename: size_large_000001.jpg
  dataset_path: train/size_large/size_large_000001.jpg
  dimension_category: large
  directory: .
  enhanced: true
  filename: medium_square.jpg
  format: jpg
  high_confidence: false
  normalized: true
  orientation: square
  original_height: 500
  original_path: medium_square.jpg
  original_width: 500
  predicted_class: parking_meter
  prediction_confidence: 0.024884405591801485
  primary_category: size_large
  processed: true
  size_bytes: 192826
  size_category: large
  split: train
- absolute_path: stress_test/small_square.png
  aspect_ratio: 1.0
  channels: 3
  dataset_filename: size_large_000002.png
  dataset_path: train/size_large/size_large_000002.png
  dimension_category: large
  directory: .
  enhanced: true
  filename: small_square.png
  format: png
  high_confidence: false
  normalized: true
  orientation: square
  original_height: 50
  original_path: small_square.png
  original_width: 50
  predicted_class: parking_meter
  prediction_confidence: 0.024884405591801485
  primary_category: size_large
  processed: true
  size_bytes: 7618
  size_category: large
  split: train
- absolute_path: stress_test/tall.jpg
  aspect_ratio: 0.1
  channels: 3
  dataset_filename: size_large_000000.jpg
  dataset_path: train/size_large/size_large_000000.jpg
  dimension_category: large
  directory: .
  enhanced: true
  filename: tall.jpg
  format: jpg
  high_confidence: false
  normalized: true
  orientation: portrait
  original_height: 1000
  original_path: tall.jpg
  original_width: 100
  predicted_class: parking_meter
  prediction_confidence: 0.024884405591801485
  primary_category: size_large
  processed: true
  size_bytes: 79986
  size_category: large
  split: train
- absolute_path: stress_test/tiny.png
  aspect_ratio: 1.0
  channels: 3
  dataset_filename: size_large_000000.png
  dataset_path: test/size_large/size_large_000000.png
  dimension_category: large
  directory: .
  enhanced: true
  filename: tiny.png
  format: png
  high_confidence: false
  normalized: true
  orientation: square
  original_height: 1
  original_path: tiny.png
  original_width: 1
  predicted_class: parking_meter
  prediction_confidence: 0.024884405591801485
  primary_category: size_large
  processed: true
  size_bytes: 69
  size_category: large
  split: test
- absolute_path: stress_test/wide.png
  aspect_ratio: 10.0
  channels: 3
  dataset_filename: size_large_000000.png
  dataset_path: val/size_large/size_large_000000.png
  dimension_category: large
  directory: .
  enhanced: true
  filename: wide.png
  format: png
  high_confidence: false
  normalized: true
  orientation: landscape
  original_height: 100
  original_path: wide.png
  original_width: 1000
  predicted_class: parking_meter
  prediction_confidence: 0.024884405591801485
  primary_category: size_large
  processed: true
  size_bytes: 300542
  size_category: large
  split: val
