{"dataset_info": {"total_images": 6, "categories": ["size_extra_large", "size_large"], "splits": {"train": 3, "val": 1, "test": 2}, "split_ratios": [0.7, 0.2, 0.1]}, "statistics": {"category_distribution": {"size_extra_large": 1, "size_large": 5}, "format_distribution": {"jpg": 3, "png": 3}, "size_statistics": {"min": 69, "max": 9059333, "mean": 1606729.0, "total": 9640374}, "dimension_statistics": {"width": {"min": 1, "max": 4000, "mean": 941.8333333333334}, "height": {"min": 1, "max": 3000, "mean": 775.1666666666666}}}, "creation_info": {"config": {"validate_mime_type": true, "target_size": [224, 224], "maintain_aspect_ratio": true, "convert_to_grayscale": false, "normalize": true, "enhance_quality": true, "batch_size": 2, "brightness_factor": 1.0, "contrast_factor": 1.0, "sharpness_factor": 1.0, "noise_reduction": true, "use_pretrained_model": true, "model_name": "resnet50", "confidence_threshold": 0.5, "custom_categories": [], "size_thresholds": {"small": 100000, "medium": 1000000, "large": 5000000}, "dimension_thresholds": {"thumbnail": [150, 150], "small": [512, 512], "medium": [1024, 1024], "large": [2048, 2048]}, "split_ratios": [0.7, 0.2, 0.1], "naming_pattern": "{category}_{index:06d}", "preserve_original_names": false, "copy_images": true, "create_symlinks": false, "export_formats": ["csv", "json", "yaml"], "resume": false, "log_level": "INFO", "log_file": null}, "naming_pattern": "{category}_{index:06d}"}}