# تطبيق متقدم لمعالجة وتصنيف صور الإنقاذ والبحث
# Advanced Search and Rescue Image Processing and Classification Application

<div align="center">

![Python](https://img.shields.io/badge/Python-3.8+-blue.svg)
![OpenCV](https://img.shields.io/badge/OpenCV-4.8+-green.svg)
![PyTorch](https://img.shields.io/badge/PyTorch-2.0+-red.svg)
![YOLO](https://img.shields.io/badge/YOLO-v8-orange.svg)
![License](https://img.shields.io/badge/License-MIT-yellow.svg)

**تطبيق متخصص مصمم خصيصاً لدعم عمليات البحث والإنقاذ في البيئات البحرية والصحراوية**

[العربية](#العربية) | [English](#english)

</div>

---

## العربية

### 🎯 **نظرة عامة**

تطبيق متقدم ومتخصص لمعالجة وتصنيف الصور باستخدام الذكاء الاصطناعي، مصمم خصيصاً لدعم عمليات البحث والإنقاذ في البيئات الصعبة. يستخدم التطبيق أحدث تقنيات التعلم العميق والرؤية الحاسوبية لاكتشاف الناجين والحطام في الصور الجوية والفضائية.

### 🚁 **الميزات الرئيسية**

#### **1. نظام المسح الذكي المتخصص**
- 🔍 **مسح متكرر ومتكيف** للدلائل مع دعم أحجام البيانات الضخمة
- 📊 **دعم تنسيقات متنوعة**: JPEG, PNG, TIFF, BMP, GeoTIFF للبيانات الجغرافية
- 🛡️ **معالجة أخطاء قوية** مع تسجيل مفصل للملفات التالفة
- 📈 **إحصائيات شاملة** لعدد الملفات والأحجام والتنسيقات
- 🌍 **استخراج البيانات الجغرافية** من صور الأقمار الصناعية

#### **2. خط أنابيب المعالجة المسبقة المتخصص**
- 🌅 **تحسين الرؤية في الظروف الصعبة**:
  - تعديل السطوع/التباين/التشبع للإضاءة المنخفضة
  - تقليل الضوضاء المتقدم (Bilateral Filter, Non-local Means)
  - تحسين الوضوح (Unsharp Masking, Laplacian filtering)
- 🌫️ **معالجة الظروف الجوية السيئة**:
  - إزالة الضباب والدخان (Dark Channel Prior)
  - تصحيح الوهج والانعكاسات
  - تحسين الإضاءة المنخفضة (CLAHE)
- 🎨 **تصحيح الألوان المتقدم**:
  - توازن الأبيض (Gray World, White Patch)
  - تصحيح الألوان للبيئات المختلفة
- 📐 **تغيير الحجم الذكي** مع الحفاظ على النسبة

#### **3. نظام التصنيف المتخصص للإنقاذ**
- 🌊 **تصنيف البيئات**: بحر، صحراء، ساحل، حضري، غابة، جبل
- 🎯 **كشف أهداف الإنقاذ**:
  - الناجين والأشخاص
  - زوارق النجاة وسترات النجاة
  - حطام الطائرات والمركبات
  - إشارات الاستغاثة ومعدات الطوارئ
- 🤖 **نماذج متقدمة**: YOLOv5/v7/v8, DETR, EfficientDet
- 👁️ **واجهة GUI للوسم اليدوي** والتصحيح
- ✅ **التحقق البشري المستند إلى الثقة**
- 📊 **درجات ثقة متعددة** لكل تصنيف

#### **4. إدارة البيانات الذكية المتقدمة**
- 📝 **اتفاقية تسمية متخصصة**: `{environment}_{object_status}_{timestamp}_{index}.{extension}`
- 📋 **بيانات وصفية شاملة**: CSV, JSON, YAML مع البيانات الجغرافية
- 🎯 **تصدير YOLO جاهز للتدريب**:
  - بنية دليل منظمة (train/val/test)
  - ملفات تسميات بتنسيق YOLO
  - ملف data.yaml مع أسماء الفئات
- 🔄 **إزالة التكرار الذكية** باستخدام Image Hashing
- 📦 **إدارة الإصدارات** مع دعم DVC
- 🌍 **معالجة البيانات الجغرافية** من GeoTIFF

#### **5. تحليلات مجموعة البيانات التكتيكية**
- 📊 **تقسيم متوازن** مع أخذ العينات الطبقية
- 📈 **إحصائيات مفصلة**:
  - توزيع الفئات والبيئات
  - تحليل توازن الفئات الدقيق
  - إحصائيات جودة الصور
- 🔥 **خرائط حرارية للكشف** عن الأهداف
- 📊 **رسوم بيانية تفاعلية** (Matplotlib, Seaborn, Plotly)
- 📋 **تقارير تكتيكية شاملة** مع توصيات

#### **6. وحدة تدريب YOLO المدمجة**
- 🎯 **دعم إصدارات متعددة**: YOLOv5, YOLOv7, YOLOv8
- ⚙️ **تكوينات قابلة للتعديل**:
  - عدد الحقب (Epochs)
  - حجم الدفعة (Batch Size)
  - معدل التعلم (Learning Rate)
- 📊 **تسجيل التقدم** في الوقت الفعلي
- 💾 **حفظ الأوزان والنتائج** تلقائياً
- 📈 **تقييم الأداء** على مجموعة التحقق

### 🛠️ **التثبيت والإعداد**

#### **المتطلبات الأساسية**
```bash
Python 3.8+
CUDA 11.8+ (للمعالجة بـ GPU)
16GB RAM (الحد الأدنى)
```

#### **التثبيت السريع**
```bash
# استنساخ المستودع
git clone https://github.com/your-repo/rescue-image-processing.git
cd rescue-image-processing

# إنشاء بيئة افتراضية
python -m venv rescue_env
source rescue_env/bin/activate  # Linux/Mac
# أو
rescue_env\Scripts\activate  # Windows

# تثبيت المتطلبات
pip install -r rescue_requirements.txt

# تثبيت المتطلبات الاختيارية للـ GPU
pip install torch torchvision --index-url https://download.pytorch.org/whl/cu118
```

#### **التحقق من التثبيت**
```bash
python rescue_main.py --create-config-template
```

### 🚀 **الاستخدام**

#### **1. الاستخدام الأساسي**
```bash
# معالجة أساسية
python rescue_main.py --input ./rescue_images --output ./rescue_dataset

# مع تكوين مخصص
python rescue_main.py --input ./images --output ./dataset --config custom_config.yaml
```

#### **2. الاستخدام المتقدم**
```bash
# مع تحليلات مفصلة
python rescue_main.py --input ./images --output ./dataset --verbose --analytics

# مع تصدير YOLO
python rescue_main.py --input ./images --output ./dataset --yolo-export

# تخصيص المعاملات
python rescue_main.py --input ./images --output ./dataset \
    --target-size 1024 1024 \
    --confidence-threshold 0.7 \
    --batch-size 32
```

#### **3. إنشاء ملف التكوين**
```bash
# إنشاء قالب تكوين
python rescue_main.py --create-config-template

# تحرير rescue_config_template.yaml حسب احتياجاتك
```

### 📁 **هيكل الإخراج**

```
rescue_dataset/
├── train/
│   ├── sea_has_targets/
│   ├── desert_no_targets/
│   └── coast_has_targets/
├── val/
│   └── ...
├── test/
│   └── ...
├── yolo_dataset/
│   ├── train/
│   │   ├── images/
│   │   └── labels/
│   ├── val/
│   └── data.yaml
├── analytics/
│   ├── environment_distribution.png
│   ├── target_detection_analysis.png
│   ├── detection_heatmap.png
│   └── interactive_plots.html
├── rescue_metadata.csv
├── rescue_metadata.json
├── geospatial_metadata.json
├── dataset_manifest.json
└── tactical_rescue_report.json
```

### ⚙️ **التكوين المتقدم**

#### **ملف التكوين (rescue_config.yaml)**
```yaml
# الإعدادات الأساسية
input_directory: './rescue_images'
output_directory: './rescue_dataset'
target_size: [512, 512]
batch_size: 16

# المعالجة المتقدمة
advanced_preprocessing:
  enable_dehazing: true
  enable_low_light_enhancement: true
  enable_color_correction: true
  enable_glare_removal: true
  brightness_factor: 1.2
  contrast_factor: 1.3

# التصنيف المتخصص
specialized_classification:
  environment_model: 'resnet50'
  rescue_detection_model: 'yolov8m'
  rescue_confidence_threshold: 0.5
  enable_human_verification: true

# إدارة البيانات
smart_data_management:
  create_yolo_dataset: true
  enable_deduplication: true
  export_formats: ['csv', 'json', 'yaml']

# التحليلات التكتيكية
tactical_analytics:
  enable_statistical_analysis: true
  enable_heatmaps: true
  enable_interactive_plots: true
```

### 📊 **أمثلة النتائج**

#### **إحصائيات المعالجة**
```
✅ اكتملت المعالجة بنجاح!
⏱️  وقت التنفيذ: 0:15:32
📊 إجمالي الصور: 1,250
✅ تصنيفات ناجحة: 1,198
❌ عمليات فاشلة: 52
📈 معدل النجاح: 95.8%
⚡ سرعة المعالجة: 1.34 صورة/ثانية
```

#### **توزيع البيئات**
- 🌊 بحر: 45.2% (565 صورة)
- 🏜️ صحراء: 32.1% (401 صورة)
- 🏖️ ساحل: 15.7% (196 صورة)
- 🏙️ حضري: 7.0% (88 صورة)

#### **كشف الأهداف**
- 🎯 صور تحتوي على أهداف: 28.4% (355 صورة)
- ❌ صور خالية من الأهداف: 71.6% (895 صورة)
- 👥 أشخاص: 142 كشف
- 🚤 زوارق نجاة: 89 كشف
- 🦺 سترات نجاة: 67 كشف

### 🔧 **استكشاف الأخطاء**

#### **مشاكل شائعة وحلولها**

**1. خطأ CUDA Out of Memory**
```bash
# تقليل حجم الدفعة
python rescue_main.py --batch-size 8 --target-size 256 256

# أو استخدام CPU
export CUDA_VISIBLE_DEVICES=""
```

**2. فشل تحميل النماذج**
```bash
# التأكد من الاتصال بالإنترنت لتحميل النماذج المدربة مسبقاً
# أو تحميل النماذج يدوياً
```

**3. مشاكل البيانات الجغرافية**
```bash
# تثبيت GDAL
conda install -c conda-forge gdal
# أو
apt-get install gdal-bin libgdal-dev
```

### 📈 **الأداء والتحسين**

#### **توصيات الأجهزة**

**للمعالجة السريعة:**
- GPU: NVIDIA RTX 3080+ (8GB+ VRAM)
- RAM: 32GB+
- تخزين: SSD NVMe

**للمعالجة الاقتصادية:**
- GPU: NVIDIA GTX 1660+ (6GB+ VRAM)
- RAM: 16GB
- تخزين: SSD SATA

#### **تحسين الأداء**
```yaml
# للسرعة القصوى
specialized_classification:
  environment_model: 'mobilenetv2'
  rescue_detection_model: 'yolov8n'
  rescue_confidence_threshold: 0.7

# للدقة القصوى
specialized_classification:
  environment_model: 'efficientnet-b4'
  rescue_detection_model: 'yolov8x'
  rescue_confidence_threshold: 0.3
```

### 🤝 **المساهمة**

نرحب بالمساهمات! يرجى قراءة [دليل المساهمة](CONTRIBUTING.md) قبل البدء.

#### **مجالات المساهمة المطلوبة:**
- 🧠 تحسين نماذج الذكاء الاصطناعي
- 🌍 دعم بيئات جديدة
- 🎯 إضافة أهداف إنقاذ جديدة
- 🔧 تحسين الأداء
- 📚 تحسين الوثائق
- 🌐 الترجمة للغات أخرى

### 📄 **الترخيص**

هذا المشروع مرخص تحت رخصة MIT - انظر ملف [LICENSE](LICENSE) للتفاصيل.

### 🙏 **الشكر والتقدير**

- فرق البحث والإنقاذ العالمية
- مجتمع الذكاء الاصطناعي مفتوح المصدر
- مطوري OpenCV و PyTorch و Ultralytics

### 📞 **الدعم والتواصل**

- 📧 البريد الإلكتروني: <EMAIL>
- 💬 Discord: [رابط الخادم]
- 📱 Telegram: [@rescue_ai_support]
- 🐛 تقارير الأخطاء: [GitHub Issues]

### 🔄 **التحديثات**

- **v1.0.0** (2024-01-15): الإصدار الأولي
- **v1.1.0** (2024-02-01): إضافة دعم GeoTIFF
- **v1.2.0** (2024-02-15): تحسين كشف الأهداف
- **v1.3.0** (2024-03-01): واجهة GUI محسنة

---

## English

### 🎯 **Overview**

An advanced and specialized image processing and classification application using artificial intelligence, designed specifically to support search and rescue operations in challenging environments. The application uses the latest deep learning and computer vision technologies to detect survivors and debris in aerial and satellite imagery.

### 🚁 **Key Features**

- **🔍 Intelligent Scanning System** with geospatial data support
- **🌅 Advanced Preprocessing Pipeline** for harsh conditions
- **🎯 Specialized Classification System** for rescue scenarios
- **📊 Smart Data Management** with YOLO export
- **📈 Tactical Analytics** with comprehensive insights
- **🤖 Integrated YOLO Training Module**

### 🛠️ **Quick Start**

```bash
# Clone and setup
git clone https://github.com/your-repo/rescue-image-processing.git
cd rescue-image-processing
pip install -r rescue_requirements.txt

# Basic usage
python rescue_main.py --input ./images --output ./dataset

# Advanced usage with analytics
python rescue_main.py --input ./images --output ./dataset --analytics --yolo-export
```

### 📊 **Example Results**

```
✅ Processing completed successfully!
⏱️  Execution time: 0:15:32
📊 Total images: 1,250
✅ Successful classifications: 1,198
📈 Success rate: 95.8%
⚡ Processing speed: 1.34 images/second
```

### 🤝 **Contributing**

We welcome contributions! Please read our [Contributing Guide](CONTRIBUTING.md) before starting.

### 📄 **License**

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

---

<div align="center">

**🚁 مصمم خصيصاً لإنقاذ الأرواح | Designed Specifically to Save Lives 🚁**

Made with ❤️ for Search and Rescue Teams Worldwide

</div>
