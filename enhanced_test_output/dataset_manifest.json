{"dataset_info": {"total_images": 6, "categories": ["size_extra_large", "size_large"], "splits": {"train": 3, "val": 1, "test": 2}, "split_ratios": [0.7, 0.2, 0.1]}, "statistics": {"category_distribution": {"size_extra_large": 1, "size_large": 5}, "format_distribution": {"png": 1, "jpg": 5}, "size_statistics": {"min": 7132, "max": 24902700, "mean": 4390260.666666667, "total": 26341564}, "dimension_statistics": {"width": {"min": 150, "max": 3840, "mean": 1263.6666666666667}, "height": {"min": 100, "max": 2160, "mean": 782.0}}}, "creation_info": {"config": {"validate_mime_type": true, "target_size": [224, 224], "maintain_aspect_ratio": true, "convert_to_grayscale": false, "normalize": true, "enhance_quality": true, "batch_size": 32, "brightness_factor": 1.0, "contrast_factor": 1.0, "sharpness_factor": 1.0, "noise_reduction": true, "use_pretrained_model": true, "model_name": "resnet50", "confidence_threshold": 0.5, "custom_categories": [], "file_size_classification": {"enabled": true, "thresholds": {"small": 512000, "medium": 2097152}}, "dimension_classification": {"enabled": true, "resolution_thresholds": {"low_resolution": [640, 480], "standard_resolution": [1920, 1080]}, "aspect_ratio_tolerance": 0.1}, "content_classification": {"enabled": true, "available_models": ["resnet50", "efficientnet-b0", "mobilenetv2"], "model_confidence_threshold": 0.3, "max_predictions": 5}, "object_detection": {"enabled": false, "model_name": "yolov8n", "confidence_threshold": 0.5, "iou_threshold": 0.45, "max_detections": 100, "save_detection_images": false, "object_categories": {"contains_objects": "Contains Objects", "object_free": "Object-Free"}}, "size_thresholds": {"small": 100000, "medium": 1000000, "large": 5000000}, "dimension_thresholds": {"thumbnail": [150, 150], "small": [512, 512], "medium": [1024, 1024], "large": [2048, 2048]}, "split_ratios": [0.7, 0.2, 0.1], "naming_pattern": "{category}_{index:06d}", "preserve_original_names": false, "copy_images": true, "create_symlinks": false, "export_formats": ["csv", "json", "yaml"], "resume": false, "log_level": "INFO", "log_file": null, "input_dir": "enhanced_test_images", "output_dir": "enhanced_test_output"}, "naming_pattern": "{category}_{index:06d}"}}