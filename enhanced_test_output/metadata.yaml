- absolute_path: enhanced_test_images/large_high_res.png
  aspect_ratio: 1.778
  aspect_ratio_category: wide
  channels: 3
  dataset_filename: size_extra_large_000000.png
  dataset_path: test/size_extra_large/size_extra_large_000000.png
  detection_model_used: placeholder
  dimension_category: extra_large
  directory: .
  enhanced: true
  enhanced_high_confidence: false
  enhanced_orientation: landscape
  enhanced_predicted_class: unknown
  enhanced_prediction_confidence: 0.0
  enhanced_resolution_category: high_resolution
  enhanced_size_category: large
  filename: large_high_res.png
  format: png
  high_confidence: false
  model_used: placeholder
  normalized: true
  object_classification: Object-Free
  orientation: ''
  original_height: 2160
  original_path: large_high_res.png
  original_width: 3840
  predicted_class: baseball_bat
  prediction_confidence: 0.023063124949242196
  primary_category: size_extra_large
  processed: true
  resolution_description: '> 1920x1080 (2,073,600+ pixels)'
  size_bytes: 24902700
  size_bytes_formatted: 23.7 MB
  size_category: extra_large
  size_description: '> 2.0 MB'
  split: test
  total_objects_detected: 0
  total_pixels: 8294400
  unique_object_classes: []
- absolute_path: enhanced_test_images/medium_standard_res.jpg
  aspect_ratio: 1.778
  aspect_ratio_category: wide
  channels: 3
  dataset_filename: size_large_000002.jpg
  dataset_path: train/size_large/size_large_000002.jpg
  detection_model_used: placeholder
  dimension_category: large
  directory: .
  enhanced: true
  enhanced_high_confidence: false
  enhanced_orientation: landscape
  enhanced_predicted_class: unknown
  enhanced_prediction_confidence: 0.0
  enhanced_resolution_category: standard_resolution
  enhanced_size_category: medium
  filename: medium_standard_res.jpg
  format: jpg
  high_confidence: false
  model_used: placeholder
  normalized: true
  object_classification: Object-Free
  orientation: ''
  original_height: 720
  original_path: medium_standard_res.jpg
  original_width: 1280
  predicted_class: baseball_bat
  prediction_confidence: 0.023063124949242196
  primary_category: size_large
  processed: true
  resolution_description: 640x480 to 1920x1080 (307,200 - 2,073,600 pixels)
  size_bytes: 695790
  size_bytes_formatted: 679.5 KB
  size_category: large
  size_description: 500.0 KB - 2.0 MB
  split: train
  total_objects_detected: 0
  total_pixels: 921600
  unique_object_classes: []
- absolute_path: enhanced_test_images/portrait_image.jpg
  aspect_ratio: 0.75
  aspect_ratio_category: standard_portrait
  channels: 3
  dataset_filename: size_large_000000.jpg
  dataset_path: val/size_large/size_large_000000.jpg
  detection_model_used: placeholder
  dimension_category: large
  directory: .
  enhanced: true
  enhanced_high_confidence: false
  enhanced_orientation: portrait
  enhanced_predicted_class: unknown
  enhanced_prediction_confidence: 0.0
  enhanced_resolution_category: standard_resolution
  enhanced_size_category: small
  filename: portrait_image.jpg
  format: jpg
  high_confidence: false
  model_used: placeholder
  normalized: true
  object_classification: Object-Free
  orientation: ''
  original_height: 800
  original_path: portrait_image.jpg
  original_width: 600
  predicted_class: baseball_bat
  prediction_confidence: 0.023063124949242196
  primary_category: size_large
  processed: true
  resolution_description: 640x480 to 1920x1080 (307,200 - 2,073,600 pixels)
  size_bytes: 289458
  size_bytes_formatted: 282.7 KB
  size_category: large
  size_description: < 500.0 KB
  split: val
  total_objects_detected: 0
  total_pixels: 480000
  unique_object_classes: []
- absolute_path: enhanced_test_images/small_low_res.jpg
  aspect_ratio: 1.5
  aspect_ratio_category: wide
  channels: 3
  dataset_filename: size_large_000000.jpg
  dataset_path: test/size_large/size_large_000000.jpg
  detection_model_used: placeholder
  dimension_category: large
  directory: .
  enhanced: true
  enhanced_high_confidence: false
  enhanced_orientation: landscape
  enhanced_predicted_class: unknown
  enhanced_prediction_confidence: 0.0
  enhanced_resolution_category: low_resolution
  enhanced_size_category: small
  filename: small_low_res.jpg
  format: jpg
  high_confidence: false
  model_used: placeholder
  normalized: true
  object_classification: Object-Free
  orientation: ''
  original_height: 100
  original_path: small_low_res.jpg
  original_width: 150
  predicted_class: baseball_bat
  prediction_confidence: 0.023063124949242196
  primary_category: size_large
  processed: true
  resolution_description: < 640x480 (307,200 pixels)
  size_bytes: 7132
  size_bytes_formatted: 7.0 KB
  size_category: large
  size_description: < 500.0 KB
  split: test
  total_objects_detected: 0
  total_pixels: 15000
  unique_object_classes: []
- absolute_path: enhanced_test_images/square_image.jpg
  aspect_ratio: 1.0
  aspect_ratio_category: square
  channels: 3
  dataset_filename: size_large_000001.jpg
  dataset_path: train/size_large/size_large_000001.jpg
  detection_model_used: placeholder
  dimension_category: large
  directory: .
  enhanced: true
  enhanced_high_confidence: false
  enhanced_orientation: square
  enhanced_predicted_class: unknown
  enhanced_prediction_confidence: 0.0
  enhanced_resolution_category: low_resolution
  enhanced_size_category: small
  filename: square_image.jpg
  format: jpg
  high_confidence: false
  model_used: placeholder
  normalized: true
  object_classification: Object-Free
  orientation: ''
  original_height: 512
  original_path: square_image.jpg
  original_width: 512
  predicted_class: baseball_bat
  prediction_confidence: 0.023063124949242196
  primary_category: size_large
  processed: true
  resolution_description: < 640x480 (307,200 pixels)
  size_bytes: 157885
  size_bytes_formatted: 154.2 KB
  size_category: large
  size_description: < 500.0 KB
  split: train
  total_objects_detected: 0
  total_pixels: 262144
  unique_object_classes: []
- absolute_path: enhanced_test_images/ultra_wide.jpg
  aspect_ratio: 3.0
  aspect_ratio_category: ultra_wide
  channels: 3
  dataset_filename: size_large_000000.jpg
  dataset_path: train/size_large/size_large_000000.jpg
  detection_model_used: placeholder
  dimension_category: large
  directory: .
  enhanced: true
  enhanced_high_confidence: false
  enhanced_orientation: landscape
  enhanced_predicted_class: unknown
  enhanced_prediction_confidence: 0.0
  enhanced_resolution_category: standard_resolution
  enhanced_size_category: small
  filename: ultra_wide.jpg
  format: jpg
  high_confidence: false
  model_used: placeholder
  normalized: true
  object_classification: Object-Free
  orientation: ''
  original_height: 400
  original_path: ultra_wide.jpg
  original_width: 1200
  predicted_class: baseball_bat
  prediction_confidence: 0.023063124949242196
  primary_category: size_large
  processed: true
  resolution_description: 640x480 to 1920x1080 (307,200 - 2,073,600 pixels)
  size_bytes: 288599
  size_bytes_formatted: 281.8 KB
  size_category: large
  size_description: < 500.0 KB
  split: train
  total_objects_detected: 0
  total_pixels: 480000
  unique_object_classes: []
