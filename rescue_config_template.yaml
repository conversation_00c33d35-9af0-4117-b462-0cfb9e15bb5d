advanced_preprocessing:
  brightness_factor: 1.2
  contrast_factor: 1.3
  convert_to_grayscale: false
  denoise_method: bilateral
  enable_color_correction: true
  enable_dehazing: true
  enable_glare_removal: true
  enable_low_light_enhancement: true
  normalization_method: standard
  normalize_images: true
  preserve_aspect_ratio: true
  saturation_factor: 1.1
  sharpness_factor: 1.2
  target_size:
  - 512
  - 512
batch_size: 16
input_directory: ./rescue_images
intelligent_scanning:
  enable_geospatial_analysis: true
  log_level: INFO
  supported_formats:
  - .jpg
  - .jpeg
  - .png
  - .tiff
  - .tif
  - .bmp
  - .webp
log_directory: ./logs
log_level: INFO
output_directory: ./rescue_dataset
preserve_aspect_ratio: true
smart_data_management:
  copy_files: true
  create_yolo_dataset: true
  enable_deduplication: true
  enable_versioning: true
  export_formats:
  - csv
  - json
  - yaml
  hash_algorithm: phash
  include_geospatial_metadata: true
  naming_convention: '{environment}_{object_status}_{timestamp}_{index:06d}.{extension}'
  similarity_threshold: 5
  yolo_format_version: v8
specialized_classification:
  custom_rescue_categories: []
  device: auto
  enable_gpu_acceleration: true
  enable_human_verification: true
  environment_confidence_threshold: 0.7
  environment_model: resnet50
  human_verification_threshold: 0.6
  max_detections: 100
  rescue_confidence_threshold: 0.5
  rescue_detection_model: yolov8m
  rescue_iou_threshold: 0.45
stratified_split: true
tactical_analytics:
  analyze_detection_patterns: true
  analyze_spatial_distribution: true
  analyze_temporal_patterns: true
  dpi: 300
  enable_heatmaps: true
  enable_interactive_plots: true
  enable_statistical_analysis: true
  export_formats:
  - png
  - pdf
  - html
  figure_size:
  - 12
  - 8
target_size:
- 512
- 512
test_ratio: 0.1
train_ratio: 0.7
val_ratio: 0.2
