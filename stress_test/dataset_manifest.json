{"dataset_info": {"total_images": 16, "categories": ["size_large"], "splits": {"train": 12, "val": 2, "test": 2}, "split_ratios": [0.8, 0.15, 0.05]}, "statistics": {"category_distribution": {"size_large": 16}, "format_distribution": {"jpeg": 16}, "size_statistics": {"min": 21677, "max": 28679, "mean": 25690.25, "total": 411044}, "dimension_statistics": {"width": {"min": 596, "max": 596, "mean": 596.0}, "height": {"min": 296, "max": 296, "mean": 296.0}}}, "creation_info": {"config": {"target_size": [512, 512], "maintain_aspect_ratio": true, "convert_to_grayscale": false, "normalize": true, "enhance_quality": true, "batch_size": 16, "brightness_factor": 1.05, "contrast_factor": 1.1, "sharpness_factor": 1.05, "noise_reduction": true, "use_pretrained_model": false, "confidence_threshold": 0.6, "custom_categories": ["portraits", "landscapes", "architecture", "animals", "food", "events", "travel"], "split_ratios": [0.8, 0.15, 0.05], "naming_pattern": "photo_{category}_{index:06d}", "preserve_original_names": false, "copy_images": true, "create_symlinks": false, "export_formats": ["csv", "json"], "validate_mime_type": true, "resume": false, "log_level": "INFO"}, "naming_pattern": "photo_{category}_{index:06d}"}}