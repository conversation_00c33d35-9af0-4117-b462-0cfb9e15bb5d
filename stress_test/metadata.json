[{"filename": "1bb9a0e0-img_0056_magic.jpeg", "original_path": "1bb9a0e0-img_0056_magic.jpeg", "absolute_path": "/mnt/sda5/Projet_IA/Dron2/test_images/project-3-at-/images/1bb9a0e0-img_0056_magic.jpeg", "dataset_path": "train/size_large/photo_size_large_000007.jpeg", "dataset_filename": "photo_size_large_000007.jpeg", "format": "jpeg", "size_bytes": 28679, "directory": ".", "original_width": 596, "original_height": 296, "channels": 3, "processed": true, "normalized": true, "enhanced": true, "primary_category": "size_large", "size_category": "large", "dimension_category": "large", "orientation": "landscape", "aspect_ratio": 2.01, "predicted_class": "", "prediction_confidence": 0, "high_confidence": false, "split": "train", "custom_portraits_score": 0.0, "custom_landscapes_score": 0.0, "custom_architecture_score": 0.0, "custom_animals_score": 0.0, "custom_food_score": 0.0, "custom_events_score": 0.0, "custom_travel_score": 0.0}, {"filename": "20de6c61-img_0020_magic_1.jpeg", "original_path": "20de6c61-img_0020_magic_1.jpeg", "absolute_path": "/mnt/sda5/Projet_IA/Dron2/test_images/project-3-at-/images/20de6c61-img_0020_magic_1.jpeg", "dataset_path": "train/size_large/photo_size_large_000008.jpeg", "dataset_filename": "photo_size_large_000008.jpeg", "format": "jpeg", "size_bytes": 22696, "directory": ".", "original_width": 596, "original_height": 296, "channels": 3, "processed": true, "normalized": true, "enhanced": true, "primary_category": "size_large", "size_category": "large", "dimension_category": "large", "orientation": "landscape", "aspect_ratio": 2.01, "predicted_class": "", "prediction_confidence": 0, "high_confidence": false, "split": "train", "custom_portraits_score": 0.0, "custom_landscapes_score": 0.0, "custom_architecture_score": 0.0, "custom_animals_score": 0.0, "custom_food_score": 0.0, "custom_events_score": 0.0, "custom_travel_score": 0.0}, {"filename": "218e3e58-img_0061_magic.jpeg", "original_path": "218e3e58-img_0061_magic.jpeg", "absolute_path": "/mnt/sda5/Projet_IA/Dron2/test_images/project-3-at-/images/218e3e58-img_0061_magic.jpeg", "dataset_path": "train/size_large/photo_size_large_000003.jpeg", "dataset_filename": "photo_size_large_000003.jpeg", "format": "jpeg", "size_bytes": 28112, "directory": ".", "original_width": 596, "original_height": 296, "channels": 3, "processed": true, "normalized": true, "enhanced": true, "primary_category": "size_large", "size_category": "large", "dimension_category": "large", "orientation": "landscape", "aspect_ratio": 2.01, "predicted_class": "", "prediction_confidence": 0, "high_confidence": false, "split": "train", "custom_portraits_score": 0.0, "custom_landscapes_score": 0.0, "custom_architecture_score": 0.0, "custom_animals_score": 0.0, "custom_food_score": 0.0, "custom_events_score": 0.0, "custom_travel_score": 0.0}, {"filename": "3fe254c8-img_0014_magic_1.jpeg", "original_path": "3fe254c8-img_0014_magic_1.jpeg", "absolute_path": "/mnt/sda5/Projet_IA/Dron2/test_images/project-3-at-/images/3fe254c8-img_0014_magic_1.jpeg", "dataset_path": "train/size_large/photo_size_large_000011.jpeg", "dataset_filename": "photo_size_large_000011.jpeg", "format": "jpeg", "size_bytes": 21850, "directory": ".", "original_width": 596, "original_height": 296, "channels": 3, "processed": true, "normalized": true, "enhanced": true, "primary_category": "size_large", "size_category": "large", "dimension_category": "large", "orientation": "landscape", "aspect_ratio": 2.01, "predicted_class": "", "prediction_confidence": 0, "high_confidence": false, "split": "train", "custom_portraits_score": 0.0, "custom_landscapes_score": 0.0, "custom_architecture_score": 0.0, "custom_animals_score": 0.0, "custom_food_score": 0.0, "custom_events_score": 0.0, "custom_travel_score": 0.0}, {"filename": "4eefc39c-img_0060_magic.jpeg", "original_path": "4eefc39c-img_0060_magic.jpeg", "absolute_path": "/mnt/sda5/Projet_IA/Dron2/test_images/project-3-at-/images/4eefc39c-img_0060_magic.jpeg", "dataset_path": "train/size_large/photo_size_large_000005.jpeg", "dataset_filename": "photo_size_large_000005.jpeg", "format": "jpeg", "size_bytes": 27975, "directory": ".", "original_width": 596, "original_height": 296, "channels": 3, "processed": true, "normalized": true, "enhanced": true, "primary_category": "size_large", "size_category": "large", "dimension_category": "large", "orientation": "landscape", "aspect_ratio": 2.01, "predicted_class": "", "prediction_confidence": 0, "high_confidence": false, "split": "train", "custom_portraits_score": 0.0, "custom_landscapes_score": 0.0, "custom_architecture_score": 0.0, "custom_animals_score": 0.0, "custom_food_score": 0.0, "custom_events_score": 0.0, "custom_travel_score": 0.0}, {"filename": "52de5bff-img_0018_magic_1.jpeg", "original_path": "52de5bff-img_0018_magic_1.jpeg", "absolute_path": "/mnt/sda5/Projet_IA/Dron2/test_images/project-3-at-/images/52de5bff-img_0018_magic_1.jpeg", "dataset_path": "train/size_large/photo_size_large_000002.jpeg", "dataset_filename": "photo_size_large_000002.jpeg", "format": "jpeg", "size_bytes": 23773, "directory": ".", "original_width": 596, "original_height": 296, "channels": 3, "processed": true, "normalized": true, "enhanced": true, "primary_category": "size_large", "size_category": "large", "dimension_category": "large", "orientation": "landscape", "aspect_ratio": 2.01, "predicted_class": "", "prediction_confidence": 0, "high_confidence": false, "split": "train", "custom_portraits_score": 0.0, "custom_landscapes_score": 0.0, "custom_architecture_score": 0.0, "custom_animals_score": 0.0, "custom_food_score": 0.0, "custom_events_score": 0.0, "custom_travel_score": 0.0}, {"filename": "5374a6f8-img_0059_magic.jpeg", "original_path": "5374a6f8-img_0059_magic.jpeg", "absolute_path": "/mnt/sda5/Projet_IA/Dron2/test_images/project-3-at-/images/5374a6f8-img_0059_magic.jpeg", "dataset_path": "train/size_large/photo_size_large_000000.jpeg", "dataset_filename": "photo_size_large_000000.jpeg", "format": "jpeg", "size_bytes": 28191, "directory": ".", "original_width": 596, "original_height": 296, "channels": 3, "processed": true, "normalized": true, "enhanced": true, "primary_category": "size_large", "size_category": "large", "dimension_category": "large", "orientation": "landscape", "aspect_ratio": 2.01, "predicted_class": "", "prediction_confidence": 0, "high_confidence": false, "split": "train", "custom_portraits_score": 0.0, "custom_landscapes_score": 0.0, "custom_architecture_score": 0.0, "custom_animals_score": 0.0, "custom_food_score": 0.0, "custom_events_score": 0.0, "custom_travel_score": 0.0}, {"filename": "5fdef5e2-img_0013_magic.jpeg", "original_path": "5fdef5e2-img_0013_magic.jpeg", "absolute_path": "/mnt/sda5/Projet_IA/Dron2/test_images/project-3-at-/images/5fdef5e2-img_0013_magic.jpeg", "dataset_path": "train/size_large/photo_size_large_000006.jpeg", "dataset_filename": "photo_size_large_000006.jpeg", "format": "jpeg", "size_bytes": 21677, "directory": ".", "original_width": 596, "original_height": 296, "channels": 3, "processed": true, "normalized": true, "enhanced": true, "primary_category": "size_large", "size_category": "large", "dimension_category": "large", "orientation": "landscape", "aspect_ratio": 2.01, "predicted_class": "", "prediction_confidence": 0, "high_confidence": false, "split": "train", "custom_portraits_score": 0.0, "custom_landscapes_score": 0.0, "custom_architecture_score": 0.0, "custom_animals_score": 0.0, "custom_food_score": 0.0, "custom_events_score": 0.0, "custom_travel_score": 0.0}, {"filename": "72c7660f-img_0015_magic_1.jpeg", "original_path": "72c7660f-img_0015_magic_1.jpeg", "absolute_path": "/mnt/sda5/Projet_IA/Dron2/test_images/project-3-at-/images/72c7660f-img_0015_magic_1.jpeg", "dataset_path": "train/size_large/photo_size_large_000009.jpeg", "dataset_filename": "photo_size_large_000009.jpeg", "format": "jpeg", "size_bytes": 25262, "directory": ".", "original_width": 596, "original_height": 296, "channels": 3, "processed": true, "normalized": true, "enhanced": true, "primary_category": "size_large", "size_category": "large", "dimension_category": "large", "orientation": "landscape", "aspect_ratio": 2.01, "predicted_class": "", "prediction_confidence": 0, "high_confidence": false, "split": "train", "custom_portraits_score": 0.0, "custom_landscapes_score": 0.0, "custom_architecture_score": 0.0, "custom_animals_score": 0.0, "custom_food_score": 0.0, "custom_events_score": 0.0, "custom_travel_score": 0.0}, {"filename": "77bce7a3-img_0057_magic.jpeg", "original_path": "77bce7a3-img_0057_magic.jpeg", "absolute_path": "/mnt/sda5/Projet_IA/Dron2/test_images/project-3-at-/images/77bce7a3-img_0057_magic.jpeg", "dataset_path": "val/size_large/photo_size_large_000000.jpeg", "dataset_filename": "photo_size_large_000000.jpeg", "format": "jpeg", "size_bytes": 28157, "directory": ".", "original_width": 596, "original_height": 296, "channels": 3, "processed": true, "normalized": true, "enhanced": true, "primary_category": "size_large", "size_category": "large", "dimension_category": "large", "orientation": "landscape", "aspect_ratio": 2.01, "predicted_class": "", "prediction_confidence": 0, "high_confidence": false, "split": "val", "custom_portraits_score": 0.0, "custom_landscapes_score": 0.0, "custom_architecture_score": 0.0, "custom_animals_score": 0.0, "custom_food_score": 0.0, "custom_events_score": 0.0, "custom_travel_score": 0.0}, {"filename": "7e71b5b7-img_0017_magic_1.jpeg", "original_path": "7e71b5b7-img_0017_magic_1.jpeg", "absolute_path": "/mnt/sda5/Projet_IA/Dron2/test_images/project-3-at-/images/7e71b5b7-img_0017_magic_1.jpeg", "dataset_path": "train/size_large/photo_size_large_000010.jpeg", "dataset_filename": "photo_size_large_000010.jpeg", "format": "jpeg", "size_bytes": 25687, "directory": ".", "original_width": 596, "original_height": 296, "channels": 3, "processed": true, "normalized": true, "enhanced": true, "primary_category": "size_large", "size_category": "large", "dimension_category": "large", "orientation": "landscape", "aspect_ratio": 2.01, "predicted_class": "", "prediction_confidence": 0, "high_confidence": false, "split": "train", "custom_portraits_score": 0.0, "custom_landscapes_score": 0.0, "custom_architecture_score": 0.0, "custom_animals_score": 0.0, "custom_food_score": 0.0, "custom_events_score": 0.0, "custom_travel_score": 0.0}, {"filename": "846385d1-img_0058_magic.jpeg", "original_path": "846385d1-img_0058_magic.jpeg", "absolute_path": "/mnt/sda5/Projet_IA/Dron2/test_images/project-3-at-/images/846385d1-img_0058_magic.jpeg", "dataset_path": "train/size_large/photo_size_large_000004.jpeg", "dataset_filename": "photo_size_large_000004.jpeg", "format": "jpeg", "size_bytes": 28162, "directory": ".", "original_width": 596, "original_height": 296, "channels": 3, "processed": true, "normalized": true, "enhanced": true, "primary_category": "size_large", "size_category": "large", "dimension_category": "large", "orientation": "landscape", "aspect_ratio": 2.01, "predicted_class": "", "prediction_confidence": 0, "high_confidence": false, "split": "train", "custom_portraits_score": 0.0, "custom_landscapes_score": 0.0, "custom_architecture_score": 0.0, "custom_animals_score": 0.0, "custom_food_score": 0.0, "custom_events_score": 0.0, "custom_travel_score": 0.0}, {"filename": "878dbf7b-img_0019_magic_1.jpeg", "original_path": "878dbf7b-img_0019_magic_1.jpeg", "absolute_path": "/mnt/sda5/Projet_IA/Dron2/test_images/project-3-at-/images/878dbf7b-img_0019_magic_1.jpeg", "dataset_path": "test/size_large/photo_size_large_000000.jpeg", "dataset_filename": "photo_size_large_000000.jpeg", "format": "jpeg", "size_bytes": 22075, "directory": ".", "original_width": 596, "original_height": 296, "channels": 3, "processed": true, "normalized": true, "enhanced": true, "primary_category": "size_large", "size_category": "large", "dimension_category": "large", "orientation": "landscape", "aspect_ratio": 2.01, "predicted_class": "", "prediction_confidence": 0, "high_confidence": false, "split": "test", "custom_portraits_score": 0.0, "custom_landscapes_score": 0.0, "custom_architecture_score": 0.0, "custom_animals_score": 0.0, "custom_food_score": 0.0, "custom_events_score": 0.0, "custom_travel_score": 0.0}, {"filename": "bc4c3184-img_0016_magic_1.jpeg", "original_path": "bc4c3184-img_0016_magic_1.jpeg", "absolute_path": "/mnt/sda5/Projet_IA/Dron2/test_images/project-3-at-/images/bc4c3184-img_0016_magic_1.jpeg", "dataset_path": "test/size_large/photo_size_large_000001.jpeg", "dataset_filename": "photo_size_large_000001.jpeg", "format": "jpeg", "size_bytes": 26597, "directory": ".", "original_width": 596, "original_height": 296, "channels": 3, "processed": true, "normalized": true, "enhanced": true, "primary_category": "size_large", "size_category": "large", "dimension_category": "large", "orientation": "landscape", "aspect_ratio": 2.01, "predicted_class": "", "prediction_confidence": 0, "high_confidence": false, "split": "test", "custom_portraits_score": 0.0, "custom_landscapes_score": 0.0, "custom_architecture_score": 0.0, "custom_animals_score": 0.0, "custom_food_score": 0.0, "custom_events_score": 0.0, "custom_travel_score": 0.0}, {"filename": "de3b68a4-img_0021_magic.jpeg", "original_path": "de3b68a4-img_0021_magic.jpeg", "absolute_path": "/mnt/sda5/Projet_IA/Dron2/test_images/project-3-at-/images/de3b68a4-img_0021_magic.jpeg", "dataset_path": "train/size_large/photo_size_large_000001.jpeg", "dataset_filename": "photo_size_large_000001.jpeg", "format": "jpeg", "size_bytes": 26157, "directory": ".", "original_width": 596, "original_height": 296, "channels": 3, "processed": true, "normalized": true, "enhanced": true, "primary_category": "size_large", "size_category": "large", "dimension_category": "large", "orientation": "landscape", "aspect_ratio": 2.01, "predicted_class": "", "prediction_confidence": 0, "high_confidence": false, "split": "train", "custom_portraits_score": 0.0, "custom_landscapes_score": 0.0, "custom_architecture_score": 0.0, "custom_animals_score": 0.0, "custom_food_score": 0.0, "custom_events_score": 0.0, "custom_travel_score": 0.0}, {"filename": "f76beb16-img_0021_magic_1.jpeg", "original_path": "f76beb16-img_0021_magic_1.jpeg", "absolute_path": "/mnt/sda5/Projet_IA/Dron2/test_images/project-3-at-/images/f76beb16-img_0021_magic_1.jpeg", "dataset_path": "val/size_large/photo_size_large_000001.jpeg", "dataset_filename": "photo_size_large_000001.jpeg", "format": "jpeg", "size_bytes": 25994, "directory": ".", "original_width": 596, "original_height": 296, "channels": 3, "processed": true, "normalized": true, "enhanced": true, "primary_category": "size_large", "size_category": "large", "dimension_category": "large", "orientation": "landscape", "aspect_ratio": 2.01, "predicted_class": "", "prediction_confidence": 0, "high_confidence": false, "split": "val", "custom_portraits_score": 0.0, "custom_landscapes_score": 0.0, "custom_architecture_score": 0.0, "custom_animals_score": 0.0, "custom_food_score": 0.0, "custom_events_score": 0.0, "custom_travel_score": 0.0}]