# Core dependencies
numpy>=1.21.0
pandas>=1.3.0
Pillow>=8.3.0
opencv-python>=4.5.0
PyYAML>=5.4.0
tqdm>=4.62.0

# GUI dependencies (tkinter is usually included with Python)
# If tkinter is not available, install python3-tk on Ubuntu/Debian:
# sudo apt-get install python3-tk

# Scientific computing and machine learning
scikit-learn>=1.0.0
scipy>=1.7.0

# Optional visualization dependencies
matplotlib>=3.4.0
seaborn>=0.11.0

# Enhanced Classification Dependencies
torch>=1.9.0
torchvision>=0.10.0
timm>=0.6.0

# Object Detection Dependencies (optional)
ultralytics>=8.0.0

# Alternative deep learning framework (optional)
# tensorflow>=2.6.0

# Development and testing dependencies (optional)
# pytest>=6.2.0
# pytest-cov>=2.12.0
# black>=21.0.0
# flake8>=3.9.0
# mypy>=0.910
