#!/usr/bin/env python3
"""
مشغل الواجهة الرسومية المحسنة للإنقاذ والبحث
Enhanced GUI Launcher for Search and Rescue Application
======================================================

سكريبت تشغيل الواجهة الرسومية المحسنة مع فحص المتطلبات
وإعداد البيئة تلقائياً.
"""

import os
import sys
import logging
from pathlib import Path
import subprocess

# إضافة مسار src إلى Python path
sys.path.insert(0, str(Path(__file__).parent / 'src'))

def check_python_version():
    """فحص إصدار Python"""
    if sys.version_info < (3, 8):
        print("❌ خطأ: يتطلب Python 3.8 أو أحدث")
        print(f"   الإصدار الحالي: {sys.version}")
        return False
    
    print(f"✅ إصدار Python: {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")
    return True

def check_required_packages():
    """فحص المتطلبات الأساسية"""
    required_packages = {
        'tkinter': 'مكتبة الواجهة الرسومية',
        'PIL': 'معالجة الصور (Pillow)',
        'numpy': 'العمليات الرقمية',
        'pandas': 'معالجة البيانات',
        'matplotlib': 'الرسوم البيانية',
        'yaml': 'معالجة ملفات YAML'
    }
    
    missing_packages = []
    available_packages = []
    
    print("🔍 فحص المتطلبات الأساسية...")
    
    for package, description in required_packages.items():
        try:
            if package == 'tkinter':
                import tkinter
            elif package == 'PIL':
                from PIL import Image
            elif package == 'numpy':
                import numpy
            elif package == 'pandas':
                import pandas
            elif package == 'matplotlib':
                import matplotlib
            elif package == 'yaml':
                import yaml
            
            available_packages.append(package)
            print(f"   ✅ {package}: {description}")
            
        except ImportError:
            missing_packages.append((package, description))
            print(f"   ❌ {package}: {description} - غير متوفر")
    
    return missing_packages, available_packages

def install_missing_packages(missing_packages):
    """تثبيت المتطلبات المفقودة"""
    if not missing_packages:
        return True
    
    print(f"\n📦 المتطلبات المفقودة: {len(missing_packages)}")
    
    # خريطة أسماء الحزم للتثبيت
    package_map = {
        'PIL': 'Pillow',
        'yaml': 'PyYAML'
    }
    
    install_commands = []
    for package, description in missing_packages:
        install_name = package_map.get(package, package)
        install_commands.append(install_name)
    
    print("💡 لتثبيت المتطلبات المفقودة، قم بتشغيل:")
    print(f"   pip install {' '.join(install_commands)}")
    
    # محاولة التثبيت التلقائي
    response = input("\n❓ هل تريد محاولة التثبيت التلقائي؟ (y/n): ")
    
    if response.lower() in ['y', 'yes', 'نعم', 'ن']:
        try:
            print("🔄 جاري تثبيت المتطلبات...")
            
            for install_name in install_commands:
                print(f"   تثبيت {install_name}...")
                result = subprocess.run([
                    sys.executable, '-m', 'pip', 'install', install_name
                ], capture_output=True, text=True)
                
                if result.returncode == 0:
                    print(f"   ✅ تم تثبيت {install_name}")
                else:
                    print(f"   ❌ فشل تثبيت {install_name}: {result.stderr}")
                    return False
            
            print("✅ تم تثبيت جميع المتطلبات بنجاح!")
            return True
            
        except Exception as e:
            print(f"❌ خطأ في التثبيت التلقائي: {e}")
            return False
    
    return False

def check_optional_packages():
    """فحص المتطلبات الاختيارية"""
    optional_packages = {
        'torch': 'PyTorch للذكاء الاصطناعي',
        'ultralytics': 'YOLO للكشف عن الأهداف',
        'opencv-python': 'OpenCV لمعالجة الصور المتقدمة',
        'folium': 'الخرائط التفاعلية',
        'plotly': 'الرسوم البيانية التفاعلية'
    }
    
    print("\n🔍 فحص المتطلبات الاختيارية...")
    
    available_optional = []
    missing_optional = []
    
    for package, description in optional_packages.items():
        try:
            # Test import availability for optional packages
            if package == 'torch':
                __import__('torch')
            elif package == 'ultralytics':
                __import__('ultralytics')
            elif package == 'opencv-python':
                __import__('cv2')
            elif package == 'folium':
                __import__('folium')
            elif package == 'plotly':
                __import__('plotly')

            available_optional.append(package)
            print(f"   ✅ {package}: {description}")

        except ImportError:
            missing_optional.append((package, description))
            print(f"   ⚠️  {package}: {description} - غير متوفر (اختياري)")
    
    if missing_optional:
        print(f"\n💡 المتطلبات الاختيارية المفقودة ({len(missing_optional)}):")
        for package, description in missing_optional:
            print(f"   - {package}: {description}")
        
        print("\n   هذه المتطلبات اختيارية ولن تؤثر على الوظائف الأساسية")
        print("   يمكن تثبيتها لاحقاً للحصول على ميزات إضافية")
    
    return available_optional, missing_optional

def setup_logging():
    """إعداد نظام التسجيل"""
    log_dir = Path('logs')
    log_dir.mkdir(exist_ok=True)
    
    log_file = log_dir / f'rescue_gui_{Path(__file__).stem}.log'
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    
    return str(log_file)

def create_desktop_shortcut():
    """إنشاء اختصار على سطح المكتب"""
    try:
        import platform
        
        if platform.system() == 'Windows':
            # إنشاء اختصار Windows
            desktop = Path.home() / 'Desktop'
            shortcut_path = desktop / 'تطبيق الإنقاذ والبحث.lnk'
            
            # استخدام PowerShell لإنشاء الاختصار
            ps_script = f'''
$WshShell = New-Object -comObject WScript.Shell
$Shortcut = $WshShell.CreateShortcut("{shortcut_path}")
$Shortcut.TargetPath = "{sys.executable}"
$Shortcut.Arguments = "{Path(__file__).absolute()}"
$Shortcut.WorkingDirectory = "{Path(__file__).parent.absolute()}"
$Shortcut.IconLocation = "{sys.executable}"
$Shortcut.Description = "تطبيق الإنقاذ والبحث المتقدم"
$Shortcut.Save()
'''
            
            result = subprocess.run([
                'powershell', '-Command', ps_script
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                print(f"✅ تم إنشاء اختصار سطح المكتب: {shortcut_path}")
            else:
                print("⚠️  لم يتم إنشاء اختصار سطح المكتب")
        
        elif platform.system() == 'Linux':
            # إنشاء ملف .desktop للينكس
            desktop = Path.home() / 'Desktop'
            if not desktop.exists():
                desktop = Path.home() / '.local' / 'share' / 'applications'
                desktop.mkdir(parents=True, exist_ok=True)
            
            shortcut_path = desktop / 'rescue-app.desktop'
            
            desktop_content = f'''[Desktop Entry]
Version=1.0
Type=Application
Name=تطبيق الإنقاذ والبحث
Name[en]=Search & Rescue Application
Comment=تطبيق متقدم لمعالجة صور الإنقاذ والبحث
Comment[en]=Advanced application for search and rescue image processing
Exec={sys.executable} "{Path(__file__).absolute()}"
Icon=applications-science
Terminal=false
Categories=Science;Education;
'''
            
            with open(shortcut_path, 'w', encoding='utf-8') as f:
                f.write(desktop_content)
            
            # جعل الملف قابل للتنفيذ
            os.chmod(shortcut_path, 0o755)
            
            print(f"✅ تم إنشاء اختصار التطبيق: {shortcut_path}")
        
        else:
            print("⚠️  إنشاء الاختصار غير مدعوم على هذا النظام")
    
    except Exception as e:
        print(f"⚠️  فشل في إنشاء اختصار سطح المكتب: {e}")

def launch_gui():
    """تشغيل الواجهة الرسومية"""
    try:
        print("🚀 تشغيل الواجهة الرسومية المحسنة...")
        
        # استيراد وتشغيل الواجهة
        from src.rescue.enhanced_gui import EnhancedRescueGUI
        
        app = EnhancedRescueGUI()
        app.run()
        
        return True
        
    except ImportError as e:
        print(f"❌ خطأ في استيراد الواجهة: {e}")
        print("   تأكد من وجود جميع ملفات التطبيق")
        return False
    
    except Exception as e:
        print(f"❌ خطأ في تشغيل الواجهة: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚁 مشغل تطبيق الإنقاذ والبحث المتقدم")
    print("=" * 50)
    
    try:
        # إعداد التسجيل
        log_file = setup_logging()
        print(f"📝 ملف السجل: {log_file}")
        
        # فحص إصدار Python
        if not check_python_version():
            return 1
        
        # فحص المتطلبات الأساسية
        missing_packages, available_packages = check_required_packages()
        
        # محاولة تثبيت المتطلبات المفقودة
        if missing_packages:
            if not install_missing_packages(missing_packages):
                print("\n❌ لا يمكن تشغيل التطبيق بدون المتطلبات الأساسية")
                print("   يرجى تثبيت المتطلبات يدوياً ثم إعادة المحاولة")
                return 1
            
            # إعادة فحص المتطلبات بعد التثبيت
            missing_packages, available_packages = check_required_packages()
            
            if missing_packages:
                print("❌ فشل في تثبيت بعض المتطلبات")
                return 1
        
        # فحص المتطلبات الاختيارية
        available_optional, missing_optional = check_optional_packages()
        
        # عرض ملخص الحالة
        print(f"\n📊 ملخص الحالة:")
        print(f"   ✅ المتطلبات الأساسية: {len(available_packages)}/{len(available_packages) + len(missing_packages)}")
        print(f"   ⚠️  المتطلبات الاختيارية: {len(available_optional)}/{len(available_optional) + len(missing_optional)}")
        
        # سؤال عن إنشاء اختصار
        if len(sys.argv) == 1:  # تشغيل مباشر وليس من اختصار
            response = input("\n❓ هل تريد إنشاء اختصار على سطح المكتب؟ (y/n): ")
            if response.lower() in ['y', 'yes', 'نعم', 'ن']:
                create_desktop_shortcut()
        
        print("\n" + "=" * 50)
        
        # تشغيل الواجهة
        success = launch_gui()
        
        if success:
            print("✅ تم إغلاق التطبيق بنجاح")
            return 0
        else:
            print("❌ فشل في تشغيل التطبيق")
            return 1
    
    except KeyboardInterrupt:
        print("\n⚠️  تم إيقاف التطبيق بواسطة المستخدم")
        return 1
    
    except Exception as e:
        print(f"\n❌ خطأ عام: {e}")
        return 1

if __name__ == "__main__":
    exit_code = main()
    
    # انتظار إدخال المستخدم قبل الإغلاق (في Windows)
    if sys.platform.startswith('win'):
        input("\nاضغط Enter للخروج...")
    
    sys.exit(exit_code)
