# Enhanced Classification System Guide

## Overview

The Advanced Image Processing and Classification Application now features a comprehensive multi-criteria classification framework that provides detailed analysis of images across multiple dimensions.

## Enhanced Classification Features

### 1. File Size Classification

**Configurable Size Thresholds:**
- **Small**: < 500KB (configurable)
- **Medium**: 500KB - 2MB (configurable)
- **Large**: > 2MB (configurable)

**Configuration:**
```yaml
file_size_classification:
  enabled: true
  thresholds:
    small: 512000      # 500KB in bytes
    medium: 2097152    # 2MB in bytes
```

**Output Metadata:**
- `enhanced_size_category`: small/medium/large
- `size_description`: Human-readable size range
- `size_bytes_formatted`: Formatted file size (e.g., "1.2 MB")

### 2. Image Dimension Classification

**Resolution Categories:**
- **Low Resolution**: < 640x480 pixels
- **Standard Resolution**: 640x480 to 1920x1080 pixels
- **High Resolution**: > 1920x1080 pixels

**Aspect Ratio Analysis:**
- **Square**: Aspect ratio ≈ 1.0 (within tolerance)
- **Landscape**: Width > Height
  - Standard Landscape: 1.0 < ratio < 1.5
  - Wide: 1.5 ≤ ratio < 2.0
  - Ultra Wide: ratio ≥ 2.0
- **Portrait**: Height > Width
  - Standard Portrait: 0.67 < ratio ≤ 1.0
  - Tall: 0.5 < ratio ≤ 0.67
  - Ultra Tall: ratio ≤ 0.5

**Configuration:**
```yaml
dimension_classification:
  enabled: true
  resolution_thresholds:
    low_resolution: [640, 480]
    standard_resolution: [1920, 1080]
  aspect_ratio_tolerance: 0.1
```

**Output Metadata:**
- `enhanced_resolution_category`: low_resolution/standard_resolution/high_resolution
- `resolution_description`: Human-readable resolution range
- `total_pixels`: Total pixel count
- `enhanced_orientation`: square/landscape/portrait
- `aspect_ratio_category`: Detailed aspect ratio classification

### 3. Enhanced Content Classification

**Supported Models:**
- **ResNet50**: Robust general-purpose classification
- **EfficientNet-B0**: Efficient and accurate classification
- **MobileNetV2**: Lightweight for resource-constrained environments

**Configuration:**
```yaml
content_classification:
  enabled: true
  available_models: ["resnet50", "efficientnet-b0", "mobilenetv2"]
  model_confidence_threshold: 0.3
  max_predictions: 5
```

**Output Metadata:**
- `enhanced_predicted_class`: Top predicted class
- `enhanced_prediction_confidence`: Confidence score (0-1)
- `enhanced_high_confidence`: Boolean for high confidence predictions
- `content_predictions`: List of top predictions with confidence scores
- `model_used`: Name of the model used

### 4. Object Detection Integration

**Supported Models:**
- **YOLOv8n**: Nano version (fastest, smallest)
- **YOLOv8s**: Small version (balanced speed/accuracy)
- **YOLOv8m**: Medium version (higher accuracy)
- **YOLOv5s**: Alternative YOLO implementation

**Configuration:**
```yaml
object_detection:
  enabled: true
  model_name: "yolov8n"
  confidence_threshold: 0.5
  iou_threshold: 0.45
  max_detections: 100
  save_detection_images: false
  object_categories:
    contains_objects: "Contains Objects"
    object_free: "Object-Free"
```

**Output Metadata:**
- `total_objects_detected`: Number of objects found
- `object_classification`: "Contains Objects" or "Object-Free"
- `object_detections`: Detailed list of detected objects
- `unique_object_classes`: List of unique object types detected
- `detection_model_used`: Name of detection model

**Object Detection Details:**
Each detection includes:
- `class_id`: Numeric class identifier
- `class_name`: Human-readable class name
- `confidence`: Detection confidence (0-1)
- `bbox`: Bounding box coordinates [x1, y1, x2, y2]
- `bbox_normalized`: Normalized bounding box [x_center, y_center, width, height]

## Installation and Setup

### 1. Install Enhanced Dependencies

```bash
# Core enhanced classification dependencies
pip install torch>=1.9.0 torchvision>=0.10.0 timm>=0.6.0

# Object detection dependencies
pip install ultralytics>=8.0.0

# Install all at once
pip install -r requirements.txt
```

### 2. Configuration Setup

**Basic Configuration:**
```yaml
# Enable all enhanced features
file_size_classification:
  enabled: true
  
dimension_classification:
  enabled: true
  
content_classification:
  enabled: true
  
object_detection:
  enabled: true  # Set to false if not needed
```

**Performance Configuration:**
```yaml
# For faster processing (lower accuracy)
content_classification:
  available_models: ["mobilenetv2"]
  model_confidence_threshold: 0.5

object_detection:
  model_name: "yolov8n"
  confidence_threshold: 0.6
```

**High Accuracy Configuration:**
```yaml
# For maximum accuracy (slower processing)
content_classification:
  available_models: ["resnet50"]
  model_confidence_threshold: 0.2

object_detection:
  model_name: "yolov8m"
  confidence_threshold: 0.3
```

## Usage Examples

### Command Line Usage

```bash
# Basic enhanced classification
python3 main.py --input ./images --output ./enhanced_dataset

# With custom configuration
python3 main.py --input ./images --output ./enhanced_dataset --config enhanced_config.yaml

# Disable object detection for faster processing
python3 main.py --input ./images --output ./enhanced_dataset --config no_detection_config.yaml
```

### GUI Usage

1. **Launch Enhanced GUI:**
   ```bash
   python3 launch_gui.py
   ```

2. **Configure Enhanced Classification:**
   - Go to "Configuration" tab
   - Select content classification model
   - Adjust confidence thresholds
   - Enable/disable object detection
   - Set detection model and parameters

3. **Monitor Enhanced Results:**
   - Real-time object detection counts
   - Enhanced classification statistics
   - Detailed confidence metrics

### Programmatic Usage

```python
from src.core.classifier import ImageClassifier
from src.utils.config_manager import ConfigManager

# Load enhanced configuration
config = ConfigManager('enhanced_config.yaml').load_config()

# Initialize enhanced classifier
classifier = ImageClassifier(config)

# Process images with enhanced classification
classified_data = classifier.classify_images(processed_data)

# Access enhanced results
for data in classified_data:
    print(f"Enhanced Size: {data['enhanced_size_category']}")
    print(f"Resolution: {data['enhanced_resolution_category']}")
    print(f"Content: {data['enhanced_predicted_class']} ({data['enhanced_prediction_confidence']:.2f})")
    print(f"Objects: {data['total_objects_detected']} detected")
    print(f"Object Classes: {data['unique_object_classes']}")
```

## Output Structure

### Enhanced Metadata Fields

The enhanced classification system adds the following fields to metadata exports:

**File Size Classification:**
- `enhanced_size_category`
- `size_description`
- `size_bytes_formatted`

**Dimension Classification:**
- `enhanced_resolution_category`
- `resolution_description`
- `total_pixels`
- `enhanced_orientation`
- `aspect_ratio_category`

**Content Classification:**
- `enhanced_predicted_class`
- `enhanced_prediction_confidence`
- `enhanced_high_confidence`
- `content_predictions`
- `model_used`

**Object Detection:**
- `total_objects_detected`
- `object_classification`
- `object_detections`
- `unique_object_classes`
- `detection_model_used`

### Analytics Enhancements

The analytics system now includes:

**Enhanced Classification Analysis:**
- Size category distribution and percentages
- Resolution category distribution
- Aspect ratio analysis
- Enhanced content classification statistics
- Confidence score analysis

**Object Detection Analysis:**
- Total objects across dataset
- Average objects per image
- Object detection ratio
- Most common detected objects
- Object classification distribution

## Performance Considerations

### Model Selection Guidelines

**For Speed (Real-time Processing):**
- Content Model: `mobilenetv2`
- Detection Model: `yolov8n`
- Confidence Thresholds: Higher (0.6+)

**For Accuracy (Batch Processing):**
- Content Model: `resnet50`
- Detection Model: `yolov8m`
- Confidence Thresholds: Lower (0.3+)

**For Balanced Performance:**
- Content Model: `efficientnet-b0`
- Detection Model: `yolov8s`
- Confidence Thresholds: Medium (0.5)

### Hardware Requirements

**Minimum (CPU Only):**
- 8GB RAM
- Content classification only
- Small batch sizes (8-16)

**Recommended (GPU):**
- 16GB RAM
- NVIDIA GPU with 4GB+ VRAM
- Full feature set enabled
- Standard batch sizes (32-64)

**Optimal (High-end GPU):**
- 32GB RAM
- NVIDIA GPU with 8GB+ VRAM
- All features with high accuracy models
- Large batch sizes (64-128)

## Troubleshooting

### Common Issues

**1. CUDA Out of Memory:**
```bash
# Reduce batch size or use CPU
export CUDA_VISIBLE_DEVICES=""  # Force CPU
```

**2. Model Download Issues:**
```bash
# Models download automatically on first use
# Ensure internet connection for initial setup
```

**3. Slow Processing:**
```bash
# Use lighter models
content_classification:
  available_models: ["mobilenetv2"]
object_detection:
  model_name: "yolov8n"
```

### Performance Optimization

**1. Disable Unused Features:**
```yaml
object_detection:
  enabled: false  # Disable if not needed
```

**2. Adjust Confidence Thresholds:**
```yaml
# Higher thresholds = fewer detections = faster processing
object_detection:
  confidence_threshold: 0.7
```

**3. Limit Detections:**
```yaml
object_detection:
  max_detections: 50  # Reduce from default 100
```

The enhanced classification system provides comprehensive image analysis while maintaining flexibility for different use cases and performance requirements.
