#!/usr/bin/env python3
"""
Enhanced Classification System Test
==================================

Test script for the enhanced multi-criteria classification framework.
"""

import sys
import tempfile
import numpy as np
from pathlib import Path
from PIL import Image

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / 'src'))

from src.utils.config_manager import ConfigManager
from src.core.classifier import ImageClassifier
from src.core.model_manager import ModelManager


def create_test_images():
    """Create test images with different characteristics."""
    test_dir = Path('enhanced_test_images')
    test_dir.mkdir(exist_ok=True)
    
    test_images = []
    
    # Small, low-resolution image
    small_img = np.random.randint(0, 255, (100, 150, 3), dtype=np.uint8)
    small_path = test_dir / 'small_low_res.jpg'
    Image.fromarray(small_img).save(small_path, quality=50)  # Small file size
    test_images.append(small_path)
    
    # Medium, standard resolution image
    medium_img = np.random.randint(0, 255, (720, 1280, 3), dtype=np.uint8)
    medium_path = test_dir / 'medium_standard_res.jpg'
    Image.fromarray(medium_img).save(medium_path, quality=85)
    test_images.append(medium_path)
    
    # Large, high-resolution image
    large_img = np.random.randint(0, 255, (2160, 3840, 3), dtype=np.uint8)
    large_path = test_dir / 'large_high_res.png'
    Image.fromarray(large_img).save(large_path)  # PNG for larger file size
    test_images.append(large_path)
    
    # Square image
    square_img = np.random.randint(0, 255, (512, 512, 3), dtype=np.uint8)
    square_path = test_dir / 'square_image.jpg'
    Image.fromarray(square_img).save(square_path)
    test_images.append(square_path)
    
    # Ultra-wide landscape
    wide_img = np.random.randint(0, 255, (400, 1200, 3), dtype=np.uint8)
    wide_path = test_dir / 'ultra_wide.jpg'
    Image.fromarray(wide_img).save(wide_path)
    test_images.append(wide_path)
    
    # Portrait image
    portrait_img = np.random.randint(0, 255, (800, 600, 3), dtype=np.uint8)
    portrait_path = test_dir / 'portrait_image.jpg'
    Image.fromarray(portrait_img).save(portrait_path)
    test_images.append(portrait_path)
    
    return test_images


def test_enhanced_configuration():
    """Test enhanced configuration loading and validation."""
    print("=== Testing Enhanced Configuration ===")
    
    try:
        # Test default configuration
        config_manager = ConfigManager('config.yaml')
        config = config_manager.load_config()
        
        # Check enhanced classification settings
        file_size_config = config.get('file_size_classification', {})
        print(f"✓ File size classification enabled: {file_size_config.get('enabled', False)}")
        
        dim_config = config.get('dimension_classification', {})
        print(f"✓ Dimension classification enabled: {dim_config.get('enabled', False)}")
        
        content_config = config.get('content_classification', {})
        print(f"✓ Content classification enabled: {content_config.get('enabled', False)}")
        
        obj_config = config.get('object_detection', {})
        print(f"✓ Object detection enabled: {obj_config.get('enabled', False)}")
        
        print("✅ Enhanced configuration test passed")
        return True
        
    except Exception as e:
        print(f"❌ Enhanced configuration test failed: {e}")
        return False


def test_model_manager():
    """Test the ModelManager functionality."""
    print("\n=== Testing Model Manager ===")
    
    try:
        # Load configuration
        config_manager = ConfigManager('config.yaml')
        config = config_manager.load_config()
        
        # Initialize model manager
        model_manager = ModelManager(config)
        
        # Check availability
        content_available = model_manager.is_content_classification_available()
        detection_available = model_manager.is_object_detection_available()
        
        print(f"✓ Content classification available: {content_available}")
        print(f"✓ Object detection available: {detection_available}")
        
        # Test with dummy image
        test_image = np.random.randint(0, 255, (224, 224, 3), dtype=np.uint8)
        
        # Test content classification
        content_results = model_manager.classify_content(test_image)
        print(f"✓ Content classification returned: {len(content_results)} fields")
        
        # Test object detection
        detection_results = model_manager.detect_objects(test_image)
        print(f"✓ Object detection returned: {len(detection_results)} fields")
        
        print("✅ Model manager test passed")
        return True
        
    except Exception as e:
        print(f"❌ Model manager test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_enhanced_classifier():
    """Test the enhanced ImageClassifier."""
    print("\n=== Testing Enhanced Classifier ===")
    
    try:
        # Load configuration
        config_manager = ConfigManager('config.yaml')
        config = config_manager.load_config()
        
        # Disable object detection for faster testing
        config['object_detection']['enabled'] = False
        
        # Initialize classifier
        classifier = ImageClassifier(config)
        
        # Create test data
        test_data = {
            'filename': 'test_image.jpg',
            'filepath': Path('test_image.jpg'),
            'size_bytes': 1500000,  # 1.5MB
            'format': 'JPEG',
            'original_dimensions': (1920, 1080),
            'processed_array': np.random.randint(0, 255, (224, 224, 3), dtype=np.uint8),
            'processed': True,
            'enhanced': True,
            'normalized': True
        }
        
        # Test classification
        result = classifier._add_basic_classification(test_data)
        
        # Check enhanced file size classification
        if 'enhanced_size_category' in result:
            print(f"✓ Enhanced size category: {result['enhanced_size_category']}")
            print(f"✓ Size description: {result['size_description']}")
        
        # Check enhanced dimension classification
        if 'enhanced_resolution_category' in result:
            print(f"✓ Enhanced resolution: {result['enhanced_resolution_category']}")
            print(f"✓ Aspect ratio category: {result['aspect_ratio_category']}")
        
        print("✅ Enhanced classifier test passed")
        return True
        
    except Exception as e:
        print(f"❌ Enhanced classifier test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_full_pipeline():
    """Test the full enhanced classification pipeline."""
    print("\n=== Testing Full Enhanced Pipeline ===")
    
    try:
        # Create test images
        test_images = create_test_images()
        print(f"✓ Created {len(test_images)} test images")
        
        # Load configuration
        config_manager = ConfigManager('config.yaml')
        config = config_manager.load_config()
        
        # Disable object detection for faster testing
        config['object_detection']['enabled'] = False
        
        # Initialize classifier
        classifier = ImageClassifier(config)
        
        # Process each test image
        results = []
        for image_path in test_images:
            # Simulate processed data
            img = Image.open(image_path)
            img_array = np.array(img)
            
            data = {
                'filename': image_path.name,
                'filepath': image_path,
                'size_bytes': image_path.stat().st_size,
                'format': img.format or 'JPEG',
                'original_dimensions': img.size,
                'processed_array': img_array,
                'processed': True,
                'enhanced': True,
                'normalized': True
            }
            
            # Classify
            classified = classifier._add_basic_classification(data)
            results.append(classified)
            
            print(f"✓ Processed {image_path.name}:")
            print(f"  Size: {classified.get('enhanced_size_category', 'unknown')}")
            print(f"  Resolution: {classified.get('enhanced_resolution_category', 'unknown')}")
            print(f"  Orientation: {classified.get('enhanced_orientation', 'unknown')}")
        
        print(f"✅ Full pipeline test passed - processed {len(results)} images")
        return True
        
    except Exception as e:
        print(f"❌ Full pipeline test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_analytics_enhancement():
    """Test enhanced analytics functionality."""
    print("\n=== Testing Enhanced Analytics ===")
    
    try:
        from src.utils.analytics import DatasetAnalytics
        import pandas as pd
        
        # Create sample enhanced metadata
        sample_data = [
            {
                'filename': 'image1.jpg',
                'enhanced_size_category': 'small',
                'enhanced_resolution_category': 'low_resolution',
                'aspect_ratio_category': 'standard_landscape',
                'enhanced_predicted_class': 'cat',
                'enhanced_prediction_confidence': 0.85,
                'total_objects_detected': 2,
                'object_classification': 'Contains Objects',
                'unique_object_classes': ['cat', 'person']
            },
            {
                'filename': 'image2.jpg',
                'enhanced_size_category': 'medium',
                'enhanced_resolution_category': 'standard_resolution',
                'aspect_ratio_category': 'square',
                'enhanced_predicted_class': 'dog',
                'enhanced_prediction_confidence': 0.92,
                'total_objects_detected': 1,
                'object_classification': 'Contains Objects',
                'unique_object_classes': ['dog']
            },
            {
                'filename': 'image3.jpg',
                'enhanced_size_category': 'large',
                'enhanced_resolution_category': 'high_resolution',
                'aspect_ratio_category': 'standard_portrait',
                'enhanced_predicted_class': 'landscape',
                'enhanced_prediction_confidence': 0.78,
                'total_objects_detected': 0,
                'object_classification': 'Object-Free',
                'unique_object_classes': []
            }
        ]
        
        # Initialize analytics
        config = ConfigManager('config.yaml').load_config()
        analytics = DatasetAnalytics(config)
        
        # Convert to DataFrame
        df = pd.DataFrame(sample_data)
        
        # Test enhanced classification analysis
        enhanced_analysis = analytics._analyze_enhanced_classification(df)
        print(f"✓ Enhanced classification analysis: {len(enhanced_analysis)} metrics")
        
        # Test object detection analysis
        object_analysis = analytics._analyze_object_detection(df)
        print(f"✓ Object detection analysis: {len(object_analysis)} metrics")
        
        # Print some results
        if 'enhanced_size_distribution' in enhanced_analysis:
            print(f"  Size distribution: {enhanced_analysis['enhanced_size_distribution']}")
        
        if 'object_detection_statistics' in object_analysis:
            stats = object_analysis['object_detection_statistics']
            print(f"  Total objects: {stats.get('total_objects_across_dataset', 0)}")
            print(f"  Detection ratio: {stats.get('object_detection_ratio', 0):.2f}")
        
        print("✅ Enhanced analytics test passed")
        return True
        
    except Exception as e:
        print(f"❌ Enhanced analytics test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Main test function."""
    print("🧪 Enhanced Classification System Test")
    print("=" * 50)
    
    tests = [
        test_enhanced_configuration,
        test_model_manager,
        test_enhanced_classifier,
        test_full_pipeline,
        test_analytics_enhancement
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ Test {test_func.__name__} crashed: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All enhanced classification tests passed!")
        print("✅ Enhanced classification system is ready for use")
        return 0
    else:
        print("⚠️  Some tests failed - check the output above")
        print("💡 Note: Some failures may be due to missing optional dependencies")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
