#!/usr/bin/env python3
"""
Advanced Image Processing and Classification Application
======================================================

A comprehensive Python application for automatic dataset generation with:
- Directory scanning and image processing
- Multi-criteria classification with pre-trained models
- Dataset splitting and analytics
- Metadata generation and export

Author: AI Assistant
Version: 1.0.0
"""

import argparse
import sys
import logging
from pathlib import Path

from src.core.scanner import ImageScanner
from src.core.processor import ImageProcessor
from src.core.classifier import ImageClassifier
from src.core.dataset_manager import DatasetManager
from src.utils.config_manager import ConfigManager
from src.utils.logger import setup_logger


def main():
    """Main application entry point."""
    parser = argparse.ArgumentParser(
        description="Advanced Image Processing and Classification Application",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python main.py --input /path/to/images --output /path/to/output
  python main.py --config config.yaml --resume
  python main.py --input ./images --output ./dataset --split-ratios 0.7 0.2 0.1
        """
    )
    
    parser.add_argument(
        "--input", "-i",
        type=str,
        required=True,
        help="Input directory containing images to process"
    )
    
    parser.add_argument(
        "--output", "-o",
        type=str,
        required=True,
        help="Output directory for processed dataset"
    )
    
    parser.add_argument(
        "--config", "-c",
        type=str,
        default="config.yaml",
        help="Configuration file path (default: config.yaml)"
    )
    
    parser.add_argument(
        "--split-ratios",
        nargs=3,
        type=float,
        default=[0.7, 0.2, 0.1],
        help="Train/validation/test split ratios (default: 0.7 0.2 0.1)"
    )
    
    parser.add_argument(
        "--resume",
        action="store_true",
        help="Resume interrupted processing"
    )
    
    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="Enable verbose logging"
    )
    
    args = parser.parse_args()
    
    # Validate split ratios
    if abs(sum(args.split_ratios) - 1.0) > 1e-6:
        print(f"Error: Split ratios must sum to 1.0 (current sum: {sum(args.split_ratios):.3f})")
        print(f"Current ratios: train={args.split_ratios[0]}, val={args.split_ratios[1]}, test={args.split_ratios[2]}")
        sys.exit(1)
    
    # Setup logging
    log_level = logging.DEBUG if args.verbose else logging.INFO
    logger = setup_logger(log_level)
    
    try:
        # Load configuration
        config_manager = ConfigManager(args.config)
        config = config_manager.load_config()
        
        # Override config with command line arguments
        config['input_dir'] = args.input
        config['output_dir'] = args.output
        config['split_ratios'] = args.split_ratios
        config['resume'] = args.resume
        
        # Initialize components
        scanner = ImageScanner(config)
        processor = ImageProcessor(config)
        classifier = ImageClassifier(config)
        dataset_manager = DatasetManager(config)
        
        logger.info("Starting image processing pipeline...")
        
        # Step 1: Scan for images
        logger.info("Step 1: Scanning for images...")
        image_files = scanner.scan_directory(Path(args.input))
        logger.info(f"Found {len(image_files)} images")
        
        # Step 2: Process images
        logger.info("Step 2: Processing images...")
        processed_data = processor.process_images(image_files)
        
        # Step 3: Classify images
        logger.info("Step 3: Classifying images...")
        classified_data = classifier.classify_images(processed_data)
        
        # Step 4: Generate dataset
        logger.info("Step 4: Generating dataset...")
        dataset_manager.create_dataset(classified_data, Path(args.output))
        
        logger.info("Pipeline completed successfully!")
        
    except KeyboardInterrupt:
        logger.info("Processing interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Error during processing: {e}")
        if args.verbose:
            logger.exception("Full traceback:")
        sys.exit(1)


if __name__ == "__main__":
    main()
