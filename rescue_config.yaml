# ملف التكوين المتخصص لتطبيق الإنقاذ والبحث
# Specialized Configuration File for Search and Rescue Application
# ================================================================

# الإعدادات الأساسية | Basic Settings
# =====================================
input_directory: './rescue_images'
output_directory: './rescue_dataset'
log_level: 'INFO'
log_directory: './logs'

# إعدادات المعالجة | Processing Settings
# ======================================
target_size: [512, 512]
batch_size: 16
preserve_aspect_ratio: true

# إعدادات تقسيم البيانات | Data Splitting Settings
# ================================================
train_ratio: 0.7
val_ratio: 0.2
test_ratio: 0.1
stratified_split: true

# إعدادات المسح الذكي | Intelligent Scanning Settings
# ===================================================
intelligent_scanning:
  # تفعيل تحليل البيانات الجغرافية المكانية
  enable_geospatial_analysis: true
  
  # التنسيقات المدعومة للصور
  supported_formats:
    - '.jpg'
    - '.jpeg'
    - '.png'
    - '.tiff'
    - '.tif'
    - '.bmp'
    - '.webp'
    - '.jp2'
    - '.hdf'
    - '.nc'
  
  # مستوى التسجيل للمسح
  log_level: 'INFO'
  
  # دليل السجلات
  log_directory: './logs'

# إعدادات المعالجة المتقدمة | Advanced Preprocessing Settings
# ===========================================================
advanced_preprocessing:
  # الحجم المستهدف للصور
  target_size: [512, 512]
  preserve_aspect_ratio: true
  interpolation_method: 'LANCZOS'
  
  # تحسين الرؤية في الظروف الصعبة
  # Vision Enhancement for Harsh Conditions
  brightness_factor: 1.2
  contrast_factor: 1.3
  saturation_factor: 1.1
  sharpness_factor: 1.2
  
  # إزالة الضباب والدخان | Dehazing and Smoke Removal
  enable_dehazing: true
  dark_channel_omega: 0.95
  atmospheric_light_percentile: 0.1
  
  # تحسين الإضاءة المنخفضة | Low-Light Enhancement
  enable_low_light_enhancement: true
  clahe_clip_limit: 3.0
  clahe_tile_grid_size: [8, 8]
  
  # تصحيح الألوان | Color Correction
  enable_color_correction: true
  white_balance_method: 'gray_world'  # 'gray_world' أو 'white_patch'
  
  # إزالة الوهج والانعكاسات | Glare and Reflection Removal
  enable_glare_removal: true
  glare_threshold: 240
  
  # تقليل الضوضاء المتقدم | Advanced Noise Reduction
  denoise_method: 'bilateral'  # 'bilateral', 'nl_means', 'gaussian'
  bilateral_d: 9
  bilateral_sigma_color: 75
  bilateral_sigma_space: 75
  
  # التحويلات النهائية | Final Transformations
  convert_to_grayscale: false
  normalize_images: true
  normalization_method: 'standard'  # 'standard', 'z_score'

# إعدادات التصنيف المتخصص | Specialized Classification Settings
# ==============================================================
specialized_classification:
  # نموذج تصنيف البيئة | Environment Classification Model
  environment_model: 'resnet50'  # 'resnet50', 'efficientnet_b0', 'vit_base'
  environment_confidence_threshold: 0.7
  
  # نموذج كشف أهداف الإنقاذ | Rescue Target Detection Model
  rescue_detection_model: 'yolov8m'  # 'yolov8n', 'yolov8s', 'yolov8m', 'yolov8l', 'yolov8x'
  rescue_confidence_threshold: 0.5
  rescue_iou_threshold: 0.45
  max_detections: 100
  
  # التحقق البشري | Human Verification
  enable_human_verification: true
  human_verification_threshold: 0.6
  
  # فئات الإنقاذ المخصصة | Custom Rescue Categories
  custom_rescue_categories: []
  
  # إعدادات متعددة الفئات | Multi-class Detection Settings
  enable_multi_class_detection: true
  
  # إعدادات الأداء | Performance Settings
  device: 'auto'  # 'auto', 'cpu', 'cuda'
  batch_size: 1
  enable_gpu_acceleration: true

# إعدادات إدارة البيانات الذكية | Smart Data Management Settings
# ===============================================================
smart_data_management:
  # اتفاقية التسمية | Naming Convention
  naming_convention: '{environment}_{object_status}_{timestamp}_{index:06d}.{extension}'
  use_arabic_names: false
  
  # إعدادات تقسيم البيانات | Data Splitting Settings
  train_ratio: 0.7
  val_ratio: 0.2
  test_ratio: 0.1
  stratified_split: true
  
  # إعدادات تصدير YOLO | YOLO Export Settings
  create_yolo_dataset: true
  yolo_format_version: 'v8'
  include_confidence_in_labels: false
  
  # إعدادات البيانات الوصفية | Metadata Settings
  export_formats:
    - 'csv'
    - 'json'
    - 'yaml'
  include_geospatial_metadata: true
  include_processing_metadata: true
  
  # إعدادات إزالة التكرار | Deduplication Settings
  enable_deduplication: true
  hash_algorithm: 'phash'  # 'phash', 'dhash', 'whash', 'average_hash'
  similarity_threshold: 5
  
  # إعدادات إدارة الإصدارات | Version Management Settings
  enable_versioning: true
  version_control_system: 'dvc'
  
  # إعدادات الأداء | Performance Settings
  copy_files: true
  create_symlinks: false
  preserve_original_structure: false

# إعدادات التحليلات التكتيكية | Tactical Analytics Settings
# ==========================================================
tactical_analytics:
  # إعدادات الرسوم البيانية | Plotting Settings
  figure_size: [12, 8]
  dpi: 300
  style: 'seaborn-v0_8'
  color_palette: 'viridis'
  
  # إعدادات التحليل | Analysis Settings
  enable_statistical_analysis: true
  confidence_level: 0.95
  enable_heatmaps: true
  enable_interactive_plots: true
  
  # إعدادات التصدير | Export Settings
  export_formats:
    - 'png'
    - 'pdf'
    - 'html'
  save_data_tables: true
  
  # إعدادات التحليل التكتيكي | Tactical Analysis Settings
  analyze_temporal_patterns: true
  analyze_spatial_distribution: true
  analyze_detection_patterns: true

# إعدادات وحدة تدريب YOLO | YOLO Training Module Settings
# ========================================================
yolo_training:
  # إعدادات النموذج | Model Settings
  model_version: 'yolov8m'  # 'yolov8n', 'yolov8s', 'yolov8m', 'yolov8l', 'yolov8x'
  pretrained: true
  
  # إعدادات التدريب | Training Settings
  epochs: 100
  batch_size: 16
  learning_rate: 0.01
  momentum: 0.937
  weight_decay: 0.0005
  
  # إعدادات البيانات | Data Settings
  image_size: 640
  augmentation: true
  mosaic: 1.0
  mixup: 0.0
  
  # إعدادات الحفظ | Saving Settings
  save_period: 10
  save_best: true
  save_last: true
  
  # إعدادات التحقق | Validation Settings
  val_period: 1
  patience: 50
  
  # إعدادات الأداء | Performance Settings
  workers: 8
  device: 'auto'
  amp: true  # Automatic Mixed Precision
  
  # إعدادات التسجيل | Logging Settings
  project: 'rescue_training'
  name: 'rescue_yolo_training'
  exist_ok: true
  verbose: true

# إعدادات واجهة المستخدم الرسومية | GUI Settings
# ===============================================
gui_settings:
  # إعدادات النافذة | Window Settings
  window_title: 'تطبيق الإنقاذ والبحث - Rescue & Search Application'
  window_size: [1200, 800]
  theme: 'default'
  
  # إعدادات اللغة | Language Settings
  default_language: 'arabic'
  support_rtl: true
  
  # إعدادات العرض | Display Settings
  show_progress_bars: true
  show_real_time_stats: true
  auto_refresh_interval: 1000  # milliseconds
  
  # إعدادات الوسم اليدوي | Manual Annotation Settings
  annotation_tools:
    - 'bounding_box'
    - 'polygon'
    - 'point'
  default_annotation_tool: 'bounding_box'
  
  # إعدادات الألوان | Color Settings
  rescue_target_colors:
    person: '#FF0000'
    life_jacket: '#00FF00'
    life_boat: '#0000FF'
    debris: '#FFFF00'
    aircraft_wreckage: '#FF00FF'
    vehicle_wreckage: '#00FFFF'

# إعدادات الأمان والخصوصية | Security and Privacy Settings
# ==========================================================
security_settings:
  # تشفير البيانات | Data Encryption
  encrypt_sensitive_data: false
  encryption_algorithm: 'AES-256'
  
  # إعدادات الوصول | Access Settings
  require_authentication: false
  session_timeout: 3600  # seconds
  
  # إعدادات التدقيق | Audit Settings
  enable_audit_logging: true
  audit_log_retention_days: 90
  
  # إعدادات الخصوصية | Privacy Settings
  anonymize_geospatial_data: false
  remove_exif_data: false

# إعدادات الشبكة والاتصال | Network and Communication Settings
# ==============================================================
network_settings:
  # إعدادات البروكسي | Proxy Settings
  use_proxy: false
  proxy_host: ''
  proxy_port: 8080
  
  # إعدادات التحميل | Download Settings
  download_timeout: 300  # seconds
  max_retries: 3
  
  # إعدادات النماذج | Model Settings
  auto_download_models: true
  model_cache_directory: './models'
  
  # إعدادات التحديثات | Update Settings
  check_for_updates: true
  auto_update: false

# إعدادات الأداء والذاكرة | Performance and Memory Settings
# ==========================================================
performance_settings:
  # إعدادات الذاكرة | Memory Settings
  max_memory_usage_gb: 16
  enable_memory_monitoring: true
  garbage_collection_interval: 100
  
  # إعدادات المعالجة المتوازية | Parallel Processing Settings
  max_workers: 4
  use_multiprocessing: true
  chunk_size: 10
  
  # إعدادات التخزين المؤقت | Caching Settings
  enable_caching: true
  cache_directory: './cache'
  cache_size_limit_gb: 5
  
  # إعدادات التحسين | Optimization Settings
  optimize_for_speed: false
  optimize_for_accuracy: true
  enable_mixed_precision: true

# إعدادات التصدير والتكامل | Export and Integration Settings
# ===========================================================
export_settings:
  # تنسيقات التصدير | Export Formats
  supported_formats:
    - 'csv'
    - 'json'
    - 'yaml'
    - 'xml'
    - 'parquet'
  
  # إعدادات قواعد البيانات | Database Settings
  export_to_database: false
  database_type: 'sqlite'
  database_connection_string: ''
  
  # إعدادات السحابة | Cloud Settings
  export_to_cloud: false
  cloud_provider: 'aws'  # 'aws', 'azure', 'gcp'
  cloud_bucket: ''
  
  # إعدادات API | API Settings
  enable_rest_api: false
  api_port: 8000
  api_host: 'localhost'

# إعدادات التطوير والاختبار | Development and Testing Settings
# =============================================================
development_settings:
  # وضع التطوير | Development Mode
  debug_mode: false
  verbose_logging: false
  
  # إعدادات الاختبار | Testing Settings
  enable_unit_tests: true
  test_data_directory: './test_data'
  
  # إعدادات التوثيق | Documentation Settings
  generate_documentation: true
  documentation_format: 'markdown'
  
  # إعدادات الإحصائيات | Statistics Settings
  collect_usage_statistics: true
  statistics_retention_days: 30
