#!/usr/bin/env python3
"""
GUI Launcher for Image Processing Application
============================================

Enhanced launcher with dependency checking and error handling.
"""

import sys
import os
from pathlib import Path
import subprocess
import tkinter as tk
from tkinter import messagebox

def check_dependencies():
    """Check if all required dependencies are installed."""
    required_packages = [
        'numpy', 'pandas', 'PIL', 'cv2', 'yaml', 'tqdm'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'PIL':
                import PIL
            elif package == 'cv2':
                import cv2
            elif package == 'yaml':
                import yaml
            else:
                __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    return missing_packages

def install_dependencies(packages):
    """Install missing dependencies."""
    try:
        for package in packages:
            if package == 'PIL':
                package = 'Pillow'
            elif package == 'cv2':
                package = 'opencv-python'
            elif package == 'yaml':
                package = 'PyYAML'
            
            print(f"Installing {package}...")
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])
        
        return True
    except subprocess.CalledProcessError as e:
        print(f"Failed to install dependencies: {e}")
        return False

def show_dependency_dialog(missing_packages):
    """Show dialog for missing dependencies."""
    root = tk.Tk()
    root.withdraw()  # Hide main window
    
    message = f"""Missing required packages:
{', '.join(missing_packages)}

Would you like to install them automatically?
(This will run: pip install {' '.join(missing_packages)})"""
    
    result = messagebox.askyesno("Missing Dependencies", message)
    root.destroy()
    
    return result

def check_python_version():
    """Check if Python version is compatible."""
    if sys.version_info < (3, 8):
        root = tk.Tk()
        root.withdraw()
        messagebox.showerror(
            "Python Version Error",
            f"This application requires Python 3.8 or higher.\n"
            f"Current version: {sys.version_info.major}.{sys.version_info.minor}"
        )
        root.destroy()
        return False
    return True

def check_src_directory():
    """Check if src directory exists."""
    src_path = Path(__file__).parent / 'src'
    if not src_path.exists():
        root = tk.Tk()
        root.withdraw()
        messagebox.showerror(
            "Application Error",
            f"Source directory not found: {src_path}\n"
            f"Please ensure the application is properly installed."
        )
        root.destroy()
        return False
    return True

def main():
    """Main launcher function."""
    print("Image Processing Application - GUI Launcher")
    print("=" * 50)
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Check source directory
    if not check_src_directory():
        sys.exit(1)
    
    # Check dependencies
    print("Checking dependencies...")
    missing_packages = check_dependencies()
    
    if missing_packages:
        print(f"Missing packages: {', '.join(missing_packages)}")
        
        # Show GUI dialog for dependency installation
        if show_dependency_dialog(missing_packages):
            print("Installing missing dependencies...")
            if install_dependencies(missing_packages):
                print("Dependencies installed successfully!")
            else:
                print("Failed to install dependencies. Please install manually:")
                print(f"pip install {' '.join(missing_packages)}")
                sys.exit(1)
        else:
            print("Cannot run application without required dependencies.")
            print("Please install manually:")
            print(f"pip install {' '.join(missing_packages)}")
            sys.exit(1)
    else:
        print("All dependencies are satisfied.")
    
    # Launch GUI application
    print("Launching GUI application...")
    try:
        # Import and run GUI
        from gui_main import main as gui_main
        gui_main()
    except ImportError as e:
        root = tk.Tk()
        root.withdraw()
        messagebox.showerror(
            "Import Error",
            f"Failed to import GUI application:\n{e}\n\n"
            f"Please ensure all files are present and dependencies are installed."
        )
        root.destroy()
        sys.exit(1)
    except Exception as e:
        root = tk.Tk()
        root.withdraw()
        messagebox.showerror(
            "Application Error",
            f"An error occurred while running the application:\n{e}"
        )
        root.destroy()
        sys.exit(1)

if __name__ == "__main__":
    main()
