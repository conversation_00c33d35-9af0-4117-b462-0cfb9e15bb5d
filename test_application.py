#!/usr/bin/env python3
"""
Test Script for Image Processing Application
===========================================

Simple test script to verify the application works correctly.
"""

import os
import sys
import tempfile
import shutil
from pathlib import Path
from PIL import Image
import numpy as np

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / 'src'))

from src.core.scanner import ImageScanner
from src.core.processor import ImageProcessor
from src.core.classifier import ImageClassifier
from src.core.dataset_manager import DatasetManager
from src.utils.config_manager import ConfigManager
from src.utils.logger import setup_logger


def create_test_images(test_dir: Path, num_images: int = 10):
    """
    Create test images for testing the application.
    
    Args:
        test_dir: Directory to create test images in
        num_images: Number of test images to create
    """
    print(f"Creating {num_images} test images in {test_dir}")
    
    # Create subdirectories
    categories = ['photos', 'documents', 'graphics']
    for category in categories:
        (test_dir / category).mkdir(parents=True, exist_ok=True)
    
    # Create test images
    for i in range(num_images):
        category = categories[i % len(categories)]
        
        # Create random image
        if category == 'photos':
            # Colorful image for photos
            image_array = np.random.randint(0, 255, (300, 400, 3), dtype=np.uint8)
        elif category == 'documents':
            # Black and white for documents
            image_array = np.random.randint(200, 255, (400, 300, 3), dtype=np.uint8)
        else:
            # Graphics with specific patterns
            image_array = np.random.randint(0, 255, (200, 200, 3), dtype=np.uint8)
        
        # Save image
        image = Image.fromarray(image_array)
        image_path = test_dir / category / f"test_image_{i:03d}.jpg"
        image.save(image_path, 'JPEG', quality=85)
    
    print(f"Created test images successfully")


def test_application():
    """
    Test the complete application pipeline.
    """
    print("Starting application test...")
    
    # Setup logging
    logger = setup_logger()
    
    # Create temporary directories
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        input_dir = temp_path / 'input'
        output_dir = temp_path / 'output'
        
        try:
            # Create test images
            create_test_images(input_dir, 15)
            
            # Load configuration
            config_manager = ConfigManager('config.yaml')
            config = config_manager.load_config()
            
            # Override some settings for testing
            config.update({
                'batch_size': 4,
                'use_pretrained_model': False,  # Skip model loading for test
                'enhance_quality': False,       # Speed up processing
                'export_formats': ['json'],     # Minimal export
            })
            
            print("Testing image scanner...")
            # Test scanner
            scanner = ImageScanner(config)
            image_files = scanner.scan_directory(input_dir)
            print(f"Scanner found {len(image_files)} images")
            
            if len(image_files) == 0:
                print("ERROR: No images found by scanner")
                return False
            
            print("Testing image processor...")
            # Test processor
            processor = ImageProcessor(config)
            processed_data = processor.process_images(image_files)
            print(f"Processor handled {len(processed_data)} images")
            
            if len(processed_data) == 0:
                print("ERROR: No images processed")
                return False
            
            print("Testing image classifier...")
            # Test classifier
            classifier = ImageClassifier(config)
            classified_data = classifier.classify_images(processed_data)
            print(f"Classifier handled {len(classified_data)} images")
            
            if len(classified_data) == 0:
                print("ERROR: No images classified")
                return False
            
            print("Testing dataset manager...")
            # Test dataset manager
            dataset_manager = DatasetManager(config)
            dataset_manager.create_dataset(classified_data, output_dir)
            
            # Verify output structure
            expected_dirs = ['train', 'val', 'test']
            for dir_name in expected_dirs:
                dir_path = output_dir / dir_name
                if not dir_path.exists():
                    print(f"ERROR: Missing directory {dir_name}")
                    return False
                print(f"✓ Found {dir_name} directory")
            
            # Check for metadata files
            metadata_files = ['metadata.json', 'dataset_manifest.json']
            for file_name in metadata_files:
                file_path = output_dir / file_name
                if not file_path.exists():
                    print(f"ERROR: Missing file {file_name}")
                    return False
                print(f"✓ Found {file_name}")
            
            # Check analytics directory
            analytics_dir = output_dir / 'analytics'
            if analytics_dir.exists():
                print("✓ Found analytics directory")
            
            print("✅ All tests passed successfully!")
            print(f"Test output created in: {output_dir}")
            
            # Show some statistics
            scanner_stats = scanner.get_statistics()
            processor_stats = processor.get_statistics()
            classifier_stats = classifier.get_statistics()
            dataset_stats = dataset_manager.get_statistics()
            
            print("\nTest Statistics:")
            print(f"  Scanner: {scanner_stats}")
            print(f"  Processor: {processor_stats}")
            print(f"  Classifier: {classifier_stats}")
            print(f"  Dataset: {dataset_stats}")
            
            return True
            
        except Exception as e:
            print(f"ERROR during testing: {e}")
            import traceback
            traceback.print_exc()
            return False


def main():
    """Main test function."""
    print("Image Processing Application Test")
    print("=" * 40)
    
    success = test_application()
    
    if success:
        print("\n🎉 Application test completed successfully!")
        print("The application is ready to use.")
        sys.exit(0)
    else:
        print("\n❌ Application test failed!")
        print("Please check the error messages above.")
        sys.exit(1)


if __name__ == "__main__":
    main()
