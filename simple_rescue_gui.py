#!/usr/bin/env python3
"""
واجهة مبسطة لتطبيق الإنقاذ والبحث
Simple GUI for Search and Rescue Application
==========================================

واجهة رسومية مبسطة وموثوقة لتطبيق معالجة صور الإنقاذ والبحث
تعمل بدون تعقيدات الواجهة المتقدمة.
"""

import os
import sys
import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
from pathlib import Path
import threading
import subprocess
import json
from datetime import datetime

# إضافة مسار src إلى Python path
sys.path.insert(0, str(Path(__file__).parent / 'src'))

class SimpleRescueGUI:
    """واجهة مبسطة لتطبيق الإنقاذ والبحث"""
    
    def __init__(self):
        """تهيئة الواجهة المبسطة"""
        self.root = tk.Tk()
        self.root.title("🚁 تطبيق الإنقاذ والبحث - واجهة مبسطة")
        self.root.geometry("900x700")
        self.root.minsize(800, 600)
        
        # متغيرات الواجهة
        self.input_directory = tk.StringVar()
        self.output_directory = tk.StringVar()
        self.config_file = tk.StringVar()
        
        # متغيرات الإعدادات
        self.target_size_width = tk.IntVar(value=512)
        self.target_size_height = tk.IntVar(value=512)
        self.batch_size = tk.IntVar(value=16)
        self.enable_analytics = tk.BooleanVar(value=True)
        self.verbose_mode = tk.BooleanVar(value=False)
        
        # حالة المعالجة
        self.processing_thread = None
        self.is_processing = False
        
        self._setup_ui()
        
    def _setup_ui(self):
        """إعداد واجهة المستخدم"""
        # إطار رئيسي
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # عنوان التطبيق
        title_label = ttk.Label(main_frame, 
                               text="🚁 تطبيق الإنقاذ والبحث المبسط", 
                               font=('Arial', 18, 'bold'))
        title_label.pack(pady=(0, 20))
        
        # إعداد المجلدات
        self._create_folder_settings(main_frame)
        
        # إعدادات سريعة
        self._create_quick_settings(main_frame)
        
        # أزرار التحكم
        self._create_control_buttons(main_frame)
        
        # منطقة السجل
        self._create_log_area(main_frame)
        
        # شريط الحالة
        self._create_status_bar()
        
    def _create_folder_settings(self, parent):
        """إنشاء إعدادات المجلدات"""
        folders_frame = ttk.LabelFrame(parent, text="📁 إعدادات المجلدات", padding="15")
        folders_frame.pack(fill=tk.X, pady=(0, 15))
        
        # مجلد الإدخال
        input_frame = ttk.Frame(folders_frame)
        input_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(input_frame, text="مجلد الصور:", font=('Arial', 10, 'bold')).pack(anchor=tk.W)
        
        input_entry_frame = ttk.Frame(input_frame)
        input_entry_frame.pack(fill=tk.X, pady=(5, 0))
        
        self.input_entry = ttk.Entry(input_entry_frame, textvariable=self.input_directory, font=('Arial', 10))
        self.input_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10))
        
        ttk.Button(input_entry_frame, text="📂 تصفح", 
                  command=self._browse_input_directory).pack(side=tk.RIGHT)
        
        # مجلد الإخراج
        output_frame = ttk.Frame(folders_frame)
        output_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(output_frame, text="مجلد النتائج:", font=('Arial', 10, 'bold')).pack(anchor=tk.W)
        
        output_entry_frame = ttk.Frame(output_frame)
        output_entry_frame.pack(fill=tk.X, pady=(5, 0))
        
        self.output_entry = ttk.Entry(output_entry_frame, textvariable=self.output_directory, font=('Arial', 10))
        self.output_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10))
        
        ttk.Button(output_entry_frame, text="📂 تصفح", 
                  command=self._browse_output_directory).pack(side=tk.RIGHT)
        
        # ملف التكوين (اختياري)
        config_frame = ttk.Frame(folders_frame)
        config_frame.pack(fill=tk.X)
        
        ttk.Label(config_frame, text="ملف التكوين (اختياري):", font=('Arial', 10, 'bold')).pack(anchor=tk.W)
        
        config_entry_frame = ttk.Frame(config_frame)
        config_entry_frame.pack(fill=tk.X, pady=(5, 0))
        
        self.config_entry = ttk.Entry(config_entry_frame, textvariable=self.config_file, font=('Arial', 10))
        self.config_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10))
        
        ttk.Button(config_entry_frame, text="📄 تصفح", 
                  command=self._browse_config_file).pack(side=tk.RIGHT)
    
    def _create_quick_settings(self, parent):
        """إنشاء الإعدادات السريعة"""
        settings_frame = ttk.LabelFrame(parent, text="⚙️ إعدادات سريعة", padding="15")
        settings_frame.pack(fill=tk.X, pady=(0, 15))
        
        # صف أول - أحجام الصور
        size_frame = ttk.Frame(settings_frame)
        size_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(size_frame, text="حجم الصورة المستهدف:", font=('Arial', 10, 'bold')).pack(side=tk.LEFT)
        
        ttk.Entry(size_frame, textvariable=self.target_size_width, width=8).pack(side=tk.LEFT, padx=(10, 5))
        ttk.Label(size_frame, text="×").pack(side=tk.LEFT)
        ttk.Entry(size_frame, textvariable=self.target_size_height, width=8).pack(side=tk.LEFT, padx=(5, 10))
        
        # أزرار أحجام محددة مسبقاً
        size_buttons = [
            ("224×224", 224, 224),
            ("512×512", 512, 512),
            ("640×640", 640, 640),
            ("1024×1024", 1024, 1024)
        ]
        
        for text, w, h in size_buttons:
            ttk.Button(size_frame, text=text, 
                      command=lambda w=w, h=h: self._set_image_size(w, h)).pack(side=tk.LEFT, padx=2)
        
        # صف ثاني - إعدادات المعالجة
        processing_frame = ttk.Frame(settings_frame)
        processing_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(processing_frame, text="حجم الدفعة:", font=('Arial', 10, 'bold')).pack(side=tk.LEFT)
        ttk.Scale(processing_frame, from_=1, to=64, variable=self.batch_size, 
                 orient=tk.HORIZONTAL, length=150).pack(side=tk.LEFT, padx=(10, 5))
        
        self.batch_label = ttk.Label(processing_frame, text=str(self.batch_size.get()))
        self.batch_label.pack(side=tk.LEFT, padx=(5, 20))
        
        # خيارات إضافية
        options_frame = ttk.Frame(settings_frame)
        options_frame.pack(fill=tk.X)
        
        ttk.Checkbutton(options_frame, text="تمكين التحليلات المتقدمة", 
                       variable=self.enable_analytics).pack(side=tk.LEFT, padx=(0, 20))
        
        ttk.Checkbutton(options_frame, text="الوضع المفصل", 
                       variable=self.verbose_mode).pack(side=tk.LEFT)
    
    def _create_control_buttons(self, parent):
        """إنشاء أزرار التحكم"""
        control_frame = ttk.LabelFrame(parent, text="🎮 التحكم", padding="15")
        control_frame.pack(fill=tk.X, pady=(0, 15))
        
        buttons_frame = ttk.Frame(control_frame)
        buttons_frame.pack()
        
        self.start_button = ttk.Button(buttons_frame, text="🚀 بدء المعالجة", 
                                      command=self._start_processing)
        self.start_button.pack(side=tk.LEFT, padx=(0, 10))
        
        self.stop_button = ttk.Button(buttons_frame, text="⏹️ إيقاف", 
                                     command=self._stop_processing, state=tk.DISABLED)
        self.stop_button.pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(buttons_frame, text="🗑️ مسح السجل", 
                  command=self._clear_log).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(buttons_frame, text="📊 عرض النتائج", 
                  command=self._show_results).pack(side=tk.LEFT)
    
    def _create_log_area(self, parent):
        """إنشاء منطقة السجل"""
        log_frame = ttk.LabelFrame(parent, text="📋 سجل العمليات", padding="10")
        log_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        self.log_text = scrolledtext.ScrolledText(log_frame, 
                                                 wrap=tk.WORD, 
                                                 font=('Consolas', 9),
                                                 bg='#f8f9fa',
                                                 fg='#333333')
        self.log_text.pack(fill=tk.BOTH, expand=True)
        
        # رسالة ترحيب
        self._add_log("🚁 مرحباً بك في تطبيق الإنقاذ والبحث المبسط")
        self._add_log("📝 يرجى تحديد مجلدي الإدخال والإخراج للبدء")
    
    def _create_status_bar(self):
        """إنشاء شريط الحالة"""
        self.status_bar = ttk.Frame(self.root)
        self.status_bar.pack(fill=tk.X, side=tk.BOTTOM)
        
        self.status_label = ttk.Label(self.status_bar, text="جاهز", relief=tk.SUNKEN, anchor=tk.W)
        self.status_label.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(5, 0), pady=2)
        
        self.progress_bar = ttk.Progressbar(self.status_bar, mode='indeterminate')
        self.progress_bar.pack(side=tk.RIGHT, padx=5, pady=2)
    
    def _browse_input_directory(self):
        """تصفح مجلد الإدخال"""
        directory = filedialog.askdirectory(title="اختر مجلد الصور")
        if directory:
            self.input_directory.set(directory)
            self._add_log(f"📂 تم تحديد مجلد الإدخال: {directory}")
    
    def _browse_output_directory(self):
        """تصفح مجلد الإخراج"""
        directory = filedialog.askdirectory(title="اختر مجلد النتائج")
        if directory:
            self.output_directory.set(directory)
            self._add_log(f"📁 تم تحديد مجلد الإخراج: {directory}")
    
    def _browse_config_file(self):
        """تصفح ملف التكوين"""
        file_path = filedialog.askopenfilename(
            title="اختر ملف التكوين",
            filetypes=[("YAML files", "*.yaml *.yml"), ("All files", "*.*")]
        )
        if file_path:
            self.config_file.set(file_path)
            self._add_log(f"📄 تم تحديد ملف التكوين: {file_path}")
    
    def _set_image_size(self, width, height):
        """تعيين حجم الصورة"""
        self.target_size_width.set(width)
        self.target_size_height.set(height)
        self._add_log(f"📐 تم تعيين حجم الصورة: {width}×{height}")
    
    def _add_log(self, message, level="INFO"):
        """إضافة رسالة للسجل"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_message = f"[{timestamp}] {message}\n"
        
        self.log_text.insert(tk.END, log_message)
        self.log_text.see(tk.END)
        self.root.update_idletasks()
    
    def _clear_log(self):
        """مسح السجل"""
        self.log_text.delete(1.0, tk.END)
        self._add_log("🗑️ تم مسح السجل")
    
    def _start_processing(self):
        """بدء المعالجة"""
        if not self.input_directory.get():
            messagebox.showerror("خطأ", "يرجى تحديد مجلد الصور")
            return
        
        if not self.output_directory.get():
            messagebox.showerror("خطأ", "يرجى تحديد مجلد النتائج")
            return
        
        if not os.path.exists(self.input_directory.get()):
            messagebox.showerror("خطأ", "مجلد الصور غير موجود")
            return
        
        # إنشاء مجلد الإخراج إذا لم يكن موجوداً
        os.makedirs(self.output_directory.get(), exist_ok=True)
        
        self.is_processing = True
        self.start_button.config(state=tk.DISABLED)
        self.stop_button.config(state=tk.NORMAL)
        self.progress_bar.start()
        self.status_label.config(text="جاري المعالجة...")
        
        self._add_log("🚀 بدء المعالجة...")
        
        # تشغيل المعالجة في خيط منفصل
        self.processing_thread = threading.Thread(target=self._run_processing)
        self.processing_thread.daemon = True
        self.processing_thread.start()
    
    def _run_processing(self):
        """تشغيل المعالجة الفعلية"""
        try:
            # بناء أمر المعالجة
            cmd = [
                sys.executable, "main.py",
                "--input", self.input_directory.get(),
                "--output", self.output_directory.get(),
                "--target-size", f"{self.target_size_width.get()},{self.target_size_height.get()}",
                "--batch-size", str(self.batch_size.get())
            ]
            
            if self.config_file.get():
                cmd.extend(["--config", self.config_file.get()])
            
            if self.enable_analytics.get():
                cmd.append("--enable-analytics")
            
            if self.verbose_mode.get():
                cmd.append("--verbose")
            
            self._add_log(f"🔧 تشغيل الأمر: {' '.join(cmd)}")
            
            # تشغيل المعالجة
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                bufsize=1,
                universal_newlines=True
            )
            
            # قراءة الإخراج
            for line in process.stdout:
                if not self.is_processing:
                    process.terminate()
                    break
                
                line = line.strip()
                if line:
                    self.root.after(0, lambda msg=line: self._add_log(msg))
            
            process.wait()
            
            if self.is_processing:
                if process.returncode == 0:
                    self.root.after(0, lambda: self._add_log("✅ تمت المعالجة بنجاح!"))
                else:
                    self.root.after(0, lambda: self._add_log("❌ فشلت المعالجة"))
        
        except Exception as e:
            self.root.after(0, lambda: self._add_log(f"❌ خطأ في المعالجة: {str(e)}"))
        
        finally:
            self.root.after(0, self._processing_finished)
    
    def _stop_processing(self):
        """إيقاف المعالجة"""
        self.is_processing = False
        self._add_log("⏹️ تم إيقاف المعالجة")
        self._processing_finished()
    
    def _processing_finished(self):
        """انتهاء المعالجة"""
        self.is_processing = False
        self.start_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)
        self.progress_bar.stop()
        self.status_label.config(text="جاهز")
    
    def _show_results(self):
        """عرض النتائج"""
        if not self.output_directory.get():
            messagebox.showwarning("تحذير", "لم يتم تحديد مجلد النتائج")
            return
        
        if not os.path.exists(self.output_directory.get()):
            messagebox.showwarning("تحذير", "مجلد النتائج غير موجود")
            return
        
        # فتح مجلد النتائج
        try:
            if sys.platform.startswith('win'):
                os.startfile(self.output_directory.get())
            elif sys.platform.startswith('darwin'):
                subprocess.run(['open', self.output_directory.get()])
            else:
                subprocess.run(['xdg-open', self.output_directory.get()])
            
            self._add_log(f"📊 تم فتح مجلد النتائج: {self.output_directory.get()}")
        
        except Exception as e:
            self._add_log(f"❌ فشل في فتح مجلد النتائج: {str(e)}")
    
    def run(self):
        """تشغيل الواجهة"""
        try:
            self.root.mainloop()
        except KeyboardInterrupt:
            self.root.quit()

def main():
    """الدالة الرئيسية"""
    try:
        app = SimpleRescueGUI()
        app.run()
    except Exception as e:
        print(f"خطأ في تشغيل الواجهة المبسطة: {e}")

if __name__ == "__main__":
    main()
