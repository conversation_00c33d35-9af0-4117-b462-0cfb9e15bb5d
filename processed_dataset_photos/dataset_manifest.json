{"dataset_info": {"total_images": 12, "categories": ["size_large"], "splits": {"train": 8, "val": 2, "test": 2}, "split_ratios": [0.7, 0.2, 0.1]}, "statistics": {"category_distribution": {"size_large": 12}, "format_distribution": {"png": 7, "jpg": 5}, "size_statistics": {"min": 408, "max": 83787, "mean": 35318.666666666664, "total": 423824}, "dimension_statistics": {"width": {"min": 200, "max": 400, "mean": 308.3333333333333}, "height": {"min": 200, "max": 400, "mean": 291.6666666666667}}}, "creation_info": {"config": {"validate_mime_type": true, "target_size": [512, 512], "maintain_aspect_ratio": true, "convert_to_grayscale": false, "normalize": true, "enhance_quality": true, "batch_size": 16, "brightness_factor": 1.05, "contrast_factor": 1.1, "sharpness_factor": 1.05, "noise_reduction": true, "use_pretrained_model": true, "model_name": "resnet50", "confidence_threshold": 0.6, "custom_categories": ["portraits", "landscapes", "architecture", "animals", "food", "events", "travel"], "size_thresholds": {"small": 500000, "medium": 2000000, "large": 8000000}, "dimension_thresholds": {"thumbnail": [200, 200], "small": [800, 600], "medium": [1920, 1080], "large": [4000, 3000]}, "split_ratios": [0.7, 0.2, 0.1], "naming_pattern": "photo_{category}_{index:06d}", "preserve_original_names": false, "copy_images": true, "create_symlinks": false, "export_formats": ["csv", "json"], "resume": false, "log_level": "INFO", "log_file": null, "input_dir": "sample_images", "output_dir": "processed_dataset_photos"}, "naming_pattern": "photo_{category}_{index:06d}"}}