# الدليل الشامل لتطبيق الإنقاذ والبحث المتقدم
# Comprehensive Guide for Advanced Search and Rescue Application

## 📋 جدول المحتويات | Table of Contents

1. [نظرة عامة](#نظرة-عامة)
2. [التثبيت والإعداد](#التثبيت-والإعداد)
3. [الاستخدام السريع](#الاستخدام-السريع)
4. [الميزات المتقدمة](#الميزات-المتقدمة)
5. [التحليلات التكتيكية](#التحليلات-التكتيكية)
6. [تدريب نماذج YOLO](#تدريب-نماذج-yolo)
7. [الواجهة الرسومية](#الواجهة-الرسومية)
8. [استكشاف الأخطاء](#استكشاف-الأخطاء)
9. [المساهمة والتطوير](#المساهمة-والتطوير)
10. [الأسئلة الشائعة](#الأسئلة-الشائعة)

---

## 🎯 نظرة عامة

### ما هو تطبيق الإنقاذ والبحث المتقدم؟

تطبيق الإنقاذ والبحث المتقدم هو نظام متكامل ومتخصص لمعالجة وتصنيف صور عمليات الإنقاذ والبحث باستخدام تقنيات الذكاء الاصطناعي المتقدمة. يهدف التطبيق إلى مساعدة فرق الإنقاذ والبحث في تحليل الصور الجوية والأقمار الصناعية لاكتشاف الناجين والحطام والمعدات في البيئات المختلفة.

### الميزات الرئيسية

#### 🔍 **المسح الذكي المتخصص**
- مسح متكرر ومتكيف للدلائل
- دعم تنسيقات متنوعة (JPEG, PNG, TIFF, GeoTIFF)
- استخراج البيانات الجغرافية المكانية
- معالجة أخطاء قوية مع تسجيل مفصل

#### 🖼️ **المعالجة المتقدمة للصور**
- تحسين الرؤية في الظروف الصعبة
- إزالة الضباب والدخان (Dark Channel Prior)
- تحسين الإضاءة المنخفضة (CLAHE)
- تصحيح الألوان وإزالة الوهج
- تقليل الضوضاء المتقدم

#### 🎯 **التصنيف المتخصص للإنقاذ**
- تصنيف البيئات (بحر، صحراء، ساحل، حضري، غابة، جبل)
- كشف أهداف الإنقاذ (ناجين، حطام، معدات)
- دعم نماذج YOLO متعددة (YOLOv8n/s/m/l/x)
- التحقق البشري المستند إلى الثقة
- فئات مخصصة قابلة للتكوين

#### 📊 **إدارة البيانات الذكية**
- اتفاقية تسمية متخصصة
- تصدير مجموعة بيانات YOLO كاملة
- إزالة التكرار الذكية
- إدارة الإصدارات مع DVC
- بيانات وصفية شاملة

#### 📈 **التحليلات التكتيكية المتقدمة**
- تحليل مكاني متقدم مع التجميع
- تحليل زمني وكشف الأنماط
- تحليل الكفاءة التشغيلية
- توليد رؤى تكتيكية ذكية
- تصور تفاعلي للنتائج

#### 🤖 **تدريب نماذج YOLO المدمج**
- تدريب نماذج YOLO متخصصة
- واجهة تدريب تفاعلية
- مراقبة التقدم في الوقت الفعلي
- تصدير النماذج بتنسيقات متعددة

#### 🖥️ **واجهة مستخدم محسنة**
- واجهة رسومية متقدمة مع دعم العربية
- معاينة الصور والنتائج
- تقارير تفاعلية
- إدارة المشاريع

### البيئات المدعومة

- **🌊 البحر**: كشف الناجين في المياه، قوارب النجاة، سترات النجاة
- **🏜️ الصحراء**: البحث في البيئات الصحراوية، كشف المركبات والأشخاص
- **🏖️ الساحل**: عمليات الإنقاذ الساحلية، كشف الحطام البحري
- **🏙️ الحضري**: البحث في المناطق الحضرية، كشف الناجين في المباني
- **🌲 الغابات**: البحث في الغابات، كشف الأشخاص المفقودين
- **⛰️ الجبال**: عمليات الإنقاذ الجبلية، كشف المتسلقين

### فئات الأهداف المدعومة

1. **👤 الأشخاص**: كشف الناجين والأشخاص المفقودين
2. **🦺 سترات النجاة**: كشف سترات النجاة في المياه
3. **🚤 قوارب النجاة**: كشف قوارب النجاة والطوافات
4. **🧱 الحطام**: كشف حطام المباني والمركبات
5. **✈️ حطام الطائرات**: كشف حطام الطائرات المتحطمة
6. **🚗 حطام المركبات**: كشف حطام السيارات والمركبات
7. **🚨 إشارات الطوارئ**: كشف إشارات الاستغاثة والطوارئ
8. **🛠️ معدات الإنقاذ**: كشف معدات الإنقاذ والإسعاف

---

## 🚀 التثبيت والإعداد

### المتطلبات الأساسية

#### متطلبات النظام
- **نظام التشغيل**: Windows 10+, macOS 10.14+, Ubuntu 18.04+
- **Python**: 3.8 أو أحدث
- **الذاكرة**: 8 GB RAM (16 GB مستحسن)
- **مساحة القرص**: 10 GB مساحة فارغة
- **معالج الرسوم**: NVIDIA GPU مع CUDA (اختياري للتسريع)

#### المكتبات المطلوبة

**المتطلبات الأساسية:**
```bash
pip install numpy pandas matplotlib seaborn
pip install Pillow opencv-python PyYAML tqdm
pip install scikit-learn scipy
```

**المتطلبات المتقدمة:**
```bash
pip install torch torchvision  # PyTorch
pip install ultralytics        # YOLOv8
pip install plotly folium      # التصور التفاعلي
pip install dvc                # إدارة الإصدارات
```

### التثبيت السريع

#### 1. تحميل التطبيق
```bash
git clone https://github.com/your-repo/rescue-search-app.git
cd rescue-search-app
```

#### 2. تثبيت المتطلبات
```bash
pip install -r rescue_requirements.txt
```

#### 3. التشغيل السريع
```bash
# تشغيل الواجهة الرسومية
python launch_rescue_gui.py

# أو تشغيل من سطر الأوامر
python rescue_main.py --input ./images --output ./results
```

### التثبيت المتقدم

#### إعداد البيئة الافتراضية
```bash
# إنشاء بيئة افتراضية
python -m venv rescue_env

# تفعيل البيئة (Windows)
rescue_env\Scripts\activate

# تفعيل البيئة (Linux/macOS)
source rescue_env/bin/activate

# تثبيت المتطلبات
pip install -r rescue_requirements.txt
```

#### إعداد CUDA (للتسريع)
```bash
# التحقق من توفر CUDA
python -c "import torch; print(torch.cuda.is_available())"

# تثبيت PyTorch مع CUDA
pip install torch torchvision --index-url https://download.pytorch.org/whl/cu118
```

### التحقق من التثبيت

```bash
# تشغيل اختبار سريع
python simple_tactical_test.py

# أو اختبار شامل
python test_tactical_analytics.py
```

---

## ⚡ الاستخدام السريع

### التشغيل الأساسي

#### 1. باستخدام الواجهة الرسومية
```bash
python launch_rescue_gui.py
```

1. **تحديد المسارات**: اختر دليل الصور ودليل الإخراج
2. **ضبط الإعدادات**: حدد الحجم المستهدف وحجم الدفعة
3. **بدء المعالجة**: اضغط "بدء المعالجة"
4. **مراجعة النتائج**: تصفح النتائج في تبويب "النتائج والتقارير"

#### 2. باستخدام سطر الأوامر
```bash
# معالجة أساسية
python rescue_main.py --input ./rescue_images --output ./rescue_dataset

# معالجة مع تحليلات
python rescue_main.py --input ./images --output ./results --analytics --verbose

# معالجة مع تصدير YOLO
python rescue_main.py --input ./images --output ./results --yolo-export
```

### مثال سريع

```bash
# 1. إنشاء دليل للصور
mkdir rescue_images

# 2. نسخ صورك إلى الدليل
cp /path/to/your/images/* rescue_images/

# 3. تشغيل المعالجة
python rescue_main.py --input rescue_images --output rescue_results --analytics

# 4. مراجعة النتائج
ls rescue_results/
```

### هيكل النتائج

```
rescue_results/
├── processed_images/          # الصور المعالجة
│   ├── sea/                  # صور البحر
│   ├── desert/               # صور الصحراء
│   └── coast/                # صور الساحل
├── yolo_dataset/             # مجموعة بيانات YOLO
│   ├── train/
│   ├── val/
│   ├── test/
│   └── data.yaml
├── analytics/                # التحليلات والتقارير
│   ├── tactical_report.json
│   ├── visualizations/
│   └── insights_report.json
├── metadata/                 # البيانات الوصفية
│   ├── rescue_metadata.csv
│   └── processing_log.json
└── logs/                     # ملفات السجل
    └── rescue_processing.log
```

---

## 🔬 الميزات المتقدمة

### المعالجة المتقدمة للصور

#### إزالة الضباب والدخان
```python
# في ملف التكوين
advanced_preprocessing:
  enable_dehazing: true
  dark_channel_omega: 0.95
  atmospheric_light_percentile: 0.1
```

#### تحسين الإضاءة المنخفضة
```python
# إعدادات CLAHE
advanced_preprocessing:
  enable_low_light_enhancement: true
  clahe_clip_limit: 3.0
  clahe_tile_grid_size: [8, 8]
```

#### تصحيح الألوان
```python
# تصحيح توازن الألوان
advanced_preprocessing:
  enable_color_correction: true
  white_balance_method: 'gray_world'  # أو 'white_patch'
```

### التصنيف المتخصص

#### تخصيص فئات الإنقاذ
```yaml
# في ملف التكوين
specialized_classification:
  custom_rescue_categories:
    - 'emergency_vehicle'
    - 'medical_equipment'
    - 'communication_device'
    - 'shelter'
```

#### ضبط عتبات الثقة
```yaml
specialized_classification:
  environment_confidence_threshold: 0.7
  rescue_confidence_threshold: 0.5
  human_verification_threshold: 0.6
```

### إدارة البيانات المتقدمة

#### اتفاقية التسمية المخصصة
```yaml
smart_data_management:
  naming_convention: '{environment}_{object_status}_{timestamp}_{index:06d}.{extension}'
  use_arabic_names: false
```

#### إزالة التكرار
```yaml
smart_data_management:
  enable_deduplication: true
  hash_algorithm: 'phash'  # أو 'dhash', 'whash', 'average_hash'
  similarity_threshold: 5
```

### التكامل مع الأنظمة الخارجية

#### تصدير إلى قواعد البيانات
```yaml
export_settings:
  export_to_database: true
  database_type: 'postgresql'
  database_connection_string: 'postgresql://user:pass@localhost/rescue_db'
```

#### التكامل مع السحابة
```yaml
export_settings:
  export_to_cloud: true
  cloud_provider: 'aws'  # أو 'azure', 'gcp'
  cloud_bucket: 'rescue-data-bucket'
```

---

## 📊 التحليلات التكتيكية

### أنواع التحليلات

#### 1. التحليل المكاني
- **التجميع المكاني**: تجميع النقاط المتقاربة جغرافياً
- **كشف النقاط الساخنة**: تحديد المناطق عالية الكثافة
- **تحليل التوزيع**: دراسة أنماط التوزيع الجغرافي
- **الارتباط الذاتي المكاني**: قياس التجميع المكاني

```python
# مثال على التحليل المكاني
spatial_analysis = {
    'clustering_analysis': {
        'algorithm': 'dbscan',
        'eps': 0.1,
        'min_samples': 5
    },
    'hotspot_detection': {
        'enable': true,
        'density_threshold': 0.8
    }
}
```

#### 2. التحليل الزمني
- **تحليل الاتجاهات**: كشف الاتجاهات طويلة المدى
- **تحليل الموسمية**: كشف الأنماط الموسمية والدورية
- **كشف القمم**: تحديد فترات النشاط المكثف
- **التنبؤ**: توقع الأنماط المستقبلية

```python
# مثال على التحليل الزمني
temporal_analysis = {
    'trend_analysis': true,
    'seasonality_detection': true,
    'forecast_horizon_days': 7,
    'confidence_level': 0.95
}
```

#### 3. تحليل الكفاءة التشغيلية
- **مقاييس الأداء**: سرعة المعالجة ومعدل النجاح
- **استخدام الموارد**: توزيع العمل والموارد
- **أوقات الاستجابة**: تحليل أوقات المعالجة
- **فرص التحسين**: تحديد نقاط التحسين

### توليد الرؤى التكتيكية

#### أنواع الرؤى
1. **رؤى تشغيلية**: تحسينات فورية للعمليات
2. **رؤى استراتيجية**: توجيهات طويلة المدى
3. **رؤى تكتيكية**: قرارات تكتيكية سريعة
4. **رؤى تنبؤية**: توقعات مستقبلية

#### مثال على رؤية تكتيكية
```json
{
  "id": "insight_001",
  "category": "operational",
  "priority": "critical",
  "title": "معدل كشف منخفض في البيئة البحرية",
  "description": "معدل الكشف في البيئة البحرية 35% أقل من المتوسط",
  "confidence": 0.92,
  "recommendations": [
    "تحسين خوارزميات الكشف للبيئة البحرية",
    "زيادة التدريب على صور البحر",
    "مراجعة معايير الثقة للكشف البحري"
  ],
  "timeline": "immediate"
}
```

### التصور التفاعلي

#### الرسوم البيانية المتاحة
- **خرائط حرارية**: توزيع الكشوفات جغرافياً
- **رسوم زمنية**: تطور الأنماط عبر الزمن
- **رسوم دائرية**: توزيع البيئات والفئات
- **رسوم شبكية**: مقاييس الأداء المتعددة

#### الخرائط التفاعلية
```python
# إنشاء خريطة تفاعلية للنقاط الساخنة
if FOLIUM_AVAILABLE:
    hotspot_map = create_hotspot_map(
        hotspot_data=spatial_analysis['hotspot_detection'],
        center_coordinates=[25.0, 45.0],
        zoom_level=6
    )
```

---

## 🤖 تدريب نماذج YOLO

### نظرة عامة على تدريب YOLO

تطبيق الإنقاذ والبحث يتضمن نظام تدريب YOLO متكامل ومتخصص لتدريب نماذج كشف الأهداف على بيانات الإنقاذ والبحث.

### الميزات الرئيسية لنظام التدريب

#### 🎯 **دعم إصدارات YOLO متعددة**
- YOLOv8n (نانو): سريع وخفيف
- YOLOv8s (صغير): توازن بين السرعة والدقة
- YOLOv8m (متوسط): دقة جيدة مع سرعة معقولة
- YOLOv8l (كبير): دقة عالية
- YOLOv8x (إضافي كبير): أقصى دقة

#### ⚙️ **تكوينات قابلة للتخصيص**
- إعدادات التدريب (epochs, batch size, learning rate)
- إعدادات البيانات (augmentation, mosaic, mixup)
- إعدادات الأداء (workers, device, AMP)

#### 📊 **مراقبة التقدم في الوقت الفعلي**
- تتبع الخسارة والدقة
- رسوم بيانية للتقدم
- حفظ أفضل النماذج تلقائياً

### التدريب من سطر الأوامر

#### 1. إعداد مجموعة البيانات
```bash
# تأكد من وجود مجموعة بيانات YOLO
ls rescue_dataset/yolo_dataset/
# train/  val/  test/  data.yaml
```

#### 2. تشغيل التدريب الأساسي
```bash
python train_yolo_rescue.py --dataset ./rescue_dataset/yolo_dataset --epochs 100
```

#### 3. تدريب متقدم مع إعدادات مخصصة
```bash
python train_yolo_rescue.py \
    --dataset ./rescue_dataset/yolo_dataset \
    --model yolov8m \
    --epochs 200 \
    --batch-size 32 \
    --learning-rate 0.01 \
    --device cuda
```

#### 4. استخدام ملف تكوين
```bash
# إنشاء ملف تكوين افتراضي
python train_yolo_rescue.py --create-config

# تدريب باستخدام ملف التكوين
python train_yolo_rescue.py --config yolo_training_config.yaml
```

### التدريب باستخدام الواجهة الرسومية

#### 1. تشغيل واجهة التدريب
```bash
python train_yolo_rescue.py --gui
```

#### 2. خطوات التدريب في الواجهة
1. **تحديد مجموعة البيانات**: اختر مجلد مجموعة البيانات
2. **اختيار النموذج**: حدد إصدار YOLO المطلوب
3. **ضبط الإعدادات**: عدد الحقب، حجم الدفعة، معدل التعلم
4. **بدء التدريب**: اضغط "بدء التدريب"
5. **مراقبة التقدم**: تابع التقدم في الوقت الفعلي

### ملف التكوين للتدريب

```yaml
# yolo_training_config.yaml
# إعدادات النموذج
model_version: 'yolov8m'
pretrained: true
image_size: 640

# إعدادات التدريب
epochs: 100
batch_size: 16
learning_rate: 0.01
momentum: 0.937
weight_decay: 0.0005

# إعدادات البيانات
dataset_path: './rescue_dataset/yolo_dataset'
augmentation: true
mosaic: 1.0
mixup: 0.0

# إعدادات الحفظ
save_period: 10
patience: 50

# إعدادات الأداء
workers: 8
device: 'auto'
amp: true

# إعدادات متخصصة للإنقاذ
rescue_classes:
  - 'person'
  - 'life_jacket'
  - 'life_boat'
  - 'debris'
  - 'aircraft_wreckage'
  - 'vehicle_wreckage'
  - 'emergency_signal'
  - 'rescue_equipment'

environment_types:
  - 'sea'
  - 'desert'
  - 'coast'
  - 'urban'
  - 'forest'
  - 'mountain'
```

### تقييم النماذج المدربة

#### تقييم تلقائي
```bash
# تقييم النموذج المدرب
python -c "
from src.rescue.yolo_training_module import YOLOTrainingManager
manager = YOLOTrainingManager({})
results = manager.evaluate_model(
    'rescue_training/training_*/weights/best.pt',
    'rescue_dataset/yolo_dataset/data.yaml'
)
print(results)
"
```

#### مقاييس التقييم
- **mAP50**: متوسط الدقة عند IoU=0.5
- **mAP50-95**: متوسط الدقة عند IoU=0.5:0.95
- **Precision**: الدقة
- **Recall**: الاستدعاء

### تصدير النماذج

#### تصدير بتنسيقات متعددة
```python
from src.rescue.yolo_training_module import YOLOTrainingManager

manager = YOLOTrainingManager({})
exported_models = manager.export_model(
    'rescue_training/training_*/weights/best.pt',
    export_formats=['onnx', 'torchscript', 'tflite']
)
```

#### التنسيقات المدعومة
- **PyTorch (.pt)**: التنسيق الأصلي
- **ONNX (.onnx)**: للنشر متعدد المنصات
- **TorchScript (.torchscript)**: للإنتاج مع PyTorch
- **TensorFlow Lite (.tflite)**: للأجهزة المحمولة

---

## 🖥️ الواجهة الرسومية

### نظرة عامة على الواجهة

الواجهة الرسومية المحسنة توفر تجربة مستخدم متكاملة ومتقدمة مع دعم كامل للغة العربية والميزات التفاعلية.

### الميزات الرئيسية للواجهة

#### 🎨 **تصميم متقدم**
- نظام ألوان متخصص للإنقاذ والبحث
- دعم كامل للغة العربية
- واجهة تكيفية ومتجاوبة
- أيقونات وصور توضيحية

#### 📱 **تجربة مستخدم محسنة**
- تنقل سهل وبديهي
- معاينة فورية للصور
- تقارير تفاعلية
- نوافذ تقدم متقدمة

#### 🔧 **إدارة المشاريع**
- حفظ وتحميل المشاريع
- إعدادات قابلة للحفظ
- تاريخ العمليات
- نسخ احتياطية تلقائية

### تشغيل الواجهة الرسومية

#### التشغيل السريع
```bash
python launch_rescue_gui.py
```

#### التشغيل مع فحص المتطلبات
```bash
# سيقوم بفحص وتثبيت المتطلبات تلقائياً
python launch_rescue_gui.py
```

### أقسام الواجهة الرئيسية

#### 1. **لوحة الإعدادات** (اليسار)
- **الإعدادات الأساسية**: المسارات والمعاملات الأساسية
- **الإعدادات المتقدمة**: تكوينات متخصصة
- **لوحة التحكم**: أزرار التشغيل والإيقاف

#### 2. **لوحة المعاينة والنتائج** (اليمين)
- **معاينة الصور**: عرض الصور مع المعلومات
- **النتائج والتقارير**: عرض النتائج التفاعلي

#### 3. **شريط الحالة** (الأسفل)
- حالة العملية الحالية
- معلومات إضافية
- إحصائيات سريعة

### استخدام الواجهة خطوة بخطوة

#### الخطوة 1: تحديد المسارات
1. اضغط "تصفح" بجانب "دليل الصور المدخلة"
2. اختر المجلد الذي يحتوي على صور الإنقاذ
3. اضغط "تصفح" بجانب "دليل الإخراج"
4. اختر المجلد لحفظ النتائج

#### الخطوة 2: ضبط الإعدادات
1. حدد الحجم المستهدف للصور (افتراضي: 512×512)
2. اختر حجم الدفعة (افتراضي: 16)
3. فعل الخيارات المطلوبة:
   - ✅ تفعيل التحليلات المتقدمة
   - ✅ تصدير مجموعة بيانات YOLO
   - ⚠️ الوضع المفصل (للتطوير)

#### الخطوة 3: بدء المعالجة
1. اضغط "🚀 بدء المعالجة"
2. ستظهر نافذة التقدم مع:
   - شريط التقدم
   - العملية الحالية
   - تفاصيل المعالجة
3. يمكن إيقاف العملية بالضغط على "إلغاء"

#### الخطوة 4: مراجعة النتائج
1. انتقل إلى تبويب "النتائج والتقارير"
2. تصفح الملخص والتفاصيل
3. اعرض الرسوم البيانية
4. صدر التقارير حسب الحاجة

### الميزات المتقدمة في الواجهة

#### معاينة الصور
- عرض الصور مع معلومات مفصلة
- تكبير وتصغير
- معلومات EXIF والبيانات الجغرافية
- تصفح سريع للصور

#### عارض النتائج التفاعلي
- عرض النتائج في تبويبات منظمة
- رسوم بيانية تفاعلية
- تصدير التقارير بتنسيقات متعددة
- بحث وتصفية في النتائج

#### إدارة المشاريع
```python
# حفظ مشروع
project_data = {
    'input_directory': '/path/to/images',
    'output_directory': '/path/to/results',
    'settings': {...},
    'timestamp': '2024-01-01T12:00:00'
}
```

### قوائم الواجهة

#### قائمة "ملف"
- **فتح مشروع**: تحميل مشروع محفوظ
- **حفظ مشروع**: حفظ الإعدادات الحالية
- **تصدير النتائج**: تصدير النتائج الحالية
- **خروج**: إغلاق التطبيق

#### قائمة "أدوات"
- **تدريب YOLO**: فتح واجهة تدريب YOLO
- **محرر التكوين**: تحرير ملفات التكوين
- **عارض السجلات**: مراجعة سجلات النظام

#### قائمة "مساعدة"
- **دليل المستخدم**: عرض دليل الاستخدام
- **حول التطبيق**: معلومات التطبيق والإصدار

---

## 🔧 استكشاف الأخطاء

### الأخطاء الشائعة وحلولها

#### 1. خطأ في استيراد المكتبات
```
ImportError: No module named 'torch'
```

**الحل:**
```bash
pip install torch torchvision
# أو للـ CUDA
pip install torch torchvision --index-url https://download.pytorch.org/whl/cu118
```

#### 2. خطأ في الذاكرة
```
RuntimeError: CUDA out of memory
```

**الحل:**
- قلل حجم الدفعة (batch_size)
- استخدم صور أصغر
- أغلق التطبيقات الأخرى

```yaml
# في ملف التكوين
processing:
  batch_size: 8  # بدلاً من 16
  target_size: [256, 256]  # بدلاً من [512, 512]
```

#### 3. خطأ في مسار الملفات
```
FileNotFoundError: [Errno 2] No such file or directory
```

**الحل:**
- تأكد من صحة مسارات الملفات
- استخدم مسارات مطلقة
- تحقق من أذونات الملفات

#### 4. خطأ في تنسيق الصور
```
PIL.UnidentifiedImageError: cannot identify image file
```

**الحل:**
- تأكد من أن الملفات صور صحيحة
- احذف الملفات التالفة
- استخدم تنسيقات مدعومة (JPEG, PNG, TIFF)

### تشخيص المشاكل

#### فحص النظام
```bash
# فحص إصدار Python
python --version

# فحص المكتبات المثبتة
pip list | grep -E "(torch|ultralytics|opencv|PIL)"

# فحص CUDA
python -c "import torch; print(f'CUDA available: {torch.cuda.is_available()}')"
```

#### فحص ملفات السجل
```bash
# عرض آخر 50 سطر من السجل
tail -50 logs/rescue_processing.log

# البحث عن أخطاء
grep -i "error" logs/rescue_processing.log
```

#### اختبار المكونات
```bash
# اختبار سريع
python simple_tactical_test.py

# اختبار شامل
python test_tactical_analytics.py

# اختبار مكون محدد
python -c "from src.rescue.image_scanner import RescueImageScanner; print('Scanner OK')"
```

### تحسين الأداء

#### إعدادات الأداء المثلى
```yaml
# للأجهزة القوية
performance_settings:
  batch_size: 32
  workers: 8
  device: 'cuda'
  amp: true

# للأجهزة المتوسطة
performance_settings:
  batch_size: 16
  workers: 4
  device: 'cuda'
  amp: true

# للأجهزة الضعيفة
performance_settings:
  batch_size: 4
  workers: 2
  device: 'cpu'
  amp: false
```

#### مراقبة استخدام الموارد
```bash
# مراقبة استخدام GPU
nvidia-smi -l 1

# مراقبة استخدام CPU والذاكرة
htop

# مراقبة استخدام القرص
df -h
```

### الحصول على المساعدة

#### تجميع معلومات النظام
```bash
python -c "
import sys, torch, platform
print(f'Python: {sys.version}')
print(f'Platform: {platform.platform()}')
print(f'PyTorch: {torch.__version__}')
print(f'CUDA: {torch.cuda.is_available()}')
"
```

#### إنشاء تقرير خطأ
```bash
# تشغيل مع تسجيل مفصل
python rescue_main.py --input ./test_images --output ./test_results --verbose --log-level DEBUG
```

#### قنوات الدعم
- **GitHub Issues**: للأخطاء والطلبات
- **المنتدى**: للأسئلة العامة
- **البريد الإلكتروني**: للدعم المباشر

---

## 🤝 المساهمة والتطوير

### المساهمة في التطوير

نرحب بمساهماتكم في تطوير تطبيق الإنقاذ والبحث! إليكم كيفية المساهمة:

#### 1. إعداد بيئة التطوير
```bash
# استنساخ المستودع
git clone https://github.com/your-repo/rescue-search-app.git
cd rescue-search-app

# إنشاء فرع للتطوير
git checkout -b feature/new-feature

# تثبيت متطلبات التطوير
pip install -r requirements-dev.txt
```

#### 2. معايير الكود
- **PEP 8**: اتباع معايير Python
- **Type Hints**: استخدام تلميحات الأنواع
- **Docstrings**: توثيق شامل للدوال والفئات
- **التعليقات**: تعليقات باللغة العربية والإنجليزية

```python
def process_rescue_image(image_path: str, config: Dict[str, Any]) -> Dict[str, Any]:
    """
    معالجة صورة إنقاذ واحدة
    Process a single rescue image

    Args:
        image_path: مسار الصورة
        config: إعدادات المعالجة

    Returns:
        نتائج المعالجة والتصنيف
    """
    pass
```

#### 3. الاختبارات
```bash
# تشغيل جميع الاختبارات
python -m pytest tests/

# تشغيل اختبارات محددة
python -m pytest tests/test_image_processing.py

# تشغيل مع تغطية الكود
python -m pytest --cov=src tests/
```

#### 4. إرسال المساهمة
```bash
# إضافة التغييرات
git add .
git commit -m "feat: إضافة ميزة جديدة للكشف المتقدم"

# دفع التغييرات
git push origin feature/new-feature

# إنشاء Pull Request
```

### هيكل المشروع للمطورين

```
rescue-search-app/
├── src/                          # الكود المصدري
│   ├── rescue/                   # الوحدة الرئيسية
│   │   ├── __init__.py
│   │   ├── image_scanner.py      # مسح الصور
│   │   ├── image_processor.py    # معالجة الصور
│   │   ├── classifier.py         # التصنيف
│   │   ├── yolo_training_module.py # تدريب YOLO
│   │   ├── tactical_analytics.py # التحليلات التكتيكية
│   │   ├── enhanced_gui.py       # الواجهة الرسومية
│   │   └── utils/                # أدوات مساعدة
│   └── config/                   # ملفات التكوين
├── tests/                        # الاختبارات
│   ├── unit/                     # اختبارات الوحدة
│   ├── integration/              # اختبارات التكامل
│   └── fixtures/                 # بيانات الاختبار
├── docs/                         # التوثيق
│   ├── api/                      # توثيق API
│   ├── tutorials/                # دروس تعليمية
│   └── examples/                 # أمثلة
├── scripts/                      # سكريبتات مساعدة
├── requirements/                 # ملفات المتطلبات
│   ├── base.txt                  # متطلبات أساسية
│   ├── dev.txt                   # متطلبات التطوير
│   └── prod.txt                  # متطلبات الإنتاج
└── configs/                      # ملفات التكوين النموذجية
```

### إضافة ميزات جديدة

#### إضافة خوارزمية معالجة جديدة
```python
# في src/rescue/image_processor.py
class AdvancedImageProcessor:
    def add_new_enhancement_method(self, image: np.ndarray, **kwargs) -> np.ndarray:
        """
        إضافة طريقة تحسين جديدة
        Add new enhancement method
        """
        # تنفيذ الخوارزمية الجديدة
        enhanced_image = self._apply_new_algorithm(image, **kwargs)
        return enhanced_image
```

#### إضافة نوع تحليل جديد
```python
# في src/rescue/tactical_analytics.py
class TacticalAnalytics:
    def add_new_analysis_type(self, data: Dict) -> Dict:
        """
        إضافة نوع تحليل جديد
        Add new analysis type
        """
        analysis_results = {
            'analysis_type': 'new_analysis',
            'results': self._perform_new_analysis(data),
            'insights': self._generate_insights(data)
        }
        return analysis_results
```

### إرشادات التطوير

#### أفضل الممارسات
1. **الأمان**: تحقق من المدخلات وتعامل مع الأخطاء
2. **الأداء**: استخدم المعالجة المتوازية عند الإمكان
3. **القابلية للصيانة**: كود واضح ومنظم
4. **التوافق**: دعم إصدارات Python المختلفة

#### نمط التطوير
```python
# نمط للدوال الجديدة
def new_rescue_function(
    input_data: Union[str, Path, np.ndarray],
    config: Optional[Dict[str, Any]] = None,
    verbose: bool = False
) -> Dict[str, Any]:
    """
    وصف الدالة باللغة العربية
    English description of the function

    Args:
        input_data: البيانات المدخلة
        config: إعدادات اختيارية
        verbose: طباعة تفاصيل إضافية

    Returns:
        نتائج المعالجة

    Raises:
        ValueError: في حالة بيانات غير صحيحة
        ProcessingError: في حالة فشل المعالجة
    """
    try:
        # التحقق من المدخلات
        if not input_data:
            raise ValueError("البيانات المدخلة مطلوبة")

        # المعالجة الرئيسية
        results = process_main_logic(input_data, config)

        # التحقق من النتائج
        if not validate_results(results):
            raise ProcessingError("فشل في التحقق من النتائج")

        return results

    except Exception as e:
        logger.error(f"خطأ في {new_rescue_function.__name__}: {e}")
        raise
```

---

## ❓ الأسئلة الشائعة

### الأسئلة العامة

#### س: ما هي متطلبات النظام الدنيا؟
**ج:**
- Python 3.8+
- 8 GB RAM
- 10 GB مساحة قرص
- معالج رسوم NVIDIA (مستحسن)

#### س: هل يمكن استخدام التطبيق بدون GPU؟
**ج:** نعم، يمكن تشغيل التطبيق على CPU ولكن سيكون أبطأ. للحصول على أفضل أداء، يُنصح باستخدام GPU مع CUDA.

#### س: ما هي تنسيقات الصور المدعومة؟
**ج:** JPEG, PNG, TIFF, BMP, GeoTIFF وتنسيقات أخرى مدعومة من PIL/Pillow.

### أسئلة التثبيت

#### س: كيف أحل مشكلة "ModuleNotFoundError"؟
**ج:**
```bash
# تأكد من تثبيت جميع المتطلبات
pip install -r rescue_requirements.txt

# أو تثبيت المكتبة المفقودة مباشرة
pip install [اسم_المكتبة]
```

#### س: كيف أتحقق من تثبيت CUDA؟
**ج:**
```bash
python -c "import torch; print(torch.cuda.is_available())"
```

### أسئلة الاستخدام

#### س: كيف أحسن أداء المعالجة؟
**ج:**
- استخدم GPU إذا كان متوفراً
- قلل حجم الدفعة إذا كانت الذاكرة محدودة
- استخدم صور بحجم مناسب (512×512 مثلاً)

#### س: كيف أضيف فئات جديدة للكشف؟
**ج:** عدل ملف التكوين:
```yaml
specialized_classification:
  custom_rescue_categories:
    - 'new_category_1'
    - 'new_category_2'
```

#### س: كيف أصدر النتائج لأنظمة أخرى؟
**ج:** التطبيق يدعم تصدير:
- JSON للتكامل مع APIs
- CSV لجداول البيانات
- YOLO format للتدريب
- قواعد البيانات (PostgreSQL, MySQL)

### أسئلة التدريب

#### س: كم من الوقت يستغرق تدريب نموذج YOLO؟
**ج:** يعتمد على:
- حجم مجموعة البيانات
- إصدار YOLO المختار
- قوة الجهاز
- عدد الحقب

عادة من 30 دقيقة إلى عدة ساعات.

#### س: كيف أحسن دقة النموذج؟
**ج:**
- استخدم مجموعة بيانات أكبر ومتنوعة
- زد عدد حقب التدريب
- استخدم تقنيات Data Augmentation
- اضبط معاملات التدريب

### أسئلة التحليلات

#### س: ما هي التحليلات التكتيكية؟
**ج:** تحليلات متخصصة تشمل:
- التحليل المكاني للكشوفات
- التحليل الزمني للأنماط
- تحليل الكفاءة التشغيلية
- توليد رؤى تكتيكية

#### س: كيف أفسر نتائج التحليلات؟
**ج:** راجع قسم [التحليلات التكتيكية](#التحليلات-التكتيكية) في هذا الدليل للحصول على شرح مفصل.

### أسئلة الدعم

#### س: أين أجد المساعدة إذا واجهت مشكلة؟
**ج:**
1. راجع قسم [استكشاف الأخطاء](#استكشاف-الأخطاء)
2. ابحث في GitHub Issues
3. اطرح سؤالاً في المنتدى
4. راسلنا مباشرة للدعم العاجل

#### س: كيف أبلغ عن خطأ؟
**ج:**
1. تأكد من أن الخطأ قابل للتكرار
2. اجمع معلومات النظام
3. أنشئ GitHub Issue مع:
   - وصف المشكلة
   - خطوات إعادة الإنتاج
   - رسائل الخطأ
   - معلومات النظام

---

## 📚 مراجع إضافية

### الوثائق التقنية
- [API Reference](docs/api/README.md)
- [Architecture Guide](docs/architecture.md)
- [Performance Tuning](docs/performance.md)

### دروس تعليمية
- [البدء السريع](docs/tutorials/quick-start.md)
- [التدريب المتقدم](docs/tutorials/advanced-training.md)
- [التكامل مع الأنظمة](docs/tutorials/system-integration.md)

### أمثلة عملية
- [معالجة صور البحر](examples/sea-rescue-processing.py)
- [تدريب نموذج مخصص](examples/custom-model-training.py)
- [تحليل البيانات الجغرافية](examples/geospatial-analysis.py)

### المجتمع والدعم
- **GitHub**: [github.com/rescue-search-app](https://github.com/rescue-search-app)
- **المنتدى**: [forum.rescue-search-app.org](https://forum.rescue-search-app.org)
- **البريد الإلكتروني**: <EMAIL>
- **التوثيق**: [docs.rescue-search-app.org](https://docs.rescue-search-app.org)

---

## 📄 الترخيص والحقوق

هذا التطبيق مطور لأغراض الإنقاذ والبحث الإنسانية. يرجى مراجعة ملف LICENSE للحصول على تفاصيل الترخيص.

**حقوق الطبع والنشر © 2024 فريق تطبيق الإنقاذ والبحث المتقدم**

---

*آخر تحديث: يناير 2024*
*الإصدار: 2.0.0*