#!/usr/bin/env python3
"""
تشغيل سريع لتطبيق الإنقاذ والبحث
Quick Start for Search and Rescue Application
============================================

سكريبت تشغيل سريع لاختبار وتشغيل تطبيق معالجة وتصنيف صور الإنقاذ والبحث
مع إعدادات محسنة للاستخدام السريع والفعال.
"""

import os
import sys
import shutil
from pathlib import Path
import argparse

def create_sample_directory_structure():
    """إنشاء هيكل دليل عينة للاختبار"""
    print("🏗️  إنشاء هيكل دليل عينة...")
    
    # إنشاء دلائل العينة
    sample_dirs = [
        'sample_rescue_images/sea_images',
        'sample_rescue_images/desert_images', 
        'sample_rescue_images/coast_images',
        'sample_rescue_images/mixed_images',
        'sample_output'
    ]
    
    for dir_path in sample_dirs:
        Path(dir_path).mkdir(parents=True, exist_ok=True)
    
    print("✅ تم إنشاء هيكل الدليل العينة")
    return 'sample_rescue_images'

def create_quick_config():
    """إنشاء ملف تكوين سريع للاختبار"""
    print("⚙️  إنشاء ملف تكوين سريع...")
    
    quick_config = """# تكوين سريع لتطبيق الإنقاذ والبحث
# Quick Configuration for Search and Rescue Application

# الإعدادات الأساسية
input_directory: './sample_rescue_images'
output_directory: './sample_output'
log_level: 'INFO'
target_size: [256, 256]  # حجم أصغر للاختبار السريع
batch_size: 8

# إعدادات المعالجة السريعة
advanced_preprocessing:
  target_size: [256, 256]
  enable_dehazing: true
  enable_low_light_enhancement: true
  enable_color_correction: false  # تعطيل للسرعة
  enable_glare_removal: false     # تعطيل للسرعة
  brightness_factor: 1.1
  contrast_factor: 1.2
  normalize_images: true

# إعدادات التصنيف السريع
specialized_classification:
  environment_model: 'resnet50'
  rescue_detection_model: 'yolov8n'  # أسرع نموذج
  rescue_confidence_threshold: 0.6
  enable_human_verification: false   # تعطيل للسرعة
  device: 'auto'

# إعدادات إدارة البيانات
smart_data_management:
  create_yolo_dataset: true
  enable_deduplication: false  # تعطيل للسرعة
  export_formats: ['csv', 'json']

# إعدادات التحليلات
tactical_analytics:
  enable_statistical_analysis: true
  enable_heatmaps: true
  enable_interactive_plots: false  # تعطيل للسرعة
  export_formats: ['png']
"""
    
    config_path = Path('quick_rescue_config.yaml')
    with open(config_path, 'w', encoding='utf-8') as f:
        f.write(quick_config)
    
    print(f"✅ تم إنشاء ملف التكوين السريع: {config_path}")
    return str(config_path)

def download_sample_images():
    """تحميل أو إنشاء صور عينة للاختبار"""
    print("📸 إعداد صور العينة...")
    
    try:
        import numpy as np
        from PIL import Image
        
        # إنشاء صور عينة بسيطة للاختبار
        sample_images = [
            ('sample_rescue_images/sea_images/sea_sample_1.jpg', (400, 600, 3), [50, 100, 200]),
            ('sample_rescue_images/sea_images/sea_sample_2.jpg', (500, 700, 3), [60, 120, 220]),
            ('sample_rescue_images/desert_images/desert_sample_1.jpg', (450, 650, 3), [200, 180, 100]),
            ('sample_rescue_images/desert_images/desert_sample_2.jpg', (480, 680, 3), [220, 200, 120]),
            ('sample_rescue_images/coast_images/coast_sample_1.jpg', (420, 620, 3), [150, 180, 200]),
            ('sample_rescue_images/coast_images/coast_sample_2.jpg', (460, 660, 3), [170, 200, 220]),
        ]
        
        for img_path, shape, base_color in sample_images:
            # إنشاء صورة عشوائية بألوان أساسية مناسبة للبيئة
            img_array = np.random.randint(
                max(0, base_color[0] - 50), min(255, base_color[0] + 50), shape, dtype=np.uint8
            )
            img_array[:, :, 1] = np.random.randint(
                max(0, base_color[1] - 50), min(255, base_color[1] + 50), shape[:2], dtype=np.uint8
            )
            img_array[:, :, 2] = np.random.randint(
                max(0, base_color[2] - 50), min(255, base_color[2] + 50), shape[:2], dtype=np.uint8
            )
            
            img = Image.fromarray(img_array)
            img.save(img_path, quality=85)
        
        print(f"✅ تم إنشاء {len(sample_images)} صورة عينة")
        
    except ImportError:
        print("⚠️  لم يتم العثور على PIL، يرجى وضع صور عينة في المجلدات يدوياً")
        print("   أو تثبيت Pillow: pip install Pillow")

def check_dependencies():
    """فحص المتطلبات الأساسية"""
    print("🔍 فحص المتطلبات...")
    
    required_packages = [
        'opencv-python',
        'numpy', 
        'pandas',
        'matplotlib',
        'PyYAML',
        'tqdm'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("❌ المتطلبات المفقودة:")
        for package in missing_packages:
            print(f"   - {package}")
        print("\n💡 لتثبيت المتطلبات:")
        print("   pip install " + " ".join(missing_packages))
        return False
    
    print("✅ جميع المتطلبات الأساسية متوفرة")
    return True

def run_quick_demo():
    """تشغيل عرض توضيحي سريع"""
    print("\n🚀 تشغيل العرض التوضيحي السريع...")
    
    # التحقق من وجود الملف الرئيسي
    if not Path('rescue_main.py').exists():
        print("❌ لم يتم العثور على rescue_main.py")
        print("   تأكد من وجودك في دليل التطبيق الصحيح")
        return False
    
    # تشغيل التطبيق
    import subprocess
    
    cmd = [
        sys.executable, 'rescue_main.py',
        '--input', 'sample_rescue_images',
        '--output', 'sample_output',
        '--config', 'quick_rescue_config.yaml',
        '--verbose'
    ]
    
    print(f"🔧 تشغيل الأمر: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')
        
        if result.returncode == 0:
            print("✅ تم تشغيل العرض التوضيحي بنجاح!")
            print("\n📊 النتائج:")
            print(result.stdout)
        else:
            print("❌ فشل في تشغيل العرض التوضيحي")
            print("الأخطاء:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ خطأ في تشغيل العرض التوضيحي: {e}")
        return False
    
    return True

def show_results():
    """عرض النتائج المولدة"""
    print("\n📋 عرض النتائج...")
    
    output_dir = Path('sample_output')
    if not output_dir.exists():
        print("❌ لم يتم العثور على دليل النتائج")
        return
    
    # عرض الملفات المولدة
    generated_files = list(output_dir.rglob('*'))
    
    if generated_files:
        print("📁 الملفات المولدة:")
        for file_path in sorted(generated_files):
            if file_path.is_file():
                size = file_path.stat().st_size
                print(f"   📄 {file_path.relative_to(output_dir)} ({size:,} bytes)")
    else:
        print("⚠️  لم يتم العثور على ملفات مولدة")

def cleanup():
    """تنظيف الملفات المؤقتة"""
    print("\n🧹 تنظيف الملفات المؤقتة...")
    
    cleanup_paths = [
        'sample_rescue_images',
        'sample_output', 
        'quick_rescue_config.yaml',
        'logs'
    ]
    
    for path in cleanup_paths:
        path_obj = Path(path)
        if path_obj.exists():
            if path_obj.is_dir():
                shutil.rmtree(path_obj)
            else:
                path_obj.unlink()
            print(f"   🗑️  تم حذف: {path}")
    
    print("✅ تم التنظيف")

def main():
    """الدالة الرئيسية للتشغيل السريع"""
    parser = argparse.ArgumentParser(
        description='تشغيل سريع لتطبيق الإنقاذ والبحث',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
أمثلة الاستخدام:
================

1. تشغيل العرض التوضيحي الكامل:
   python quick_start_rescue.py --demo

2. إعداد البيئة فقط:
   python quick_start_rescue.py --setup-only

3. تنظيف الملفات المؤقتة:
   python quick_start_rescue.py --cleanup

4. فحص المتطلبات فقط:
   python quick_start_rescue.py --check-deps
        """
    )
    
    parser.add_argument(
        '--demo',
        action='store_true',
        help='تشغيل العرض التوضيحي الكامل'
    )
    
    parser.add_argument(
        '--setup-only',
        action='store_true', 
        help='إعداد البيئة فقط بدون تشغيل'
    )
    
    parser.add_argument(
        '--cleanup',
        action='store_true',
        help='تنظيف الملفات المؤقتة'
    )
    
    parser.add_argument(
        '--check-deps',
        action='store_true',
        help='فحص المتطلبات فقط'
    )
    
    args = parser.parse_args()
    
    print("🚁 تشغيل سريع لتطبيق الإنقاذ والبحث")
    print("=" * 50)
    
    try:
        if args.cleanup:
            cleanup()
            return 0
        
        if args.check_deps:
            if check_dependencies():
                print("✅ جميع المتطلبات متوفرة")
                return 0
            else:
                print("❌ بعض المتطلبات مفقودة")
                return 1
        
        # فحص المتطلبات
        if not check_dependencies():
            print("⚠️  يرجى تثبيت المتطلبات المفقودة أولاً")
            return 1
        
        # إعداد البيئة
        input_dir = create_sample_directory_structure()
        config_file = create_quick_config()
        download_sample_images()
        
        if args.setup_only:
            print("✅ تم إعداد البيئة بنجاح")
            print(f"📁 دليل الصور: {input_dir}")
            print(f"⚙️  ملف التكوين: {config_file}")
            print("\n💡 لتشغيل التطبيق:")
            print("   python rescue_main.py --input sample_rescue_images --output sample_output --config quick_rescue_config.yaml")
            return 0
        
        # تشغيل العرض التوضيحي
        if args.demo or True:  # افتراضي
            if run_quick_demo():
                show_results()
                
                print("\n🎉 تم إنجاز العرض التوضيحي بنجاح!")
                print("\n📚 الخطوات التالية:")
                print("   1. راجع النتائج في دليل sample_output")
                print("   2. اقرأ ملف RESCUE_README.md للمزيد من التفاصيل")
                print("   3. استخدم صورك الحقيقية مع التطبيق")
                print("   4. خصص ملف التكوين حسب احتياجاتك")
                
                return 0
            else:
                print("❌ فشل في تشغيل العرض التوضيحي")
                return 1
        
    except KeyboardInterrupt:
        print("\n⚠️  تم إيقاف العملية بواسطة المستخدم")
        return 1
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
