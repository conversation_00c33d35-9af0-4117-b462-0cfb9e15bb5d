"""
Configuration Editor GUI
========================

A dedicated GUI for editing configuration files.
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import yaml
import json
from pathlib import Path


class ConfigEditorDialog:
    """Configuration editor dialog."""
    
    def __init__(self, parent, config_data=None):
        self.parent = parent
        self.config_data = config_data or {}
        self.result = None
        
        # Create dialog window
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("Configuration Editor")
        self.dialog.geometry("600x500")
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        self.setup_gui()
        
        # Center dialog
        self.dialog.update_idletasks()
        x = parent.winfo_x() + (parent.winfo_width() // 2) - (self.dialog.winfo_width() // 2)
        y = parent.winfo_y() + (parent.winfo_height() // 2) - (self.dialog.winfo_height() // 2)
        self.dialog.geometry(f"+{x}+{y}")
    
    def setup_gui(self):
        """Setup the configuration editor GUI."""
        # Main frame
        main_frame = ttk.Frame(self.dialog, padding=10)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Configuration sections
        self.create_processing_section(main_frame)
        self.create_classification_section(main_frame)
        self.create_dataset_section(main_frame)
        
        # Buttons
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(10, 0))
        
        ttk.Button(button_frame, text="OK", command=self.ok_clicked).pack(side=tk.RIGHT, padx=5)
        ttk.Button(button_frame, text="Cancel", command=self.cancel_clicked).pack(side=tk.RIGHT)
        ttk.Button(button_frame, text="Load File", command=self.load_file).pack(side=tk.LEFT)
        ttk.Button(button_frame, text="Save File", command=self.save_file).pack(side=tk.LEFT, padx=5)
    
    def create_processing_section(self, parent):
        """Create processing settings section."""
        frame = ttk.LabelFrame(parent, text="Processing Settings", padding=5)
        frame.pack(fill=tk.X, pady=5)
        
        # Target size
        size_frame = ttk.Frame(frame)
        size_frame.pack(fill=tk.X, pady=2)
        ttk.Label(size_frame, text="Target Size:").pack(side=tk.LEFT)
        
        self.target_width = tk.IntVar(value=self.config_data.get('target_size', [224, 224])[0])
        self.target_height = tk.IntVar(value=self.config_data.get('target_size', [224, 224])[1])
        
        ttk.Entry(size_frame, textvariable=self.target_width, width=8).pack(side=tk.LEFT, padx=5)
        ttk.Label(size_frame, text="x").pack(side=tk.LEFT)
        ttk.Entry(size_frame, textvariable=self.target_height, width=8).pack(side=tk.LEFT, padx=5)
        
        # Checkboxes
        self.enhance_quality = tk.BooleanVar(value=self.config_data.get('enhance_quality', True))
        self.normalize = tk.BooleanVar(value=self.config_data.get('normalize', True))
        self.convert_grayscale = tk.BooleanVar(value=self.config_data.get('convert_to_grayscale', False))
        
        ttk.Checkbutton(frame, text="Enhance Quality", variable=self.enhance_quality).pack(anchor=tk.W)
        ttk.Checkbutton(frame, text="Normalize", variable=self.normalize).pack(anchor=tk.W)
        ttk.Checkbutton(frame, text="Convert to Grayscale", variable=self.convert_grayscale).pack(anchor=tk.W)
    
    def create_classification_section(self, parent):
        """Create classification settings section."""
        frame = ttk.LabelFrame(parent, text="Classification Settings", padding=5)
        frame.pack(fill=tk.X, pady=5)
        
        # Confidence threshold
        conf_frame = ttk.Frame(frame)
        conf_frame.pack(fill=tk.X, pady=2)
        ttk.Label(conf_frame, text="Confidence Threshold:").pack(side=tk.LEFT)
        
        self.confidence_threshold = tk.DoubleVar(value=self.config_data.get('confidence_threshold', 0.5))
        ttk.Entry(conf_frame, textvariable=self.confidence_threshold, width=10).pack(side=tk.LEFT, padx=5)
        
        # Custom categories
        ttk.Label(frame, text="Custom Categories (one per line):").pack(anchor=tk.W, pady=(5,0))
        self.categories_text = tk.Text(frame, height=4, width=50)
        self.categories_text.pack(fill=tk.X, pady=2)
        
        # Load existing categories
        categories = self.config_data.get('custom_categories', [])
        if categories:
            self.categories_text.insert(1.0, '\n'.join(categories))
    
    def create_dataset_section(self, parent):
        """Create dataset settings section."""
        frame = ttk.LabelFrame(parent, text="Dataset Settings", padding=5)
        frame.pack(fill=tk.X, pady=5)
        
        # Split ratios
        split_frame = ttk.Frame(frame)
        split_frame.pack(fill=tk.X, pady=2)
        ttk.Label(split_frame, text="Split Ratios - Train:").pack(side=tk.LEFT)
        
        split_ratios = self.config_data.get('split_ratios', [0.7, 0.2, 0.1])
        self.train_ratio = tk.DoubleVar(value=split_ratios[0])
        self.val_ratio = tk.DoubleVar(value=split_ratios[1])
        self.test_ratio = tk.DoubleVar(value=split_ratios[2])
        
        ttk.Entry(split_frame, textvariable=self.train_ratio, width=8).pack(side=tk.LEFT, padx=2)
        ttk.Label(split_frame, text="Val:").pack(side=tk.LEFT)
        ttk.Entry(split_frame, textvariable=self.val_ratio, width=8).pack(side=tk.LEFT, padx=2)
        ttk.Label(split_frame, text="Test:").pack(side=tk.LEFT)
        ttk.Entry(split_frame, textvariable=self.test_ratio, width=8).pack(side=tk.LEFT, padx=2)
        
        # Export formats
        export_frame = ttk.Frame(frame)
        export_frame.pack(fill=tk.X, pady=5)
        ttk.Label(export_frame, text="Export Formats:").pack(side=tk.LEFT)
        
        export_formats = self.config_data.get('export_formats', ['csv', 'json'])
        self.export_csv = tk.BooleanVar(value='csv' in export_formats)
        self.export_json = tk.BooleanVar(value='json' in export_formats)
        self.export_yaml = tk.BooleanVar(value='yaml' in export_formats)
        
        ttk.Checkbutton(export_frame, text="CSV", variable=self.export_csv).pack(side=tk.LEFT, padx=5)
        ttk.Checkbutton(export_frame, text="JSON", variable=self.export_json).pack(side=tk.LEFT, padx=5)
        ttk.Checkbutton(export_frame, text="YAML", variable=self.export_yaml).pack(side=tk.LEFT, padx=5)
    
    def get_config(self):
        """Get configuration from GUI elements."""
        # Get custom categories
        categories_text = self.categories_text.get(1.0, tk.END).strip()
        custom_categories = [cat.strip() for cat in categories_text.split('\n') if cat.strip()]
        
        # Get export formats
        export_formats = []
        if self.export_csv.get():
            export_formats.append('csv')
        if self.export_json.get():
            export_formats.append('json')
        if self.export_yaml.get():
            export_formats.append('yaml')
        
        config = {
            'target_size': [self.target_width.get(), self.target_height.get()],
            'enhance_quality': self.enhance_quality.get(),
            'normalize': self.normalize.get(),
            'convert_to_grayscale': self.convert_grayscale.get(),
            'confidence_threshold': self.confidence_threshold.get(),
            'custom_categories': custom_categories,
            'split_ratios': [self.train_ratio.get(), self.val_ratio.get(), self.test_ratio.get()],
            'export_formats': export_formats
        }
        
        return config
    
    def load_file(self):
        """Load configuration from file."""
        filename = filedialog.askopenfilename(
            title="Load Configuration",
            filetypes=[("YAML files", "*.yaml *.yml"), ("JSON files", "*.json")]
        )
        if filename:
            try:
                with open(filename, 'r') as f:
                    if filename.endswith('.json'):
                        config = json.load(f)
                    else:
                        config = yaml.safe_load(f)
                
                self.config_data = config
                self.update_gui_from_config()
                messagebox.showinfo("Success", "Configuration loaded successfully!")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to load configuration: {e}")
    
    def save_file(self):
        """Save configuration to file."""
        config = self.get_config()
        filename = filedialog.asksaveasfilename(
            title="Save Configuration",
            defaultextension=".yaml",
            filetypes=[("YAML files", "*.yaml"), ("JSON files", "*.json")]
        )
        if filename:
            try:
                with open(filename, 'w') as f:
                    if filename.endswith('.json'):
                        json.dump(config, f, indent=2)
                    else:
                        yaml.dump(config, f, default_flow_style=False)
                
                messagebox.showinfo("Success", "Configuration saved successfully!")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to save configuration: {e}")
    
    def update_gui_from_config(self):
        """Update GUI elements from loaded configuration."""
        target_size = self.config_data.get('target_size', [224, 224])
        self.target_width.set(target_size[0])
        self.target_height.set(target_size[1])
        
        self.enhance_quality.set(self.config_data.get('enhance_quality', True))
        self.normalize.set(self.config_data.get('normalize', True))
        self.convert_grayscale.set(self.config_data.get('convert_to_grayscale', False))
        self.confidence_threshold.set(self.config_data.get('confidence_threshold', 0.5))
        
        # Update categories
        categories = self.config_data.get('custom_categories', [])
        self.categories_text.delete(1.0, tk.END)
        if categories:
            self.categories_text.insert(1.0, '\n'.join(categories))
        
        # Update split ratios
        split_ratios = self.config_data.get('split_ratios', [0.7, 0.2, 0.1])
        self.train_ratio.set(split_ratios[0])
        self.val_ratio.set(split_ratios[1])
        self.test_ratio.set(split_ratios[2])
        
        # Update export formats
        export_formats = self.config_data.get('export_formats', ['csv', 'json'])
        self.export_csv.set('csv' in export_formats)
        self.export_json.set('json' in export_formats)
        self.export_yaml.set('yaml' in export_formats)
    
    def ok_clicked(self):
        """Handle OK button click."""
        self.result = self.get_config()
        self.dialog.destroy()
    
    def cancel_clicked(self):
        """Handle Cancel button click."""
        self.result = None
        self.dialog.destroy()
    
    def show(self):
        """Show dialog and return result."""
        self.dialog.wait_window()
        return self.result
