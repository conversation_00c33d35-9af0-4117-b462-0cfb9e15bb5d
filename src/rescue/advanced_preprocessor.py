"""
خط أنابيب المعالجة المسبقة المتخصص للإنقاذ والبحث
Advanced Preprocessing Pipeline for Search and Rescue Operations
==============================================================

معالجة متقدمة للصور مصممة خصيصاً لتحسين رؤية الأهداف في الظروف الصعبة
مثل الإضاءة المنخفضة، الضباب، الانعكاسات، والظروف الجوية السيئة.
"""

import cv2
import numpy as np
import logging
from typing import Dict, Tuple, Optional, List
from pathlib import Path
from PIL import Image, ImageEnhance, ImageFilter
import skimage
from skimage import restoration, filters, exposure, morphology
from skimage.restoration import denoise_bilateral, denoise_nl_means
from skimage.filters import unsharp_mask
from skimage.exposure import equalize_adapthist
import warnings

# تجاهل التحذيرات غير المهمة
warnings.filterwarnings('ignore', category=UserWarning)


class AdvancedRescuePreprocessor:
    """
    معالج متقدم للصور المتخصص في عمليات الإنقاذ والبحث
    Advanced image processor specialized for search and rescue operations
    """
    
    def __init__(self, config: Dict):
        """
        تهيئة المعالج المتقدم
        
        Args:
            config: إعدادات التكوين
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # إعدادات المعالجة
        self.processing_settings = self._load_processing_settings()
        
        # إحصائيات المعالجة
        self.processing_stats = {
            'processed_images': 0,
            'failed_images': 0,
            'enhancement_applied': {},
            'processing_times': []
        }
    
    def _load_processing_settings(self) -> Dict:
        """تحميل إعدادات المعالجة من التكوين"""
        return {
            # إعدادات تغيير الحجم
            'target_size': self.config.get('target_size', [512, 512]),
            'preserve_aspect_ratio': self.config.get('preserve_aspect_ratio', True),
            'interpolation_method': cv2.INTER_LANCZOS4,
            
            # إعدادات تحسين الرؤية
            'brightness_factor': self.config.get('brightness_factor', 1.2),
            'contrast_factor': self.config.get('contrast_factor', 1.3),
            'saturation_factor': self.config.get('saturation_factor', 1.1),
            'sharpness_factor': self.config.get('sharpness_factor', 1.2),
            
            # إعدادات تقليل الضوضاء
            'denoise_method': self.config.get('denoise_method', 'bilateral'),
            'bilateral_d': self.config.get('bilateral_d', 9),
            'bilateral_sigma_color': self.config.get('bilateral_sigma_color', 75),
            'bilateral_sigma_space': self.config.get('bilateral_sigma_space', 75),
            
            # إعدادات إزالة الضباب
            'enable_dehazing': self.config.get('enable_dehazing', True),
            'dark_channel_omega': self.config.get('dark_channel_omega', 0.95),
            'atmospheric_light_percentile': self.config.get('atmospheric_light_percentile', 0.1),
            
            # إعدادات تحسين الإضاءة المنخفضة
            'enable_low_light_enhancement': self.config.get('enable_low_light_enhancement', True),
            'clahe_clip_limit': self.config.get('clahe_clip_limit', 3.0),
            'clahe_tile_grid_size': self.config.get('clahe_tile_grid_size', (8, 8)),
            
            # إعدادات تصحيح الألوان
            'enable_color_correction': self.config.get('enable_color_correction', True),
            'white_balance_method': self.config.get('white_balance_method', 'gray_world'),
            
            # إعدادات إزالة الوهج
            'enable_glare_removal': self.config.get('enable_glare_removal', True),
            'glare_threshold': self.config.get('glare_threshold', 240),
            
            # إعدادات التحويل
            'convert_to_grayscale': self.config.get('convert_to_grayscale', False),
            'normalize_images': self.config.get('normalize_images', True),
            'normalization_method': self.config.get('normalization_method', 'standard')
        }
    
    def process_image(self, image_data: Dict) -> Dict:
        """
        معالجة صورة واحدة بالتحسينات المتقدمة
        
        Args:
            image_data: بيانات الصورة
            
        Returns:
            بيانات الصورة المعالجة
        """
        try:
            start_time = cv2.getTickCount()
            
            # تحميل الصورة
            image_path = Path(image_data['filepath'])
            original_image = cv2.imread(str(image_path))
            
            if original_image is None:
                raise ValueError(f"فشل في تحميل الصورة: {image_path}")
            
            # تحويل إلى RGB
            image_rgb = cv2.cvtColor(original_image, cv2.COLOR_BGR2RGB)
            
            # تطبيق التحسينات المتقدمة
            enhanced_image = self._apply_advanced_enhancements(image_rgb)
            
            # تغيير الحجم الذكي
            resized_image = self._intelligent_resize(enhanced_image)
            
            # التحويلات النهائية
            final_image = self._apply_final_transformations(resized_image)
            
            # حساب وقت المعالجة
            processing_time = (cv2.getTickCount() - start_time) / cv2.getTickFrequency()
            self.processing_stats['processing_times'].append(processing_time)
            
            # تحديث البيانات
            processed_data = image_data.copy()
            processed_data.update({
                'processed_array': final_image,
                'processed_dimensions': final_image.shape[:2],
                'processed': True,
                'enhanced': True,
                'normalized': self.processing_settings['normalize_images'],
                'processing_time': processing_time,
                'enhancements_applied': self._get_applied_enhancements()
            })
            
            self.processing_stats['processed_images'] += 1
            self.logger.debug(f"تمت معالجة الصورة: {image_path.name} في {processing_time:.3f} ثانية")
            
            return processed_data
            
        except Exception as e:
            self.processing_stats['failed_images'] += 1
            self.logger.error(f"فشل في معالجة الصورة {image_data.get('filename', 'unknown')}: {e}")
            raise
    
    def _apply_advanced_enhancements(self, image: np.ndarray) -> np.ndarray:
        """
        تطبيق التحسينات المتقدمة للصورة
        
        Args:
            image: الصورة الأصلية
            
        Returns:
            الصورة المحسنة
        """
        enhanced = image.copy()
        
        # 1. تحسين الإضاءة المنخفضة
        if self.processing_settings['enable_low_light_enhancement']:
            enhanced = self._enhance_low_light(enhanced)
            self._mark_enhancement_applied('low_light_enhancement')
        
        # 2. إزالة الضباب والدخان
        if self.processing_settings['enable_dehazing']:
            enhanced = self._remove_haze(enhanced)
            self._mark_enhancement_applied('dehazing')
        
        # 3. تقليل الضوضاء المتقدم
        enhanced = self._advanced_noise_reduction(enhanced)
        self._mark_enhancement_applied('noise_reduction')
        
        # 4. تصحيح الألوان
        if self.processing_settings['enable_color_correction']:
            enhanced = self._correct_colors(enhanced)
            self._mark_enhancement_applied('color_correction')
        
        # 5. إزالة الوهج والانعكاسات
        if self.processing_settings['enable_glare_removal']:
            enhanced = self._remove_glare_reflections(enhanced)
            self._mark_enhancement_applied('glare_removal')
        
        # 6. تحسين الوضوح
        enhanced = self._enhance_sharpness(enhanced)
        self._mark_enhancement_applied('sharpness_enhancement')
        
        # 7. تحسين التباين والسطوع
        enhanced = self._enhance_brightness_contrast(enhanced)
        self._mark_enhancement_applied('brightness_contrast')
        
        return enhanced
    
    def _enhance_low_light(self, image: np.ndarray) -> np.ndarray:
        """
        تحسين الصور ذات الإضاءة المنخفضة باستخدام CLAHE
        
        Args:
            image: الصورة الأصلية
            
        Returns:
            الصورة المحسنة
        """
        # تحويل إلى LAB للمعالجة الأفضل
        lab = cv2.cvtColor(image, cv2.COLOR_RGB2LAB)
        l_channel, a_channel, b_channel = cv2.split(lab)
        
        # تطبيق CLAHE على قناة الإضاءة
        clahe = cv2.createCLAHE(
            clipLimit=self.processing_settings['clahe_clip_limit'],
            tileGridSize=self.processing_settings['clahe_tile_grid_size']
        )
        l_channel_enhanced = clahe.apply(l_channel)
        
        # دمج القنوات
        lab_enhanced = cv2.merge([l_channel_enhanced, a_channel, b_channel])
        enhanced = cv2.cvtColor(lab_enhanced, cv2.COLOR_LAB2RGB)
        
        return enhanced
    
    def _remove_haze(self, image: np.ndarray) -> np.ndarray:
        """
        إزالة الضباب باستخدام Dark Channel Prior
        
        Args:
            image: الصورة الأصلية
            
        Returns:
            الصورة بدون ضباب
        """
        try:
            # تحويل إلى float
            image_float = image.astype(np.float64) / 255.0
            
            # حساب Dark Channel
            dark_channel = self._calculate_dark_channel(image_float)
            
            # تقدير الضوء الجوي
            atmospheric_light = self._estimate_atmospheric_light(image_float, dark_channel)
            
            # حساب transmission map
            transmission = self._estimate_transmission(image_float, atmospheric_light)
            
            # تطبيق إزالة الضباب
            dehazed = self._recover_scene_radiance(image_float, atmospheric_light, transmission)
            
            # تحويل إلى uint8
            dehazed = np.clip(dehazed * 255, 0, 255).astype(np.uint8)
            
            return dehazed
            
        except Exception as e:
            self.logger.warning(f"فشل في إزالة الضباب: {e}")
            return image
    
    def _calculate_dark_channel(self, image: np.ndarray, patch_size: int = 15) -> np.ndarray:
        """حساب Dark Channel للصورة"""
        min_channel = np.min(image, axis=2)
        kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (patch_size, patch_size))
        dark_channel = cv2.erode(min_channel, kernel)
        return dark_channel
    
    def _estimate_atmospheric_light(self, image: np.ndarray, dark_channel: np.ndarray) -> np.ndarray:
        """تقدير الضوء الجوي"""
        h, w = dark_channel.shape
        num_pixels = int(h * w * self.processing_settings['atmospheric_light_percentile'])
        
        # العثور على أكثر البكسلات سطوعاً في Dark Channel
        dark_flat = dark_channel.flatten()
        indices = np.argpartition(dark_flat, -num_pixels)[-num_pixels:]
        
        # حساب متوسط الضوء الجوي
        atmospheric_light = np.zeros(3)
        for idx in indices:
            y, x = divmod(idx, w)
            atmospheric_light += image[y, x]
        
        atmospheric_light /= num_pixels
        return atmospheric_light
    
    def _estimate_transmission(self, image: np.ndarray, atmospheric_light: np.ndarray) -> np.ndarray:
        """تقدير transmission map"""
        omega = self.processing_settings['dark_channel_omega']
        normalized_image = image / atmospheric_light
        transmission = 1 - omega * self._calculate_dark_channel(normalized_image)
        
        # تطبيق مرشح guided filter لتنعيم transmission
        transmission = cv2.ximgproc.guidedFilter(
            cv2.cvtColor((image * 255).astype(np.uint8), cv2.COLOR_RGB2GRAY),
            (transmission * 255).astype(np.uint8),
            radius=60, eps=0.0001
        ) / 255.0
        
        return transmission
    
    def _recover_scene_radiance(self, image: np.ndarray, atmospheric_light: np.ndarray, 
                               transmission: np.ndarray, t0: float = 0.1) -> np.ndarray:
        """استرداد إشعاع المشهد"""
        transmission = np.maximum(transmission, t0)
        transmission = transmission[:, :, np.newaxis]
        
        scene_radiance = (image - atmospheric_light) / transmission + atmospheric_light
        return np.clip(scene_radiance, 0, 1)
    
    def _advanced_noise_reduction(self, image: np.ndarray) -> np.ndarray:
        """
        تقليل الضوضاء المتقدم
        
        Args:
            image: الصورة الأصلية
            
        Returns:
            الصورة بدون ضوضاء
        """
        method = self.processing_settings['denoise_method']
        
        if method == 'bilateral':
            # Bilateral Filter
            denoised = cv2.bilateralFilter(
                image,
                self.processing_settings['bilateral_d'],
                self.processing_settings['bilateral_sigma_color'],
                self.processing_settings['bilateral_sigma_space']
            )
        elif method == 'nl_means':
            # Non-local Means Denoising
            if len(image.shape) == 3:
                denoised = cv2.fastNlMeansDenoisingColored(image, None, 10, 10, 7, 21)
            else:
                denoised = cv2.fastNlMeansDenoising(image, None, 10, 7, 21)
        else:
            # Gaussian Blur كبديل
            denoised = cv2.GaussianBlur(image, (5, 5), 0)
        
        return denoised
    
    def _correct_colors(self, image: np.ndarray) -> np.ndarray:
        """
        تصحيح الألوان وتوازن الأبيض
        
        Args:
            image: الصورة الأصلية
            
        Returns:
            الصورة مصححة الألوان
        """
        method = self.processing_settings['white_balance_method']
        
        if method == 'gray_world':
            # Gray World Algorithm
            mean_r = np.mean(image[:, :, 0])
            mean_g = np.mean(image[:, :, 1])
            mean_b = np.mean(image[:, :, 2])
            
            gray_mean = (mean_r + mean_g + mean_b) / 3
            
            corrected = image.copy().astype(np.float32)
            corrected[:, :, 0] = corrected[:, :, 0] * (gray_mean / mean_r)
            corrected[:, :, 1] = corrected[:, :, 1] * (gray_mean / mean_g)
            corrected[:, :, 2] = corrected[:, :, 2] * (gray_mean / mean_b)
            
            corrected = np.clip(corrected, 0, 255).astype(np.uint8)
            
        elif method == 'white_patch':
            # White Patch Algorithm
            max_r = np.max(image[:, :, 0])
            max_g = np.max(image[:, :, 1])
            max_b = np.max(image[:, :, 2])
            
            corrected = image.copy().astype(np.float32)
            corrected[:, :, 0] = corrected[:, :, 0] * (255 / max_r)
            corrected[:, :, 1] = corrected[:, :, 1] * (255 / max_g)
            corrected[:, :, 2] = corrected[:, :, 2] * (255 / max_b)
            
            corrected = np.clip(corrected, 0, 255).astype(np.uint8)
        else:
            corrected = image
        
        return corrected
    
    def _remove_glare_reflections(self, image: np.ndarray) -> np.ndarray:
        """
        إزالة الوهج والانعكاسات
        
        Args:
            image: الصورة الأصلية
            
        Returns:
            الصورة بدون وهج
        """
        # تحويل إلى grayscale للكشف عن الوهج
        gray = cv2.cvtColor(image, cv2.COLOR_RGB2GRAY)
        
        # إنشاء mask للمناطق الساطعة جداً
        threshold = self.processing_settings['glare_threshold']
        _, glare_mask = cv2.threshold(gray, threshold, 255, cv2.THRESH_BINARY)
        
        # توسيع المناطق المكتشفة
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5, 5))
        glare_mask = cv2.morphologyEx(glare_mask, cv2.MORPH_DILATE, kernel)
        
        # تطبيق inpainting لإصلاح المناطق المتأثرة
        result = cv2.inpaint(image, glare_mask, 3, cv2.INPAINT_TELEA)
        
        return result
    
    def _enhance_sharpness(self, image: np.ndarray) -> np.ndarray:
        """
        تحسين وضوح الصورة باستخدام Unsharp Masking
        
        Args:
            image: الصورة الأصلية
            
        Returns:
            الصورة المحسنة الوضوح
        """
        # تحويل إلى float للمعالجة
        image_float = image.astype(np.float32) / 255.0
        
        # تطبيق Unsharp Mask
        sharpened = unsharp_mask(
            image_float,
            radius=1.0,
            amount=self.processing_settings['sharpness_factor']
        )
        
        # تحويل إلى uint8
        sharpened = np.clip(sharpened * 255, 0, 255).astype(np.uint8)
        
        return sharpened
    
    def _enhance_brightness_contrast(self, image: np.ndarray) -> np.ndarray:
        """
        تحسين السطوع والتباين
        
        Args:
            image: الصورة الأصلية
            
        Returns:
            الصورة المحسنة
        """
        # تحويل إلى PIL للمعالجة
        pil_image = Image.fromarray(image)
        
        # تحسين السطوع
        brightness_enhancer = ImageEnhance.Brightness(pil_image)
        enhanced = brightness_enhancer.enhance(self.processing_settings['brightness_factor'])
        
        # تحسين التباين
        contrast_enhancer = ImageEnhance.Contrast(enhanced)
        enhanced = contrast_enhancer.enhance(self.processing_settings['contrast_factor'])
        
        # تحسين التشبع
        color_enhancer = ImageEnhance.Color(enhanced)
        enhanced = color_enhancer.enhance(self.processing_settings['saturation_factor'])
        
        # تحويل إلى numpy
        enhanced_array = np.array(enhanced)
        
        return enhanced_array
    
    def _intelligent_resize(self, image: np.ndarray) -> np.ndarray:
        """
        تغيير الحجم الذكي مع الحفاظ على النسبة
        
        Args:
            image: الصورة الأصلية
            
        Returns:
            الصورة مغيرة الحجم
        """
        target_height, target_width = self.processing_settings['target_size']
        current_height, current_width = image.shape[:2]
        
        if self.processing_settings['preserve_aspect_ratio']:
            # حساب النسبة للحفاظ على الأبعاد
            scale = min(target_width / current_width, target_height / current_height)
            new_width = int(current_width * scale)
            new_height = int(current_height * scale)
            
            # تغيير الحجم
            resized = cv2.resize(
                image, (new_width, new_height),
                interpolation=self.processing_settings['interpolation_method']
            )
            
            # إضافة padding للوصول للحجم المطلوب
            delta_w = target_width - new_width
            delta_h = target_height - new_height
            top, bottom = delta_h // 2, delta_h - (delta_h // 2)
            left, right = delta_w // 2, delta_w - (delta_w // 2)
            
            # إضافة حدود سوداء
            resized = cv2.copyMakeBorder(
                resized, top, bottom, left, right,
                cv2.BORDER_CONSTANT, value=[0, 0, 0]
            )
        else:
            # تغيير الحجم المباشر
            resized = cv2.resize(
                image, (target_width, target_height),
                interpolation=self.processing_settings['interpolation_method']
            )
        
        return resized
    
    def _apply_final_transformations(self, image: np.ndarray) -> np.ndarray:
        """
        تطبيق التحويلات النهائية
        
        Args:
            image: الصورة المعالجة
            
        Returns:
            الصورة النهائية
        """
        final_image = image.copy()
        
        # التحويل إلى تدرج رمادي إذا مطلوب
        if self.processing_settings['convert_to_grayscale']:
            final_image = cv2.cvtColor(final_image, cv2.COLOR_RGB2GRAY)
            final_image = np.expand_dims(final_image, axis=2)
        
        # التسوية
        if self.processing_settings['normalize_images']:
            final_image = self._normalize_image(final_image)
        
        return final_image
    
    def _normalize_image(self, image: np.ndarray) -> np.ndarray:
        """
        تسوية الصورة
        
        Args:
            image: الصورة الأصلية
            
        Returns:
            الصورة المسواة
        """
        method = self.processing_settings['normalization_method']
        
        if method == 'standard':
            # التسوية القياسية (0-1)
            normalized = image.astype(np.float32) / 255.0
        elif method == 'z_score':
            # Z-score normalization
            mean = np.mean(image)
            std = np.std(image)
            normalized = (image - mean) / (std + 1e-8)
        else:
            normalized = image.astype(np.float32)
        
        return normalized
    
    def _mark_enhancement_applied(self, enhancement_name: str):
        """تسجيل التحسين المطبق"""
        if enhancement_name not in self.processing_stats['enhancement_applied']:
            self.processing_stats['enhancement_applied'][enhancement_name] = 0
        self.processing_stats['enhancement_applied'][enhancement_name] += 1
    
    def _get_applied_enhancements(self) -> List[str]:
        """الحصول على قائمة التحسينات المطبقة"""
        return list(self.processing_stats['enhancement_applied'].keys())
    
    def get_processing_statistics(self) -> Dict:
        """الحصول على إحصائيات المعالجة"""
        stats = self.processing_stats.copy()
        if stats['processing_times']:
            stats['average_processing_time'] = np.mean(stats['processing_times'])
            stats['total_processing_time'] = np.sum(stats['processing_times'])
        return stats
