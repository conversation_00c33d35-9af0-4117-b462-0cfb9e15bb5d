"""
نظام المسح الذكي المتخصص للإنقاذ والبحث
Intelligent Scanner System for Search and Rescue Operations
=========================================================

نظام مسح متقدم مصمم خصيصاً لمعالجة صور الاستطلاع الجوي والفضائي
في عمليات البحث والإنقاذ في البيئات البحرية والصحراوية.
"""

import os
import logging
import mimetypes
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Generator
import hashlib
from datetime import datetime
import json

try:
    import rasterio
    from rasterio.errors import RasterioIOError
    RASTERIO_AVAILABLE = True
except ImportError:
    RASTERIO_AVAILABLE = False

try:
    from osgeo import gdal, osr
    GDAL_AVAILABLE = True
except ImportError:
    GDAL_AVAILABLE = False

from PIL import Image, ExifTags
from PIL.ExifTags import TAGS
import numpy as np
from tqdm import tqdm

from ..utils.exceptions import ScannerError


class IntelligentRescueScanner:
    """
    نظام مسح ذكي متخصص للبحث والإنقاذ
    Intelligent scanner specialized for search and rescue operations
    """
    
    # تنسيقات الصور المدعومة للعمليات الجوية والفضائية
    SUPPORTED_FORMATS = {
        '.jpg', '.jpeg',    # صور الطائرات بدون طيار العادية
        '.png',             # صور عالية الجودة
        '.tiff', '.tif',    # صور الأقمار الصناعية والمسح الجوي
        '.bmp',             # صور أنظمة المراقبة
        '.webp',            # صور حديثة مضغوطة
        '.jp2',             # JPEG 2000 للصور عالية الدقة
        '.hdf',             # بيانات الأقمار الصناعية
        '.nc'               # بيانات NetCDF
    }
    
    # أنواع MIME المدعومة
    SUPPORTED_MIMES = {
        'image/jpeg', 'image/png', 'image/tiff', 'image/bmp',
        'image/webp', 'image/jp2', 'application/x-hdf',
        'application/x-netcdf'
    }
    
    def __init__(self, config: Dict):
        """
        تهيئة نظام المسح الذكي
        
        Args:
            config: إعدادات التكوين
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # إحصائيات المسح
        self.scan_stats = {
            'total_files_found': 0,
            'valid_images': 0,
            'corrupted_files': 0,
            'unsupported_formats': 0,
            'geospatial_images': 0,
            'total_size_bytes': 0,
            'directories_scanned': 0,
            'scan_start_time': None,
            'scan_end_time': None,
            'format_distribution': {},
            'error_log': [],
            'geospatial_metadata': []
        }
        
        # تكوين مستويات التسجيل
        self.setup_logging()
        
    def setup_logging(self):
        """إعداد نظام التسجيل المتقدم"""
        log_level = self.config.get('log_level', 'INFO')
        
        # إنشاء مجلد السجلات
        log_dir = Path(self.config.get('log_directory', 'logs'))
        log_dir.mkdir(exist_ok=True)
        
        # إعداد معالج الملفات
        log_file = log_dir / f"rescue_scan_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setLevel(getattr(logging, log_level))
        
        # تنسيق السجلات
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        file_handler.setFormatter(formatter)
        
        self.logger.addHandler(file_handler)
        self.logger.setLevel(getattr(logging, log_level))
        
    def scan_directory(self, root_path: str) -> List[Dict]:
        """
        مسح ذكي وشامل للدليل الجذري وجميع الدلائل الفرعية
        
        Args:
            root_path: المسار الجذري للمسح
            
        Returns:
            قائمة بمعلومات الصور المكتشفة
        """
        root_path = Path(root_path)
        if not root_path.exists():
            raise ScannerError(f"المسار غير موجود: {root_path}")
        
        self.logger.info(f"بدء المسح الذكي للمسار: {root_path}")
        self.scan_stats['scan_start_time'] = datetime.now()
        
        discovered_images = []
        
        try:
            # مسح متكرر للدلائل
            for image_info in self._recursive_scan(root_path):
                discovered_images.append(image_info)
                
        except Exception as e:
            self.logger.error(f"خطأ في المسح: {e}")
            raise ScannerError(f"فشل في مسح الدليل: {e}")
        
        finally:
            self.scan_stats['scan_end_time'] = datetime.now()
            self._generate_scan_report()
        
        self.logger.info(f"اكتمل المسح: {len(discovered_images)} صورة صالحة")
        return discovered_images
    
    def _recursive_scan(self, directory: Path) -> Generator[Dict, None, None]:
        """
        مسح متكرر للدلائل مع معالجة الأخطاء
        
        Args:
            directory: الدليل المراد مسحه
            
        Yields:
            معلومات الصور المكتشفة
        """
        try:
            # قائمة جميع الملفات في الدليل
            all_files = list(directory.rglob('*'))
            self.scan_stats['directories_scanned'] += 1
            
            # شريط التقدم
            with tqdm(all_files, desc=f"مسح {directory.name}", unit="ملف") as pbar:
                for file_path in pbar:
                    if file_path.is_file():
                        self.scan_stats['total_files_found'] += 1
                        
                        # فحص الملف
                        image_info = self._analyze_file(file_path)
                        if image_info:
                            yield image_info
                            pbar.set_postfix({
                                'صور صالحة': self.scan_stats['valid_images'],
                                'أخطاء': self.scan_stats['corrupted_files']
                            })
                            
        except PermissionError as e:
            error_msg = f"ليس لديك صلاحية للوصول إلى: {directory}"
            self.logger.warning(error_msg)
            self.scan_stats['error_log'].append({
                'type': 'permission_error',
                'path': str(directory),
                'message': error_msg,
                'timestamp': datetime.now().isoformat()
            })
        except Exception as e:
            error_msg = f"خطأ في مسح الدليل {directory}: {e}"
            self.logger.error(error_msg)
            self.scan_stats['error_log'].append({
                'type': 'scan_error',
                'path': str(directory),
                'message': error_msg,
                'timestamp': datetime.now().isoformat()
            })
    
    def _analyze_file(self, file_path: Path) -> Optional[Dict]:
        """
        تحليل ملف واحد لتحديد ما إذا كان صورة صالحة
        
        Args:
            file_path: مسار الملف
            
        Returns:
            معلومات الصورة أو None إذا لم تكن صورة صالحة
        """
        try:
            # فحص الامتداد
            if file_path.suffix.lower() not in self.SUPPORTED_FORMATS:
                return None
            
            # فحص نوع MIME
            mime_type, _ = mimetypes.guess_type(str(file_path))
            if mime_type not in self.SUPPORTED_MIMES:
                return None
            
            # معلومات أساسية للملف
            file_stats = file_path.stat()
            file_size = file_stats.st_size
            self.scan_stats['total_size_bytes'] += file_size
            
            # تحديث توزيع التنسيقات
            ext = file_path.suffix.lower()
            self.scan_stats['format_distribution'][ext] = \
                self.scan_stats['format_distribution'].get(ext, 0) + 1
            
            # تحليل الصورة
            image_info = self._extract_image_metadata(file_path, file_size)
            
            if image_info:
                self.scan_stats['valid_images'] += 1
                
                # فحص البيانات الجغرافية المكانية
                geospatial_data = self._extract_geospatial_metadata(file_path)
                if geospatial_data:
                    image_info.update(geospatial_data)
                    self.scan_stats['geospatial_images'] += 1
                    self.scan_stats['geospatial_metadata'].append({
                        'file': str(file_path),
                        'geospatial_data': geospatial_data
                    })
                
                return image_info
            
        except Exception as e:
            self._log_file_error(file_path, e)
            
        return None
    
    def _extract_image_metadata(self, file_path: Path, file_size: int) -> Optional[Dict]:
        """
        استخراج البيانات الوصفية للصورة
        
        Args:
            file_path: مسار الصورة
            file_size: حجم الملف
            
        Returns:
            البيانات الوصفية للصورة
        """
        try:
            with Image.open(file_path) as img:
                # معلومات أساسية
                width, height = img.size
                channels = len(img.getbands()) if hasattr(img, 'getbands') else 3
                format_name = img.format
                
                # البيانات الوصفية الأساسية
                metadata = {
                    'filename': file_path.name,
                    'filepath': str(file_path),
                    'absolute_path': str(file_path.absolute()),
                    'directory': str(file_path.parent),
                    'size_bytes': file_size,
                    'format': format_name.lower() if format_name else 'unknown',
                    'width': width,
                    'height': height,
                    'channels': channels,
                    'aspect_ratio': round(width / height, 3) if height > 0 else 0,
                    'total_pixels': width * height,
                    'file_hash': self._calculate_file_hash(file_path),
                    'modification_time': datetime.fromtimestamp(file_path.stat().st_mtime).isoformat(),
                    'scan_timestamp': datetime.now().isoformat()
                }
                
                # استخراج بيانات EXIF
                exif_data = self._extract_exif_data(img)
                if exif_data:
                    metadata['exif_data'] = exif_data
                
                return metadata
                
        except Exception as e:
            self._log_file_error(file_path, e, "فشل في استخراج البيانات الوصفية")
            return None
    
    def _extract_geospatial_metadata(self, file_path: Path) -> Optional[Dict]:
        """
        استخراج البيانات الجغرافية المكانية من GeoTIFF وتنسيقات أخرى
        
        Args:
            file_path: مسار الملف
            
        Returns:
            البيانات الجغرافية المكانية
        """
        geospatial_data = {}
        
        # محاولة استخدام rasterio أولاً
        if RASTERIO_AVAILABLE:
            try:
                with rasterio.open(file_path) as dataset:
                    if dataset.crs:
                        geospatial_data.update({
                            'crs': str(dataset.crs),
                            'bounds': list(dataset.bounds),
                            'transform': list(dataset.transform),
                            'resolution': [dataset.res[0], dataset.res[1]],
                            'geospatial_type': 'rasterio'
                        })
            except RasterioIOError:
                pass
            except Exception as e:
                self.logger.debug(f"خطأ في قراءة البيانات الجغرافية بـ rasterio: {e}")
        
        # محاولة استخدام GDAL كبديل
        if not geospatial_data and GDAL_AVAILABLE:
            try:
                dataset = gdal.Open(str(file_path))
                if dataset:
                    geotransform = dataset.GetGeoTransform()
                    projection = dataset.GetProjection()
                    
                    if geotransform != (0.0, 1.0, 0.0, 0.0, 0.0, 1.0) and projection:
                        geospatial_data.update({
                            'geotransform': geotransform,
                            'projection': projection,
                            'geospatial_type': 'gdal'
                        })
                        
                        # حساب الحدود الجغرافية
                        width = dataset.RasterXSize
                        height = dataset.RasterYSize
                        
                        # الزاوية العلوية اليسرى
                        x_min = geotransform[0]
                        y_max = geotransform[3]
                        
                        # الزاوية السفلية اليمنى
                        x_max = x_min + width * geotransform[1]
                        y_min = y_max + height * geotransform[5]
                        
                        geospatial_data['bounds'] = [x_min, y_min, x_max, y_max]
                        
                    dataset = None
            except Exception as e:
                self.logger.debug(f"خطأ في قراءة البيانات الجغرافية بـ GDAL: {e}")
        
        return geospatial_data if geospatial_data else None
    
    def _extract_exif_data(self, img: Image.Image) -> Optional[Dict]:
        """
        استخراج بيانات EXIF من الصورة
        
        Args:
            img: كائن الصورة
            
        Returns:
            بيانات EXIF
        """
        try:
            exif_dict = img._getexif()
            if exif_dict:
                exif_data = {}
                for tag_id, value in exif_dict.items():
                    tag = TAGS.get(tag_id, tag_id)
                    
                    # تحويل القيم المعقدة إلى نص
                    if isinstance(value, (tuple, list)):
                        value = str(value)
                    elif isinstance(value, bytes):
                        try:
                            value = value.decode('utf-8')
                        except:
                            value = str(value)
                    
                    exif_data[tag] = value
                
                return exif_data
        except Exception as e:
            self.logger.debug(f"خطأ في استخراج بيانات EXIF: {e}")
        
        return None
    
    def _calculate_file_hash(self, file_path: Path) -> str:
        """
        حساب hash للملف لاكتشاف التكرار
        
        Args:
            file_path: مسار الملف
            
        Returns:
            hash الملف
        """
        try:
            hash_md5 = hashlib.md5()
            with open(file_path, "rb") as f:
                # قراءة الملف على دفعات لتوفير الذاكرة
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except Exception as e:
            self.logger.warning(f"فشل في حساب hash للملف {file_path}: {e}")
            return ""
    
    def _log_file_error(self, file_path: Path, error: Exception, context: str = ""):
        """
        تسجيل أخطاء الملفات بتفاصيل دقيقة
        
        Args:
            file_path: مسار الملف
            error: الخطأ
            context: سياق إضافي
        """
        error_info = {
            'type': 'file_error',
            'file_path': str(file_path),
            'file_name': file_path.name,
            'directory': str(file_path.parent),
            'error_type': type(error).__name__,
            'error_message': str(error),
            'context': context,
            'timestamp': datetime.now().isoformat()
        }
        
        self.scan_stats['error_log'].append(error_info)
        self.scan_stats['corrupted_files'] += 1
        
        self.logger.warning(
            f"خطأ في الملف {file_path.name}: {error} {context}"
        )
    
    def _generate_scan_report(self):
        """توليد تقرير شامل للمسح"""
        scan_duration = (
            self.scan_stats['scan_end_time'] - self.scan_stats['scan_start_time']
        ).total_seconds()
        
        self.logger.info("=" * 60)
        self.logger.info("تقرير المسح الذكي للإنقاذ والبحث")
        self.logger.info("=" * 60)
        self.logger.info(f"إجمالي الملفات الممسوحة: {self.scan_stats['total_files_found']}")
        self.logger.info(f"الصور الصالحة: {self.scan_stats['valid_images']}")
        self.logger.info(f"الملفات التالفة: {self.scan_stats['corrupted_files']}")
        self.logger.info(f"الصور الجغرافية المكانية: {self.scan_stats['geospatial_images']}")
        self.logger.info(f"الدلائل الممسوحة: {self.scan_stats['directories_scanned']}")
        self.logger.info(f"إجمالي الحجم: {self._format_size(self.scan_stats['total_size_bytes'])}")
        self.logger.info(f"مدة المسح: {scan_duration:.2f} ثانية")
        
        # توزيع التنسيقات
        self.logger.info("\nتوزيع تنسيقات الصور:")
        for format_ext, count in self.scan_stats['format_distribution'].items():
            percentage = (count / self.scan_stats['valid_images']) * 100
            self.logger.info(f"  {format_ext.upper()}: {count} ({percentage:.1f}%)")
        
        # الأخطاء
        if self.scan_stats['error_log']:
            self.logger.info(f"\nالأخطاء المسجلة: {len(self.scan_stats['error_log'])}")
    
    def _format_size(self, size_bytes: int) -> str:
        """تنسيق حجم الملف بوحدات مقروءة"""
        for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
            if size_bytes < 1024.0:
                return f"{size_bytes:.1f} {unit}"
            size_bytes /= 1024.0
        return f"{size_bytes:.1f} PB"
    
    def get_scan_statistics(self) -> Dict:
        """الحصول على إحصائيات المسح"""
        return self.scan_stats.copy()
    
    def export_scan_report(self, output_path: str):
        """تصدير تقرير المسح إلى ملف JSON"""
        report_path = Path(output_path) / f"scan_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(self.scan_stats, f, ensure_ascii=False, indent=2, default=str)
        
        self.logger.info(f"تم تصدير تقرير المسح إلى: {report_path}")
        return report_path
