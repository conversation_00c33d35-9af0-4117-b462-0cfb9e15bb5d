"""
نظام إدارة البيانات الذكية المتقدمة للإنقاذ والبحث
Smart Advanced Data Management System for Search and Rescue Operations
====================================================================

نظام إدارة بيانات متقدم مصمم خصيصاً لتنظيم وإدارة مجموعات بيانات
عمليات البحث والإنقاذ مع دعم التنسيقات القياسية والبيانات الجغرافية.
"""

import os
import json
import yaml
import csv
import shutil
import hashlib
import logging
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Any
from datetime import datetime
import pandas as pd
import numpy as np
from collections import defaultdict, Counter

try:
    import imagehash
    from PIL import Image
    IMAGEHASH_AVAILABLE = True
except ImportError:
    IMAGEHASH_AVAILABLE = False

try:
    import dvc.api
    DVC_AVAILABLE = True
except ImportError:
    DVC_AVAILABLE = False

from ..utils.exceptions import DataManagerError


class SmartRescueDataManager:
    """
    مدير البيانات الذكي المتخصص لعمليات الإنقاذ والبحث
    Smart data manager specialized for search and rescue operations
    """
    
    def __init__(self, config: Dict):
        """
        تهيئة مدير البيانات الذكي
        
        Args:
            config: إعدادات التكوين
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # إعدادات إدارة البيانات
        self.data_settings = self._load_data_settings()
        
        # إحصائيات إدارة البيانات
        self.data_stats = {
            'total_images_processed': 0,
            'duplicates_removed': 0,
            'yolo_datasets_created': 0,
            'metadata_files_generated': 0,
            'geospatial_images': 0,
            'dataset_versions': []
        }
        
        # مخزن البيانات الوصفية
        self.metadata_store = []
        self.duplicate_hashes = set()
        
    def _load_data_settings(self) -> Dict:
        """تحميل إعدادات إدارة البيانات من التكوين"""
        return {
            # إعدادات اتفاقية التسمية
            'naming_convention': self.config.get('naming_convention', 
                '{environment}_{object_status}_{category}_{timestamp}_{index:06d}.{extension}'),
            'use_arabic_names': self.config.get('use_arabic_names', False),
            
            # إعدادات تقسيم البيانات
            'train_ratio': self.config.get('train_ratio', 0.7),
            'val_ratio': self.config.get('val_ratio', 0.2),
            'test_ratio': self.config.get('test_ratio', 0.1),
            'stratified_split': self.config.get('stratified_split', True),
            
            # إعدادات تصدير YOLO
            'create_yolo_dataset': self.config.get('create_yolo_dataset', True),
            'yolo_format_version': self.config.get('yolo_format_version', 'v8'),
            'include_confidence_in_labels': self.config.get('include_confidence_in_labels', False),
            
            # إعدادات البيانات الوصفية
            'export_formats': self.config.get('export_formats', ['csv', 'json', 'yaml']),
            'include_geospatial_metadata': self.config.get('include_geospatial_metadata', True),
            'include_processing_metadata': self.config.get('include_processing_metadata', True),
            
            # إعدادات إزالة التكرار
            'enable_deduplication': self.config.get('enable_deduplication', True),
            'hash_algorithm': self.config.get('hash_algorithm', 'phash'),
            'similarity_threshold': self.config.get('similarity_threshold', 5),
            
            # إعدادات إدارة الإصدارات
            'enable_versioning': self.config.get('enable_versioning', True),
            'version_control_system': self.config.get('version_control_system', 'dvc'),
            
            # إعدادات الأداء
            'copy_files': self.config.get('copy_files', True),
            'create_symlinks': self.config.get('create_symlinks', False),
            'preserve_original_structure': self.config.get('preserve_original_structure', False)
        }
    
    def create_rescue_dataset(self, classified_images: List[Dict], output_path: str) -> Dict:
        """
        إنشاء مجموعة بيانات متخصصة للإنقاذ والبحث
        
        Args:
            classified_images: قائمة الصور المصنفة
            output_path: مسار الإخراج
            
        Returns:
            معلومات مجموعة البيانات المنشأة
        """
        try:
            self.logger.info("بدء إنشاء مجموعة بيانات الإنقاذ والبحث...")
            
            output_dir = Path(output_path)
            output_dir.mkdir(parents=True, exist_ok=True)
            
            # إزالة التكرار
            if self.data_settings['enable_deduplication']:
                classified_images = self._remove_duplicates(classified_images)
            
            # تقسيم البيانات
            split_data = self._split_rescue_dataset(classified_images)
            
            # إنشاء هيكل الدليل
            dataset_structure = self._create_dataset_structure(output_dir, split_data)
            
            # إنشاء مجموعة بيانات YOLO
            yolo_info = {}
            if self.data_settings['create_yolo_dataset']:
                yolo_info = self._create_yolo_dataset(output_dir, split_data)
            
            # توليد البيانات الوصفية
            metadata_files = self._generate_comprehensive_metadata(output_dir, classified_images, split_data)
            
            # إنشاء ملف بيان مجموعة البيانات
            manifest = self._create_dataset_manifest(output_dir, classified_images, split_data, yolo_info)
            
            # إدارة الإصدارات
            version_info = {}
            if self.data_settings['enable_versioning']:
                version_info = self._manage_dataset_versioning(output_dir)
            
            # تحديث الإحصائيات
            self.data_stats['total_images_processed'] = len(classified_images)
            self.data_stats['metadata_files_generated'] = len(metadata_files)
            if yolo_info:
                self.data_stats['yolo_datasets_created'] += 1
            
            dataset_info = {
                'output_directory': str(output_dir),
                'total_images': len(classified_images),
                'dataset_structure': dataset_structure,
                'yolo_dataset': yolo_info,
                'metadata_files': metadata_files,
                'manifest_file': manifest,
                'version_info': version_info,
                'creation_timestamp': datetime.now().isoformat()
            }
            
            self.logger.info(f"تم إنشاء مجموعة البيانات بنجاح: {len(classified_images)} صورة")
            return dataset_info
            
        except Exception as e:
            self.logger.error(f"فشل في إنشاء مجموعة البيانات: {e}")
            raise DataManagerError(f"فشل في إنشاء مجموعة البيانات: {e}")
    
    def _remove_duplicates(self, images: List[Dict]) -> List[Dict]:
        """
        إزالة الصور المكررة باستخدام خوارزميات التجزئة
        
        Args:
            images: قائمة الصور
            
        Returns:
            قائمة الصور بدون تكرار
        """
        if not IMAGEHASH_AVAILABLE:
            self.logger.warning("مكتبة imagehash غير متوفرة، تخطي إزالة التكرار")
            return images
        
        self.logger.info("بدء إزالة الصور المكررة...")
        
        unique_images = []
        seen_hashes = {}
        algorithm = self.data_settings['hash_algorithm']
        threshold = self.data_settings['similarity_threshold']
        
        for image_data in images:
            try:
                # حساب hash للصورة
                image_path = Path(image_data['filepath'])
                with Image.open(image_path) as img:
                    if algorithm == 'phash':
                        img_hash = imagehash.phash(img)
                    elif algorithm == 'dhash':
                        img_hash = imagehash.dhash(img)
                    elif algorithm == 'whash':
                        img_hash = imagehash.whash(img)
                    else:
                        img_hash = imagehash.average_hash(img)
                
                # فحص التشابه مع الصور الموجودة
                is_duplicate = False
                for existing_hash in seen_hashes:
                    if abs(img_hash - existing_hash) <= threshold:
                        is_duplicate = True
                        self.data_stats['duplicates_removed'] += 1
                        self.logger.debug(f"صورة مكررة: {image_path.name}")
                        break
                
                if not is_duplicate:
                    seen_hashes[img_hash] = image_data
                    unique_images.append(image_data)
                    
            except Exception as e:
                self.logger.warning(f"فشل في معالجة الصورة {image_data.get('filename', 'unknown')}: {e}")
                # إضافة الصورة رغم الخطأ
                unique_images.append(image_data)
        
        removed_count = len(images) - len(unique_images)
        self.logger.info(f"تم إزالة {removed_count} صورة مكررة من أصل {len(images)}")
        
        return unique_images
    
    def _split_rescue_dataset(self, images: List[Dict]) -> Dict:
        """
        تقسيم مجموعة البيانات مع مراعاة توازن الفئات
        
        Args:
            images: قائمة الصور
            
        Returns:
            البيانات المقسمة
        """
        self.logger.info("تقسيم مجموعة البيانات...")
        
        # تجميع الصور حسب الفئات
        environment_groups = defaultdict(list)
        target_groups = defaultdict(list)
        
        for image in images:
            # تجميع حسب البيئة
            env_class = image.get('environment_classification', {}).get('predicted_environment', 'unknown')
            environment_groups[env_class].append(image)
            
            # تجميع حسب وجود الأهداف
            target_class = image.get('rescue_detection', {}).get('image_classification', 'no_targets')
            target_groups[target_class].append(image)
        
        if self.data_settings['stratified_split']:
            # تقسيم طبقي متوازن
            split_data = self._stratified_split(images, target_groups)
        else:
            # تقسيم عشوائي
            split_data = self._random_split(images)
        
        # إحصائيات التقسيم
        self._log_split_statistics(split_data, environment_groups, target_groups)
        
        return split_data
    
    def _stratified_split(self, images: List[Dict], target_groups: Dict) -> Dict:
        """تقسيم طبقي متوازن"""
        train_ratio = self.data_settings['train_ratio']
        val_ratio = self.data_settings['val_ratio']
        test_ratio = self.data_settings['test_ratio']
        
        train_images = []
        val_images = []
        test_images = []
        
        for target_class, class_images in target_groups.items():
            # خلط الصور
            np.random.shuffle(class_images)
            
            n_images = len(class_images)
            n_train = int(n_images * train_ratio)
            n_val = int(n_images * val_ratio)
            
            # تقسيم الفئة
            train_images.extend(class_images[:n_train])
            val_images.extend(class_images[n_train:n_train + n_val])
            test_images.extend(class_images[n_train + n_val:])
        
        return {
            'train': train_images,
            'val': val_images,
            'test': test_images
        }
    
    def _random_split(self, images: List[Dict]) -> Dict:
        """تقسيم عشوائي"""
        np.random.shuffle(images)
        
        n_images = len(images)
        n_train = int(n_images * self.data_settings['train_ratio'])
        n_val = int(n_images * self.data_settings['val_ratio'])
        
        return {
            'train': images[:n_train],
            'val': images[n_train:n_train + n_val],
            'test': images[n_train + n_val:]
        }
    
    def _create_dataset_structure(self, output_dir: Path, split_data: Dict) -> Dict:
        """
        إنشاء هيكل دليل مجموعة البيانات
        
        Args:
            output_dir: دليل الإخراج
            split_data: البيانات المقسمة
            
        Returns:
            معلومات هيكل الدليل
        """
        self.logger.info("إنشاء هيكل دليل مجموعة البيانات...")
        
        structure_info = {}
        
        for split_name, images in split_data.items():
            split_dir = output_dir / split_name
            split_dir.mkdir(exist_ok=True)
            
            # تجميع الصور حسب الفئات
            category_groups = defaultdict(list)
            for image in images:
                # استخدام تصنيف الأهداف كفئة رئيسية
                target_class = image.get('rescue_detection', {}).get('image_classification', 'no_targets')
                environment = image.get('environment_classification', {}).get('predicted_environment', 'unknown')
                
                # إنشاء اسم فئة مركب
                category_name = f"{environment}_{target_class}"
                category_groups[category_name].append(image)
            
            # إنشاء دلائل الفئات ونسخ الصور
            split_structure = {}
            for category, category_images in category_groups.items():
                category_dir = split_dir / category
                category_dir.mkdir(exist_ok=True)
                
                copied_files = []
                for i, image in enumerate(category_images):
                    # إنشاء اسم ملف جديد
                    new_filename = self._generate_rescue_filename(image, category, i)
                    new_path = category_dir / new_filename
                    
                    # نسخ أو ربط الملف
                    if self.data_settings['copy_files']:
                        shutil.copy2(image['filepath'], new_path)
                    elif self.data_settings['create_symlinks']:
                        new_path.symlink_to(Path(image['filepath']).absolute())
                    
                    copied_files.append(str(new_path))
                    
                    # تحديث مسار الملف في البيانات
                    image['dataset_path'] = str(new_path)
                    image['dataset_filename'] = new_filename
                
                split_structure[category] = {
                    'directory': str(category_dir),
                    'image_count': len(copied_files),
                    'files': copied_files
                }
            
            structure_info[split_name] = split_structure
        
        return structure_info
    
    def _generate_rescue_filename(self, image_data: Dict, category: str, index: int) -> str:
        """
        توليد اسم ملف وفقاً لاتفاقية التسمية المتخصصة
        
        Args:
            image_data: بيانات الصورة
            category: فئة الصورة
            index: فهرس الصورة
            
        Returns:
            اسم الملف الجديد
        """
        # استخراج المعلومات
        environment = image_data.get('environment_classification', {}).get('predicted_environment', 'unknown')
        object_status = image_data.get('rescue_detection', {}).get('image_classification', 'no_targets')
        
        # تحويل إلى أسماء مختصرة
        env_short = {
            'sea': 'sea',
            'desert': 'desert', 
            'coast': 'coast',
            'urban': 'urban',
            'unknown': 'unk'
        }.get(environment, 'unk')
        
        status_short = {
            'contains_targets': 'has_targets',
            'no_targets': 'no_targets'
        }.get(object_status, 'no_targets')
        
        # الحصول على امتداد الملف الأصلي
        original_path = Path(image_data['filepath'])
        extension = original_path.suffix.lower()
        
        # توليد timestamp
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # تطبيق اتفاقية التسمية
        filename = self.data_settings['naming_convention'].format(
            environment=env_short,
            object_status=status_short,
            category=category.replace('_', '-'),
            timestamp=timestamp,
            index=index,
            extension=extension[1:]  # إزالة النقطة
        )
        
        return filename
    
    def _create_yolo_dataset(self, output_dir: Path, split_data: Dict) -> Dict:
        """
        إنشاء مجموعة بيانات بتنسيق YOLO
        
        Args:
            output_dir: دليل الإخراج
            split_data: البيانات المقسمة
            
        Returns:
            معلومات مجموعة بيانات YOLO
        """
        self.logger.info("إنشاء مجموعة بيانات YOLO...")
        
        yolo_dir = output_dir / 'yolo_dataset'
        yolo_dir.mkdir(exist_ok=True)
        
        # إنشاء دلائل YOLO
        for split_name in split_data.keys():
            (yolo_dir / split_name / 'images').mkdir(parents=True, exist_ok=True)
            (yolo_dir / split_name / 'labels').mkdir(parents=True, exist_ok=True)
        
        # إنشاء فئات YOLO
        class_names = self._create_yolo_classes()
        class_mapping = {name: idx for idx, name in enumerate(class_names)}
        
        # معالجة كل تقسيم
        yolo_info = {'splits': {}, 'classes': class_names, 'class_mapping': class_mapping}
        
        for split_name, images in split_data.items():
            split_info = self._process_yolo_split(yolo_dir, split_name, images, class_mapping)
            yolo_info['splits'][split_name] = split_info
        
        # إنشاء ملف data.yaml
        data_yaml_path = self._create_yolo_data_yaml(yolo_dir, yolo_info)
        yolo_info['data_yaml'] = str(data_yaml_path)
        
        return yolo_info
    
    def _create_yolo_classes(self) -> List[str]:
        """إنشاء قائمة فئات YOLO للإنقاذ"""
        return [
            'person',
            'life_jacket', 
            'life_boat',
            'debris',
            'aircraft_wreckage',
            'vehicle_wreckage',
            'distress_signal',
            'survivor_shelter',
            'emergency_equipment'
        ]
    
    def _process_yolo_split(self, yolo_dir: Path, split_name: str, images: List[Dict], class_mapping: Dict) -> Dict:
        """معالجة تقسيم واحد لـ YOLO"""
        images_dir = yolo_dir / split_name / 'images'
        labels_dir = yolo_dir / split_name / 'labels'
        
        processed_count = 0
        
        for image in images:
            try:
                # نسخ الصورة
                src_path = Path(image['filepath'])
                dst_image_path = images_dir / src_path.name
                shutil.copy2(src_path, dst_image_path)
                
                # إنشاء ملف التسميات
                label_path = labels_dir / f"{src_path.stem}.txt"
                self._create_yolo_label_file(label_path, image, class_mapping)
                
                processed_count += 1
                
            except Exception as e:
                self.logger.warning(f"فشل في معالجة الصورة {image.get('filename', 'unknown')} لـ YOLO: {e}")
        
        return {
            'images_directory': str(images_dir),
            'labels_directory': str(labels_dir),
            'image_count': processed_count
        }
    
    def _create_yolo_label_file(self, label_path: Path, image_data: Dict, class_mapping: Dict):
        """إنشاء ملف تسميات YOLO"""
        detections = image_data.get('rescue_detection', {}).get('detections', [])
        
        with open(label_path, 'w') as f:
            for detection in detections:
                rescue_class = detection.get('rescue_class', 'unknown')
                if rescue_class in class_mapping:
                    class_id = class_mapping[rescue_class]
                    bbox_norm = detection.get('bbox_normalized', [0, 0, 0, 0])
                    confidence = detection.get('confidence', 0.0)
                    
                    # تنسيق YOLO: class_id x_center y_center width height
                    if len(bbox_norm) >= 4:
                        line = f"{class_id} {bbox_norm[0]:.6f} {bbox_norm[1]:.6f} {bbox_norm[2]:.6f} {bbox_norm[3]:.6f}"
                        
                        # إضافة الثقة إذا مطلوب
                        if self.data_settings['include_confidence_in_labels']:
                            line += f" {confidence:.6f}"
                        
                        f.write(line + '\n')
    
    def _create_yolo_data_yaml(self, yolo_dir: Path, yolo_info: Dict) -> Path:
        """إنشاء ملف data.yaml لـ YOLO"""
        data_yaml = {
            'path': str(yolo_dir.absolute()),
            'train': 'train/images',
            'val': 'val/images',
            'test': 'test/images',
            'nc': len(yolo_info['classes']),
            'names': yolo_info['classes']
        }
        
        yaml_path = yolo_dir / 'data.yaml'
        with open(yaml_path, 'w', encoding='utf-8') as f:
            yaml.dump(data_yaml, f, default_flow_style=False, allow_unicode=True)
        
        return yaml_path

    def _generate_comprehensive_metadata(self, output_dir: Path, images: List[Dict], split_data: Dict) -> List[str]:
        """
        توليد ملفات البيانات الوصفية الشاملة

        Args:
            output_dir: دليل الإخراج
            images: قائمة الصور
            split_data: البيانات المقسمة

        Returns:
            قائمة مسارات ملفات البيانات الوصفية
        """
        self.logger.info("توليد البيانات الوصفية الشاملة...")

        metadata_files = []
        export_formats = self.data_settings['export_formats']

        # إعداد البيانات للتصدير
        metadata_records = []
        for image in images:
            record = self._create_metadata_record(image)
            metadata_records.append(record)

        # تصدير بالتنسيقات المطلوبة
        if 'csv' in export_formats:
            csv_path = self._export_metadata_csv(output_dir, metadata_records)
            metadata_files.append(str(csv_path))

        if 'json' in export_formats:
            json_path = self._export_metadata_json(output_dir, metadata_records)
            metadata_files.append(str(json_path))

        if 'yaml' in export_formats:
            yaml_path = self._export_metadata_yaml(output_dir, metadata_records)
            metadata_files.append(str(yaml_path))

        # تصدير البيانات الجغرافية المكانية منفصلة
        if self.data_settings['include_geospatial_metadata']:
            geospatial_path = self._export_geospatial_metadata(output_dir, images)
            if geospatial_path:
                metadata_files.append(str(geospatial_path))

        return metadata_files

    def _create_metadata_record(self, image_data: Dict) -> Dict:
        """إنشاء سجل بيانات وصفية شامل لصورة واحدة"""
        record = {
            # معلومات الملف الأساسية
            'filename': image_data.get('filename', ''),
            'original_path': image_data.get('filepath', ''),
            'dataset_path': image_data.get('dataset_path', ''),
            'dataset_filename': image_data.get('dataset_filename', ''),
            'file_size_bytes': image_data.get('size_bytes', 0),
            'file_hash': image_data.get('file_hash', ''),

            # معلومات الصورة
            'width': image_data.get('width', 0),
            'height': image_data.get('height', 0),
            'channels': image_data.get('channels', 0),
            'format': image_data.get('format', ''),
            'aspect_ratio': image_data.get('aspect_ratio', 0),
            'total_pixels': image_data.get('total_pixels', 0),

            # تصنيف البيئة
            'environment_class': '',
            'environment_confidence': 0.0,
            'environment_arabic': '',

            # كشف أهداف الإنقاذ
            'rescue_classification': '',
            'rescue_classification_arabic': '',
            'targets_detected': False,
            'total_detections': 0,
            'detection_confidence_avg': 0.0,
            'unique_rescue_classes': [],

            # معلومات المعالجة
            'processed': image_data.get('processed', False),
            'enhanced': image_data.get('enhanced', False),
            'normalized': image_data.get('normalized', False),
            'processing_time': image_data.get('processing_time', 0.0),
            'enhancements_applied': image_data.get('enhancements_applied', []),

            # معلومات التصنيف
            'needs_human_verification': False,
            'classification_timestamp': '',

            # البيانات الجغرافية
            'has_geospatial_data': False,
            'coordinate_system': '',
            'bounds': [],

            # معلومات إضافية
            'scan_timestamp': image_data.get('scan_timestamp', ''),
            'modification_time': image_data.get('modification_time', '')
        }

        # ملء بيانات تصنيف البيئة
        env_data = image_data.get('environment_classification', {})
        if env_data:
            record['environment_class'] = env_data.get('predicted_environment', '')
            record['environment_confidence'] = env_data.get('confidence', 0.0)
            record['environment_arabic'] = env_data.get('environment_arabic', '')

        # ملء بيانات كشف الأهداف
        rescue_data = image_data.get('rescue_detection', {})
        if rescue_data:
            record['rescue_classification'] = rescue_data.get('image_classification', '')
            record['rescue_classification_arabic'] = rescue_data.get('classification_arabic', '')
            record['targets_detected'] = rescue_data.get('targets_detected', False)
            record['total_detections'] = rescue_data.get('total_detections', 0)
            record['unique_rescue_classes'] = rescue_data.get('unique_rescue_classes', [])

            # حساب متوسط الثقة
            detections = rescue_data.get('detections', [])
            if detections:
                confidences = [d.get('confidence', 0) for d in detections]
                record['detection_confidence_avg'] = np.mean(confidences)

        # ملء معلومات التحقق البشري
        record['needs_human_verification'] = image_data.get('needs_human_verification', False)
        record['classification_timestamp'] = image_data.get('classification_timestamp', '')

        # ملء البيانات الجغرافية
        if any(key.startswith('geo') for key in image_data.keys()):
            record['has_geospatial_data'] = True
            record['coordinate_system'] = image_data.get('crs', '')
            record['bounds'] = image_data.get('bounds', [])

        return record

    def _export_metadata_csv(self, output_dir: Path, records: List[Dict]) -> Path:
        """تصدير البيانات الوصفية بتنسيق CSV"""
        csv_path = output_dir / 'rescue_metadata.csv'

        if records:
            df = pd.DataFrame(records)
            df.to_csv(csv_path, index=False, encoding='utf-8')

        return csv_path

    def _export_metadata_json(self, output_dir: Path, records: List[Dict]) -> Path:
        """تصدير البيانات الوصفية بتنسيق JSON"""
        json_path = output_dir / 'rescue_metadata.json'

        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(records, f, ensure_ascii=False, indent=2, default=str)

        return json_path

    def _export_metadata_yaml(self, output_dir: Path, records: List[Dict]) -> Path:
        """تصدير البيانات الوصفية بتنسيق YAML"""
        yaml_path = output_dir / 'rescue_metadata.yaml'

        with open(yaml_path, 'w', encoding='utf-8') as f:
            yaml.dump(records, f, default_flow_style=False, allow_unicode=True)

        return yaml_path

    def _export_geospatial_metadata(self, output_dir: Path, images: List[Dict]) -> Optional[Path]:
        """تصدير البيانات الجغرافية المكانية منفصلة"""
        geospatial_records = []

        for image in images:
            if any(key.startswith('geo') for key in image.keys()):
                geo_record = {
                    'filename': image.get('filename', ''),
                    'filepath': image.get('filepath', ''),
                    'crs': image.get('crs', ''),
                    'bounds': image.get('bounds', []),
                    'transform': image.get('transform', []),
                    'resolution': image.get('resolution', []),
                    'geospatial_type': image.get('geospatial_type', '')
                }
                geospatial_records.append(geo_record)

        if geospatial_records:
            geo_path = output_dir / 'geospatial_metadata.json'
            with open(geo_path, 'w', encoding='utf-8') as f:
                json.dump(geospatial_records, f, ensure_ascii=False, indent=2, default=str)

            self.data_stats['geospatial_images'] = len(geospatial_records)
            return geo_path

        return None

    def _create_dataset_manifest(self, output_dir: Path, images: List[Dict],
                               split_data: Dict, yolo_info: Dict) -> Path:
        """
        إنشاء ملف بيان مجموعة البيانات

        Args:
            output_dir: دليل الإخراج
            images: قائمة الصور
            split_data: البيانات المقسمة
            yolo_info: معلومات مجموعة بيانات YOLO

        Returns:
            مسار ملف البيان
        """
        # إحصائيات عامة
        total_images = len(images)
        environment_stats = Counter()
        rescue_stats = Counter()

        for image in images:
            env_class = image.get('environment_classification', {}).get('predicted_environment', 'unknown')
            rescue_class = image.get('rescue_detection', {}).get('image_classification', 'no_targets')

            environment_stats[env_class] += 1
            rescue_stats[rescue_class] += 1

        # إحصائيات التقسيم
        split_stats = {}
        for split_name, split_images in split_data.items():
            split_stats[split_name] = {
                'count': len(split_images),
                'percentage': (len(split_images) / total_images) * 100 if total_images > 0 else 0
            }

        # إحصائيات الجودة
        quality_stats = self._calculate_quality_statistics(images)

        # إنشاء البيان
        manifest = {
            'dataset_info': {
                'name': 'Rescue and Search Dataset',
                'description': 'مجموعة بيانات متخصصة لعمليات البحث والإنقاذ',
                'total_images': total_images,
                'creation_date': datetime.now().isoformat(),
                'version': '1.0.0'
            },
            'statistics': {
                'environment_distribution': dict(environment_stats),
                'rescue_target_distribution': dict(rescue_stats),
                'split_distribution': split_stats,
                'quality_statistics': quality_stats
            },
            'yolo_dataset': yolo_info,
            'data_management': {
                'duplicates_removed': self.data_stats['duplicates_removed'],
                'deduplication_enabled': self.data_settings['enable_deduplication'],
                'naming_convention': self.data_settings['naming_convention']
            },
            'processing_info': {
                'enhancement_pipeline': 'Advanced Rescue Preprocessing',
                'classification_models': {
                    'environment_model': self.config.get('environment_model', 'resnet50'),
                    'rescue_detection_model': self.config.get('rescue_detection_model', 'yolov8m')
                }
            }
        }

        # حفظ البيان
        manifest_path = output_dir / 'dataset_manifest.json'
        with open(manifest_path, 'w', encoding='utf-8') as f:
            json.dump(manifest, f, ensure_ascii=False, indent=2, default=str)

        return manifest_path

    def _calculate_quality_statistics(self, images: List[Dict]) -> Dict:
        """حساب إحصائيات جودة مجموعة البيانات"""
        if not images:
            return {}

        # إحصائيات الأحجام
        sizes = [img.get('size_bytes', 0) for img in images]
        widths = [img.get('width', 0) for img in images]
        heights = [img.get('height', 0) for img in images]

        # إحصائيات المعالجة
        processed_count = sum(1 for img in images if img.get('processed', False))
        enhanced_count = sum(1 for img in images if img.get('enhanced', False))

        # إحصائيات التصنيف
        high_confidence_env = sum(1 for img in images
                                if img.get('environment_classification', {}).get('high_confidence', False))

        needs_verification = sum(1 for img in images
                               if img.get('needs_human_verification', False))

        return {
            'file_sizes': {
                'min_bytes': min(sizes) if sizes else 0,
                'max_bytes': max(sizes) if sizes else 0,
                'mean_bytes': np.mean(sizes) if sizes else 0,
                'total_bytes': sum(sizes)
            },
            'dimensions': {
                'min_width': min(widths) if widths else 0,
                'max_width': max(widths) if widths else 0,
                'min_height': min(heights) if heights else 0,
                'max_height': max(heights) if heights else 0,
                'mean_width': np.mean(widths) if widths else 0,
                'mean_height': np.mean(heights) if heights else 0
            },
            'processing': {
                'processed_images': processed_count,
                'enhanced_images': enhanced_count,
                'processing_rate': (processed_count / len(images)) * 100 if images else 0
            },
            'classification_quality': {
                'high_confidence_environment': high_confidence_env,
                'needs_human_verification': needs_verification,
                'verification_rate': (needs_verification / len(images)) * 100 if images else 0
            }
        }

    def _manage_dataset_versioning(self, output_dir: Path) -> Dict:
        """إدارة إصدارات مجموعة البيانات"""
        if not self.data_settings['enable_versioning']:
            return {}

        version_info = {
            'version': '1.0.0',
            'creation_date': datetime.now().isoformat(),
            'description': 'Initial rescue dataset version'
        }

        # إنشاء ملف إصدار
        version_file = output_dir / 'VERSION'
        with open(version_file, 'w') as f:
            f.write(f"Version: {version_info['version']}\n")
            f.write(f"Date: {version_info['creation_date']}\n")
            f.write(f"Description: {version_info['description']}\n")

        # محاولة تهيئة DVC إذا كان متوفراً
        if DVC_AVAILABLE:
            try:
                # تهيئة DVC في دليل مجموعة البيانات
                import subprocess
                subprocess.run(['dvc', 'init'], cwd=output_dir, capture_output=True)
                version_info['dvc_initialized'] = True
            except Exception as e:
                self.logger.warning(f"فشل في تهيئة DVC: {e}")
                version_info['dvc_initialized'] = False

        self.data_stats['dataset_versions'].append(version_info)
        return version_info

    def _log_split_statistics(self, split_data: Dict, environment_groups: Dict, target_groups: Dict):
        """تسجيل إحصائيات التقسيم"""
        self.logger.info("إحصائيات تقسيم مجموعة البيانات:")

        for split_name, images in split_data.items():
            self.logger.info(f"  {split_name}: {len(images)} صورة")

        self.logger.info("توزيع البيئات:")
        for env, images in environment_groups.items():
            self.logger.info(f"  {env}: {len(images)} صورة")

        self.logger.info("توزيع أهداف الإنقاذ:")
        for target, images in target_groups.items():
            self.logger.info(f"  {target}: {len(images)} صورة")

    def get_data_management_statistics(self) -> Dict:
        """الحصول على إحصائيات إدارة البيانات"""
        return self.data_stats.copy()

    def export_data_management_report(self, output_path: str) -> Path:
        """تصدير تقرير إدارة البيانات"""
        report_path = Path(output_path) / f"data_management_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"

        report_data = {
            'data_management_statistics': self.get_data_management_statistics(),
            'data_settings': self.data_settings,
            'export_timestamp': datetime.now().isoformat()
        }

        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, ensure_ascii=False, indent=2, default=str)

        self.logger.info(f"تم تصدير تقرير إدارة البيانات إلى: {report_path}")
        return report_path
