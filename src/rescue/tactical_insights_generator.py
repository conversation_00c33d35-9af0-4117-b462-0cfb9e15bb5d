"""
مولد الرؤى التكتيكية المتقدم للإنقاذ والبحث
Advanced Tactical Insights Generator for Search and Rescue Operations
===================================================================

وحدة متخصصة لتوليد رؤى تكتيكية ذكية وقابلة للتنفيذ من تحليل البيانات،
مع التركيز على دعم القرار في عمليات الإنقاذ والبحث الحرجة.
"""

import numpy as np
import pandas as pd
import logging
from typing import Dict, List, Tuple, Optional, Any
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from collections import defaultdict
import json

@dataclass
class TacticalInsight:
    """هيكل بيانات للرؤية التكتيكية"""
    id: str
    category: str  # 'operational', 'strategic', 'tactical', 'predictive'
    priority: str  # 'critical', 'high', 'medium', 'low'
    title: str
    description: str
    confidence: float  # 0.0 to 1.0
    supporting_data: Dict
    recommendations: List[str]
    impact_assessment: Dict
    timeline: str  # 'immediate', 'short_term', 'medium_term', 'long_term'
    stakeholders: List[str]
    timestamp: str
    
    def to_dict(self) -> Dict:
        """تحويل إلى قاموس"""
        return asdict(self)


class TacticalInsightsGenerator:
    """
    مولد الرؤى التكتيكية المتقدم
    Advanced tactical insights generator
    """
    
    def __init__(self, config: Dict):
        """
        تهيئة مولد الرؤى التكتيكية
        
        Args:
            config: إعدادات التكوين
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # إعدادات توليد الرؤى
        self.insight_settings = {
            'confidence_threshold': config.get('insight_confidence_threshold', 0.7),
            'max_insights_per_category': config.get('max_insights_per_category', 5),
            'enable_predictive_insights': config.get('enable_predictive_insights', True),
            'priority_weights': {
                'critical': 1.0,
                'high': 0.8,
                'medium': 0.6,
                'low': 0.4
            }
        }
        
        # مخزن الرؤى المولدة
        self.generated_insights = []
        
        # قوالب الرؤى
        self.insight_templates = self._load_insight_templates()
    
    def _load_insight_templates(self) -> Dict:
        """تحميل قوالب الرؤى التكتيكية"""
        return {
            'detection_efficiency': {
                'title_template': 'كفاءة الكشف في {environment}',
                'description_template': 'معدل الكشف في بيئة {environment} هو {rate:.1%}، {comparison} المتوسط العام',
                'recommendations': [
                    'تحسين خوارزميات الكشف للبيئة المحددة',
                    'زيادة التدريب على هذا النوع من البيئات',
                    'مراجعة معايير الثقة للكشف'
                ]
            },
            
            'temporal_pattern': {
                'title_template': 'نمط زمني في {activity_type}',
                'description_template': 'تم اكتشاف نمط زمني واضح: {pattern_description}',
                'recommendations': [
                    'تخصيص موارد إضافية خلال أوقات الذروة',
                    'تحسين جدولة العمليات',
                    'تطوير استراتيجيات للأوقات منخفضة النشاط'
                ]
            },
            
            'spatial_hotspot': {
                'title_template': 'نقطة ساخنة مكانية مكتشفة',
                'description_template': 'تم تحديد منطقة عالية الكثافة في {coordinates} مع {count} كشف',
                'recommendations': [
                    'زيادة التركيز على هذه المنطقة',
                    'نشر موارد إضافية في المنطقة',
                    'تحليل أسباب التركز في هذه المنطقة'
                ]
            },
            
            'quality_degradation': {
                'title_template': 'تدهور في جودة البيانات',
                'description_template': 'انخفاض في جودة البيانات بنسبة {percentage:.1%} خلال {timeframe}',
                'recommendations': [
                    'مراجعة عمليات جمع البيانات',
                    'فحص معدات المسح',
                    'تحسين إجراءات ضمان الجودة'
                ]
            },
            
            'resource_imbalance': {
                'title_template': 'عدم توازن في توزيع الموارد',
                'description_template': 'تركز {percentage:.1%} من الموارد في {dominant_area}',
                'recommendations': [
                    'إعادة توزيع الموارد بشكل أكثر توازناً',
                    'تحليل أسباب التركز',
                    'تطوير استراتيجية توزيع محسنة'
                ]
            }
        }
    
    def generate_comprehensive_insights(self, analysis_results: Dict) -> List[TacticalInsight]:
        """
        توليد رؤى تكتيكية شاملة من نتائج التحليل
        
        Args:
            analysis_results: نتائج التحليل الشامل
            
        Returns:
            قائمة الرؤى التكتيكية المولدة
        """
        try:
            self.logger.info("بدء توليد الرؤى التكتيكية الشاملة...")
            
            insights = []
            
            # رؤى تشغيلية
            operational_insights = self._generate_operational_insights(analysis_results)
            insights.extend(operational_insights)
            
            # رؤى استراتيجية
            strategic_insights = self._generate_strategic_insights(analysis_results)
            insights.extend(strategic_insights)
            
            # رؤى تكتيكية
            tactical_insights = self._generate_tactical_insights(analysis_results)
            insights.extend(tactical_insights)
            
            # رؤى تنبؤية
            if self.insight_settings['enable_predictive_insights']:
                predictive_insights = self._generate_predictive_insights(analysis_results)
                insights.extend(predictive_insights)
            
            # تصفية وترتيب الرؤى
            filtered_insights = self._filter_and_rank_insights(insights)
            
            # حفظ الرؤى المولدة
            self.generated_insights = filtered_insights
            
            self.logger.info(f"تم توليد {len(filtered_insights)} رؤية تكتيكية")
            return filtered_insights
            
        except Exception as e:
            self.logger.error(f"فشل في توليد الرؤى التكتيكية: {e}")
            return []
    
    def _generate_operational_insights(self, analysis_results: Dict) -> List[TacticalInsight]:
        """توليد رؤى تشغيلية"""
        insights = []
        
        # تحليل كفاءة الكشف
        if 'efficiency_analysis' in analysis_results:
            efficiency_insights = self._analyze_detection_efficiency(analysis_results['efficiency_analysis'])
            insights.extend(efficiency_insights)
        
        # تحليل جودة البيانات
        if 'basic_analysis' in analysis_results:
            quality_insights = self._analyze_data_quality_trends(analysis_results['basic_analysis'])
            insights.extend(quality_insights)
        
        # تحليل استخدام الموارد
        if 'efficiency_analysis' in analysis_results:
            resource_insights = self._analyze_resource_utilization(analysis_results['efficiency_analysis'])
            insights.extend(resource_insights)
        
        return insights
    
    def _generate_strategic_insights(self, analysis_results: Dict) -> List[TacticalInsight]:
        """توليد رؤى استراتيجية"""
        insights = []
        
        # تحليل الأنماط طويلة المدى
        if 'temporal_analysis' in analysis_results:
            trend_insights = self._analyze_long_term_trends(analysis_results['temporal_analysis'])
            insights.extend(trend_insights)
        
        # تحليل التوزيع الجغرافي
        if 'spatial_analysis' in analysis_results:
            geographic_insights = self._analyze_geographic_strategy(analysis_results['spatial_analysis'])
            insights.extend(geographic_insights)
        
        # تحليل فعالية النماذج
        model_insights = self._analyze_model_effectiveness(analysis_results)
        insights.extend(model_insights)
        
        return insights
    
    def _generate_tactical_insights(self, analysis_results: Dict) -> List[TacticalInsight]:
        """توليد رؤى تكتيكية"""
        insights = []
        
        # تحليل النقاط الساخنة
        if 'spatial_analysis' in analysis_results:
            hotspot_insights = self._analyze_tactical_hotspots(analysis_results['spatial_analysis'])
            insights.extend(hotspot_insights)
        
        # تحليل الأنماط الزمنية التكتيكية
        if 'temporal_analysis' in analysis_results:
            temporal_tactical_insights = self._analyze_tactical_timing(analysis_results['temporal_analysis'])
            insights.extend(temporal_tactical_insights)
        
        # تحليل فرص التحسين الفورية
        if 'efficiency_analysis' in analysis_results:
            improvement_insights = self._analyze_immediate_improvements(analysis_results['efficiency_analysis'])
            insights.extend(improvement_insights)
        
        return insights
    
    def _generate_predictive_insights(self, analysis_results: Dict) -> List[TacticalInsight]:
        """توليد رؤى تنبؤية"""
        insights = []
        
        # تنبؤات الاتجاهات
        if 'temporal_analysis' in analysis_results:
            trend_predictions = self._predict_future_trends(analysis_results['temporal_analysis'])
            insights.extend(trend_predictions)
        
        # تنبؤات المخاطر
        risk_predictions = self._predict_potential_risks(analysis_results)
        insights.extend(risk_predictions)
        
        # تنبؤات الفرص
        opportunity_predictions = self._predict_opportunities(analysis_results)
        insights.extend(opportunity_predictions)
        
        return insights
    
    def _analyze_detection_efficiency(self, efficiency_data: Dict) -> List[TacticalInsight]:
        """تحليل كفاءة الكشف"""
        insights = []
        
        if 'success_rate_analysis' in efficiency_data:
            success_data = efficiency_data['success_rate_analysis']
            
            if 'detection_success_rate' in success_data:
                rate = success_data['detection_success_rate']['overall_rate']
                
                if rate < 0.5:  # معدل كشف منخفض
                    insight = TacticalInsight(
                        id=f"detection_efficiency_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                        category='operational',
                        priority='critical',
                        title='معدل كشف منخفض يتطلب تدخل فوري',
                        description=f'معدل الكشف الحالي {rate:.1%} أقل من المستوى المقبول (50%). هذا يؤثر على فعالية عمليات الإنقاذ.',
                        confidence=0.9,
                        supporting_data={
                            'current_rate': rate,
                            'threshold': 0.5,
                            'successful_detections': success_data['detection_success_rate']['successful_detections'],
                            'total_attempts': success_data['detection_success_rate']['total_attempts']
                        },
                        recommendations=[
                            'مراجعة وتحديث خوارزميات الكشف',
                            'زيادة التدريب على البيانات المتنوعة',
                            'تحسين معايير الثقة للكشف',
                            'إجراء تقييم شامل لأداء النماذج'
                        ],
                        impact_assessment={
                            'operational_impact': 'high',
                            'mission_success_impact': 'critical',
                            'resource_efficiency_impact': 'medium'
                        },
                        timeline='immediate',
                        stakeholders=['فريق التطوير', 'مشغلي النظام', 'قادة العمليات'],
                        timestamp=datetime.now().isoformat()
                    )
                    insights.append(insight)
        
        return insights
    
    def _analyze_data_quality_trends(self, basic_data: Dict) -> List[TacticalInsight]:
        """تحليل اتجاهات جودة البيانات"""
        insights = []
        
        if 'dataset_overview' in basic_data:
            quality_dist = basic_data['dataset_overview'].get('data_quality_distribution', {})
            
            if 'mean' in quality_dist:
                avg_quality = quality_dist['mean']
                
                if avg_quality < 70:  # جودة منخفضة
                    insight = TacticalInsight(
                        id=f"quality_trend_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                        category='operational',
                        priority='high',
                        title='انخفاض في جودة البيانات العامة',
                        description=f'متوسط جودة البيانات {avg_quality:.1f} أقل من المستوى المطلوب (70). هذا قد يؤثر على دقة التحليل.',
                        confidence=0.85,
                        supporting_data=quality_dist,
                        recommendations=[
                            'مراجعة عمليات جمع البيانات',
                            'تحسين معايير ضمان الجودة',
                            'تدريب الفرق على أفضل الممارسات',
                            'تطبيق فحوصات جودة أكثر صرامة'
                        ],
                        impact_assessment={
                            'data_reliability': 'high',
                            'analysis_accuracy': 'medium',
                            'decision_making': 'medium'
                        },
                        timeline='short_term',
                        stakeholders=['فريق جمع البيانات', 'محللي الجودة', 'مديري العمليات'],
                        timestamp=datetime.now().isoformat()
                    )
                    insights.append(insight)
        
        return insights
    
    def _analyze_resource_utilization(self, efficiency_data: Dict) -> List[TacticalInsight]:
        """تحليل استخدام الموارد"""
        insights = []
        
        if 'resource_utilization' in efficiency_data:
            resource_data = efficiency_data['resource_utilization']
            
            if 'environment_distribution' in resource_data:
                env_data = resource_data['environment_distribution']
                balance_score = env_data.get('resource_balance_score', 0)
                
                if balance_score < 0.6:  # عدم توازن في الموارد
                    most_utilized = env_data.get('most_utilized_environment', 'غير محدد')
                    
                    insight = TacticalInsight(
                        id=f"resource_balance_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                        category='strategic',
                        priority='medium',
                        title='عدم توازن في توزيع الموارد',
                        description=f'نقاط التوازن {balance_score:.2f} تشير إلى تركز مفرط في بيئة {most_utilized}. يجب إعادة توزيع الموارد.',
                        confidence=0.8,
                        supporting_data=env_data,
                        recommendations=[
                            'إعادة تخصيص الموارد للبيئات الأقل تغطية',
                            'تطوير استراتيجية توزيع متوازنة',
                            'زيادة التركيز على البيئات المهملة',
                            'مراجعة أولويات العمليات'
                        ],
                        impact_assessment={
                            'coverage_completeness': 'high',
                            'operational_efficiency': 'medium',
                            'mission_effectiveness': 'medium'
                        },
                        timeline='medium_term',
                        stakeholders=['مخططي العمليات', 'مديري الموارد', 'قادة الفرق'],
                        timestamp=datetime.now().isoformat()
                    )
                    insights.append(insight)
        
        return insights
    
    def _filter_and_rank_insights(self, insights: List[TacticalInsight]) -> List[TacticalInsight]:
        """تصفية وترتيب الرؤى حسب الأولوية والثقة"""
        # تصفية الرؤى منخفضة الثقة
        filtered = [
            insight for insight in insights 
            if insight.confidence >= self.insight_settings['confidence_threshold']
        ]
        
        # ترتيب حسب الأولوية والثقة
        priority_order = {'critical': 4, 'high': 3, 'medium': 2, 'low': 1}
        
        filtered.sort(
            key=lambda x: (priority_order.get(x.priority, 0), x.confidence),
            reverse=True
        )
        
        # تحديد العدد الأقصى لكل فئة
        categorized = defaultdict(list)
        for insight in filtered:
            categorized[insight.category].append(insight)
        
        final_insights = []
        max_per_category = self.insight_settings['max_insights_per_category']
        
        for category, category_insights in categorized.items():
            final_insights.extend(category_insights[:max_per_category])
        
        return final_insights
    
    def export_insights_report(self, output_path: str) -> str:
        """تصدير تقرير الرؤى التكتيكية"""
        if not self.generated_insights:
            self.logger.warning("لا توجد رؤى مولدة للتصدير")
            return ""
        
        report = {
            'report_metadata': {
                'title': 'تقرير الرؤى التكتيكية للإنقاذ والبحث',
                'generation_timestamp': datetime.now().isoformat(),
                'total_insights': len(self.generated_insights),
                'confidence_threshold': self.insight_settings['confidence_threshold']
            },
            'insights_summary': self._generate_insights_summary(),
            'detailed_insights': [insight.to_dict() for insight in self.generated_insights],
            'action_plan': self._generate_action_plan(),
            'priority_matrix': self._generate_priority_matrix()
        }
        
        report_path = f"{output_path}/tactical_insights_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2, default=str)
        
        self.logger.info(f"تم تصدير تقرير الرؤى التكتيكية: {report_path}")
        return report_path
    
    def _generate_insights_summary(self) -> Dict:
        """توليد ملخص الرؤى"""
        summary = {
            'by_category': defaultdict(int),
            'by_priority': defaultdict(int),
            'by_timeline': defaultdict(int),
            'average_confidence': 0,
            'critical_insights_count': 0
        }
        
        for insight in self.generated_insights:
            summary['by_category'][insight.category] += 1
            summary['by_priority'][insight.priority] += 1
            summary['by_timeline'][insight.timeline] += 1
            
            if insight.priority == 'critical':
                summary['critical_insights_count'] += 1
        
        if self.generated_insights:
            summary['average_confidence'] = sum(
                insight.confidence for insight in self.generated_insights
            ) / len(self.generated_insights)
        
        return dict(summary)
    
    def _generate_action_plan(self) -> List[Dict]:
        """توليد خطة عمل من الرؤى"""
        action_items = []
        
        # ترتيب الرؤى حسب الأولوية والجدول الزمني
        critical_insights = [i for i in self.generated_insights if i.priority == 'critical']
        immediate_insights = [i for i in self.generated_insights if i.timeline == 'immediate']
        
        # إضافة الإجراءات الحرجة والفورية
        for insight in critical_insights + immediate_insights:
            for i, recommendation in enumerate(insight.recommendations):
                action_items.append({
                    'action_id': f"{insight.id}_action_{i+1}",
                    'insight_id': insight.id,
                    'action': recommendation,
                    'priority': insight.priority,
                    'timeline': insight.timeline,
                    'stakeholders': insight.stakeholders,
                    'expected_impact': insight.impact_assessment
                })
        
        return action_items
    
    def _generate_priority_matrix(self) -> Dict:
        """توليد مصفوفة الأولويات"""
        matrix = {
            'critical_immediate': [],
            'critical_short_term': [],
            'high_immediate': [],
            'high_short_term': [],
            'medium_medium_term': [],
            'low_long_term': []
        }
        
        for insight in self.generated_insights:
            key = f"{insight.priority}_{insight.timeline}"
            if key in matrix:
                matrix[key].append({
                    'id': insight.id,
                    'title': insight.title,
                    'confidence': insight.confidence
                })
        
        return matrix
