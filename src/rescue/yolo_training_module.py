"""
وحدة تدريب نماذج YOLO المدمجة للإنقاذ والبحث
Integrated YOLO Model Training Module for Search and Rescue
=========================================================

وحدة متخصصة لتدريب نماذج YOLO على بيانات الإنقاذ والبحث
مع دعم إصدارات متعددة وتكوينات قابلة للتخصيص.
"""

import os
import sys
import yaml
import json
import logging
import shutil
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Optional, Tuple, Any
import subprocess
import time

try:
    import torch
    import torchvision
    TORCH_AVAILABLE = True
except ImportError:
    TORCH_AVAILABLE = False

try:
    from ultralytics import YOLO
    ULTRALYTICS_AVAILABLE = True
except ImportError:
    ULTRALYTICS_AVAILABLE = False

from ..utils.exceptions import TrainingError


class YOLOTrainingManager:
    """
    مدير تدريب نماذج YOLO المتخصص للإنقاذ والبحث
    Specialized YOLO training manager for search and rescue
    """
    
    def __init__(self, config: Dict):
        """
        تهيئة مدير التدريب
        
        Args:
            config: إعدادات التكوين
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # إعدادات التدريب
        self.training_config = self._load_training_config()
        
        # مسارات التدريب
        self.training_paths = self._setup_training_paths()
        
        # إحصائيات التدريب
        self.training_stats = {
            'start_time': None,
            'end_time': None,
            'total_epochs': 0,
            'best_map': 0.0,
            'final_loss': 0.0,
            'training_duration': 0.0
        }
        
        # التحقق من المتطلبات
        self._check_requirements()
    
    def _load_training_config(self) -> Dict:
        """تحميل إعدادات التدريب"""
        return {
            # إعدادات النموذج
            'model_version': self.config.get('model_version', 'yolov8m'),
            'pretrained': self.config.get('pretrained', True),
            'input_size': self.config.get('image_size', 640),
            
            # إعدادات التدريب
            'epochs': self.config.get('epochs', 100),
            'batch_size': self.config.get('batch_size', 16),
            'learning_rate': self.config.get('learning_rate', 0.01),
            'momentum': self.config.get('momentum', 0.937),
            'weight_decay': self.config.get('weight_decay', 0.0005),
            
            # إعدادات البيانات
            'augmentation': self.config.get('augmentation', True),
            'mosaic': self.config.get('mosaic', 1.0),
            'mixup': self.config.get('mixup', 0.0),
            'copy_paste': self.config.get('copy_paste', 0.0),
            
            # إعدادات الحفظ والتقييم
            'save_period': self.config.get('save_period', 10),
            'save_best': self.config.get('save_best', True),
            'save_last': self.config.get('save_last', True),
            'val_period': self.config.get('val_period', 1),
            'patience': self.config.get('patience', 50),
            
            # إعدادات الأداء
            'workers': self.config.get('workers', 8),
            'device': self.config.get('device', 'auto'),
            'amp': self.config.get('amp', True),
            
            # إعدادات التسجيل
            'project': self.config.get('project', 'rescue_training'),
            'name': self.config.get('name', 'rescue_yolo_training'),
            'exist_ok': self.config.get('exist_ok', True),
            'verbose': self.config.get('verbose', True)
        }
    
    def _setup_training_paths(self) -> Dict:
        """إعداد مسارات التدريب"""
        base_dir = Path(self.training_config['project'])
        training_name = self.training_config['name']
        
        paths = {
            'project_dir': base_dir,
            'training_dir': base_dir / training_name,
            'weights_dir': base_dir / training_name / 'weights',
            'logs_dir': base_dir / training_name / 'logs',
            'results_dir': base_dir / training_name / 'results',
            'config_backup_dir': base_dir / training_name / 'config_backup'
        }
        
        # إنشاء الدلائل
        for path in paths.values():
            path.mkdir(parents=True, exist_ok=True)
        
        return paths
    
    def _check_requirements(self):
        """التحقق من المتطلبات"""
        if not TORCH_AVAILABLE:
            raise TrainingError("PyTorch غير متوفر. يرجى تثبيته: pip install torch torchvision")
        
        if not ULTRALYTICS_AVAILABLE:
            raise TrainingError("Ultralytics غير متوفر. يرجى تثبيته: pip install ultralytics")
        
        # التحقق من CUDA إذا كان مطلوباً
        if self.training_config['device'] != 'cpu' and torch.cuda.is_available():
            self.logger.info(f"CUDA متوفر: {torch.cuda.get_device_name()}")
        elif self.training_config['device'] != 'cpu':
            self.logger.warning("CUDA غير متوفر، سيتم استخدام CPU")
            self.training_config['device'] = 'cpu'
    
    def prepare_rescue_dataset(self, dataset_path: str) -> str:
        """
        إعداد مجموعة بيانات الإنقاذ للتدريب
        
        Args:
            dataset_path: مسار مجموعة البيانات
            
        Returns:
            مسار ملف data.yaml
        """
        try:
            self.logger.info("إعداد مجموعة بيانات الإنقاذ للتدريب...")
            
            dataset_dir = Path(dataset_path)
            
            # البحث عن ملف data.yaml
            data_yaml_candidates = [
                dataset_dir / 'data.yaml',
                dataset_dir / 'yolo_dataset' / 'data.yaml',
                dataset_dir / 'dataset.yaml'
            ]
            
            data_yaml_path = None
            for candidate in data_yaml_candidates:
                if candidate.exists():
                    data_yaml_path = candidate
                    break
            
            if not data_yaml_path:
                # إنشاء ملف data.yaml إذا لم يكن موجوداً
                data_yaml_path = self._create_data_yaml(dataset_dir)
            
            # التحقق من صحة مجموعة البيانات
            self._validate_dataset(data_yaml_path)
            
            # نسخ ملف التكوين إلى دليل التدريب
            backup_path = self.training_paths['config_backup_dir'] / 'data.yaml'
            shutil.copy2(data_yaml_path, backup_path)
            
            self.logger.info(f"تم إعداد مجموعة البيانات: {data_yaml_path}")
            return str(data_yaml_path)
            
        except Exception as e:
            self.logger.error(f"فشل في إعداد مجموعة البيانات: {e}")
            raise TrainingError(f"فشل في إعداد البيانات: {e}")
    
    def _create_data_yaml(self, dataset_dir: Path) -> Path:
        """إنشاء ملف data.yaml لمجموعة البيانات"""
        # فئات الإنقاذ الافتراضية
        rescue_classes = [
            'person',           # شخص
            'life_jacket',      # سترة نجاة
            'life_boat',        # قارب نجاة
            'debris',           # حطام
            'aircraft_wreckage', # حطام طائرة
            'vehicle_wreckage', # حطام مركبة
            'emergency_signal', # إشارة طوارئ
            'rescue_equipment'  # معدات إنقاذ
        ]
        
        # البحث عن دلائل البيانات
        train_dir = None
        val_dir = None
        test_dir = None
        
        possible_structures = [
            # هيكل YOLO القياسي
            {
                'train': dataset_dir / 'train' / 'images',
                'val': dataset_dir / 'val' / 'images',
                'test': dataset_dir / 'test' / 'images'
            },
            # هيكل مجموعة البيانات المخصصة
            {
                'train': dataset_dir / 'yolo_dataset' / 'train' / 'images',
                'val': dataset_dir / 'yolo_dataset' / 'val' / 'images',
                'test': dataset_dir / 'yolo_dataset' / 'test' / 'images'
            }
        ]
        
        for structure in possible_structures:
            if structure['train'].exists():
                train_dir = structure['train']
                val_dir = structure['val'] if structure['val'].exists() else structure['train']
                test_dir = structure['test'] if structure['test'].exists() else structure['val']
                break
        
        if not train_dir:
            raise TrainingError("لم يتم العثور على دليل صور التدريب")
        
        # إنشاء محتوى data.yaml
        data_config = {
            'path': str(dataset_dir.absolute()),
            'train': str(train_dir.relative_to(dataset_dir)),
            'val': str(val_dir.relative_to(dataset_dir)),
            'test': str(test_dir.relative_to(dataset_dir)) if test_dir != val_dir else str(val_dir.relative_to(dataset_dir)),
            'nc': len(rescue_classes),
            'names': rescue_classes
        }
        
        # حفظ ملف data.yaml
        data_yaml_path = dataset_dir / 'data.yaml'
        with open(data_yaml_path, 'w', encoding='utf-8') as f:
            yaml.dump(data_config, f, default_flow_style=False, allow_unicode=True)
        
        self.logger.info(f"تم إنشاء ملف data.yaml: {data_yaml_path}")
        return data_yaml_path
    
    def _validate_dataset(self, data_yaml_path: Path):
        """التحقق من صحة مجموعة البيانات"""
        with open(data_yaml_path, 'r', encoding='utf-8') as f:
            data_config = yaml.safe_load(f)
        
        dataset_path = Path(data_config['path'])
        
        # التحقق من وجود دلائل الصور
        for split in ['train', 'val']:
            if split in data_config:
                images_dir = dataset_path / data_config[split]
                labels_dir = images_dir.parent / 'labels'
                
                if not images_dir.exists():
                    raise TrainingError(f"دليل الصور غير موجود: {images_dir}")
                
                if not labels_dir.exists():
                    raise TrainingError(f"دليل التسميات غير موجود: {labels_dir}")
                
                # عد الملفات
                image_files = list(images_dir.glob('*.[jp][pn]g')) + list(images_dir.glob('*.jpeg'))
                label_files = list(labels_dir.glob('*.txt'))
                
                self.logger.info(f"{split}: {len(image_files)} صورة، {len(label_files)} تسمية")
                
                if len(image_files) == 0:
                    raise TrainingError(f"لا توجد صور في {split}")
    
    def train_model(self, data_yaml_path: str, resume_from: Optional[str] = None) -> Dict:
        """
        تدريب نموذج YOLO
        
        Args:
            data_yaml_path: مسار ملف data.yaml
            resume_from: مسار الأوزان للاستئناف (اختياري)
            
        Returns:
            نتائج التدريب
        """
        try:
            self.logger.info("بدء تدريب نموذج YOLO...")
            self.training_stats['start_time'] = datetime.now()
            
            # إعداد النموذج
            if resume_from and Path(resume_from).exists():
                model = YOLO(resume_from)
                self.logger.info(f"استئناف التدريب من: {resume_from}")
            else:
                model_name = f"{self.training_config['model_version']}.pt"
                model = YOLO(model_name)
                self.logger.info(f"بدء تدريب جديد باستخدام: {model_name}")
            
            # إعداد معاملات التدريب
            train_args = {
                'data': data_yaml_path,
                'epochs': self.training_config['epochs'],
                'batch': self.training_config['batch_size'],
                'imgsz': self.training_config['input_size'],
                'lr0': self.training_config['learning_rate'],
                'momentum': self.training_config['momentum'],
                'weight_decay': self.training_config['weight_decay'],
                'augment': self.training_config['augmentation'],
                'mosaic': self.training_config['mosaic'],
                'mixup': self.training_config['mixup'],
                'copy_paste': self.training_config['copy_paste'],
                'save_period': self.training_config['save_period'],
                'val': True,
                'patience': self.training_config['patience'],
                'workers': self.training_config['workers'],
                'device': self.training_config['device'],
                'amp': self.training_config['amp'],
                'project': str(self.training_paths['project_dir']),
                'name': self.training_config['name'],
                'exist_ok': self.training_config['exist_ok'],
                'verbose': self.training_config['verbose']
            }
            
            # حفظ إعدادات التدريب
            self._save_training_config(train_args)
            
            # بدء التدريب
            self.logger.info("بدء عملية التدريب...")
            results = model.train(**train_args)
            
            # تحديث الإحصائيات
            self.training_stats['end_time'] = datetime.now()
            self.training_stats['total_epochs'] = self.training_config['epochs']
            self.training_stats['training_duration'] = (
                self.training_stats['end_time'] - self.training_stats['start_time']
            ).total_seconds()
            
            # استخراج أفضل النتائج
            if hasattr(results, 'results_dict'):
                self.training_stats['best_map'] = results.results_dict.get('metrics/mAP50-95(B)', 0.0)
                self.training_stats['final_loss'] = results.results_dict.get('train/box_loss', 0.0)
            
            # حفظ النتائج
            training_results = self._process_training_results(results, model)
            
            self.logger.info("اكتمل تدريب النموذج بنجاح!")
            return training_results
            
        except Exception as e:
            self.logger.error(f"فشل في تدريب النموذج: {e}")
            raise TrainingError(f"فشل في التدريب: {e}")
    
    def _save_training_config(self, train_args: Dict):
        """حفظ إعدادات التدريب"""
        config_path = self.training_paths['config_backup_dir'] / 'training_config.yaml'
        
        # إضافة معلومات إضافية
        full_config = {
            'training_arguments': train_args,
            'system_info': {
                'torch_version': torch.__version__ if TORCH_AVAILABLE else 'N/A',
                'cuda_available': torch.cuda.is_available() if TORCH_AVAILABLE else False,
                'device_count': torch.cuda.device_count() if TORCH_AVAILABLE and torch.cuda.is_available() else 0,
                'training_start_time': self.training_stats['start_time'].isoformat()
            },
            'rescue_specific_config': {
                'specialized_for': 'search_and_rescue',
                'target_classes': [
                    'person', 'life_jacket', 'life_boat', 'debris',
                    'aircraft_wreckage', 'vehicle_wreckage', 'emergency_signal', 'rescue_equipment'
                ],
                'environment_types': ['sea', 'desert', 'coast', 'urban', 'forest', 'mountain']
            }
        }
        
        with open(config_path, 'w', encoding='utf-8') as f:
            yaml.dump(full_config, f, default_flow_style=False, allow_unicode=True)
        
        self.logger.info(f"تم حفظ إعدادات التدريب: {config_path}")
    
    def _process_training_results(self, results, model) -> Dict:
        """معالجة وتنظيم نتائج التدريب"""
        results_summary = {
            'training_completed': True,
            'training_stats': self.training_stats,
            'model_info': {
                'model_type': self.training_config['model_version'],
                'input_size': self.training_config['input_size'],
                'total_parameters': sum(p.numel() for p in model.model.parameters()) if hasattr(model, 'model') else 0
            },
            'training_paths': {
                'weights_dir': str(self.training_paths['weights_dir']),
                'results_dir': str(self.training_paths['results_dir']),
                'logs_dir': str(self.training_paths['logs_dir'])
            },
            'performance_metrics': {},
            'saved_models': []
        }
        
        # البحث عن الأوزان المحفوظة
        weights_dir = self.training_paths['training_dir'] / 'weights'
        if weights_dir.exists():
            weight_files = list(weights_dir.glob('*.pt'))
            results_summary['saved_models'] = [str(f) for f in weight_files]
        
        # البحث عن ملفات النتائج
        results_dir = self.training_paths['training_dir']
        if results_dir.exists():
            # ملف النتائج
            results_csv = results_dir / 'results.csv'
            if results_csv.exists():
                results_summary['results_file'] = str(results_csv)
            
            # الرسوم البيانية
            plot_files = list(results_dir.glob('*.png'))
            results_summary['training_plots'] = [str(f) for f in plot_files]
        
        # حفظ ملخص النتائج
        summary_path = self.training_paths['results_dir'] / 'training_summary.json'
        with open(summary_path, 'w', encoding='utf-8') as f:
            json.dump(results_summary, f, ensure_ascii=False, indent=2, default=str)
        
        return results_summary
    
    def evaluate_model(self, model_path: str, data_yaml_path: str) -> Dict:
        """
        تقييم النموذج المدرب
        
        Args:
            model_path: مسار النموذج المدرب
            data_yaml_path: مسار ملف data.yaml
            
        Returns:
            نتائج التقييم
        """
        try:
            self.logger.info("بدء تقييم النموذج...")
            
            # تحميل النموذج
            model = YOLO(model_path)
            
            # تشغيل التقييم
            eval_results = model.val(
                data=data_yaml_path,
                imgsz=self.training_config['input_size'],
                batch=self.training_config['batch_size'],
                device=self.training_config['device'],
                verbose=self.training_config['verbose']
            )
            
            # معالجة نتائج التقييم
            evaluation_summary = {
                'model_path': model_path,
                'evaluation_timestamp': datetime.now().isoformat(),
                'metrics': {},
                'class_performance': {}
            }
            
            # استخراج المقاييس
            if hasattr(eval_results, 'results_dict'):
                metrics = eval_results.results_dict
                evaluation_summary['metrics'] = {
                    'mAP50': metrics.get('metrics/mAP50(B)', 0.0),
                    'mAP50-95': metrics.get('metrics/mAP50-95(B)', 0.0),
                    'precision': metrics.get('metrics/precision(B)', 0.0),
                    'recall': metrics.get('metrics/recall(B)', 0.0)
                }
            
            # حفظ نتائج التقييم
            eval_path = self.training_paths['results_dir'] / 'evaluation_results.json'
            with open(eval_path, 'w', encoding='utf-8') as f:
                json.dump(evaluation_summary, f, ensure_ascii=False, indent=2, default=str)
            
            self.logger.info("اكتمل تقييم النموذج")
            return evaluation_summary
            
        except Exception as e:
            self.logger.error(f"فشل في تقييم النموذج: {e}")
            raise TrainingError(f"فشل في التقييم: {e}")
    
    def export_model(self, model_path: str, export_formats: List[str] = None) -> Dict:
        """
        تصدير النموذج بتنسيقات مختلفة
        
        Args:
            model_path: مسار النموذج
            export_formats: قائمة تنسيقات التصدير
            
        Returns:
            معلومات النماذج المصدرة
        """
        try:
            if export_formats is None:
                export_formats = ['onnx', 'torchscript']
            
            self.logger.info(f"تصدير النموذج بتنسيقات: {export_formats}")
            
            model = YOLO(model_path)
            exported_models = {}
            
            for format_type in export_formats:
                try:
                    if format_type.lower() == 'onnx':
                        exported_path = model.export(format='onnx')
                        exported_models['onnx'] = str(exported_path)
                    elif format_type.lower() == 'torchscript':
                        exported_path = model.export(format='torchscript')
                        exported_models['torchscript'] = str(exported_path)
                    elif format_type.lower() == 'tflite':
                        exported_path = model.export(format='tflite')
                        exported_models['tflite'] = str(exported_path)
                    
                    self.logger.info(f"تم تصدير {format_type}: {exported_path}")
                    
                except Exception as e:
                    self.logger.warning(f"فشل تصدير {format_type}: {e}")
            
            return exported_models
            
        except Exception as e:
            self.logger.error(f"فشل في تصدير النموذج: {e}")
            return {}
    
    def get_training_progress(self) -> Dict:
        """الحصول على تقدم التدريب"""
        progress_info = {
            'training_active': self.training_stats['start_time'] is not None and self.training_stats['end_time'] is None,
            'training_stats': self.training_stats,
            'training_paths': {k: str(v) for k, v in self.training_paths.items()}
        }
        
        # قراءة ملف النتائج إذا كان متوفراً
        results_file = self.training_paths['training_dir'] / 'results.csv'
        if results_file.exists():
            try:
                import pandas as pd
                results_df = pd.read_csv(results_file)
                if not results_df.empty:
                    latest_epoch = results_df.iloc[-1]
                    progress_info['latest_metrics'] = {
                        'epoch': int(latest_epoch.get('epoch', 0)),
                        'train_loss': float(latest_epoch.get('train/box_loss', 0)),
                        'val_map50': float(latest_epoch.get('metrics/mAP50(B)', 0)),
                        'val_map50_95': float(latest_epoch.get('metrics/mAP50-95(B)', 0))
                    }
            except Exception as e:
                self.logger.warning(f"فشل في قراءة ملف النتائج: {e}")
        
        return progress_info
