"""
وحدة التصور المتقدم للتحليلات التكتيكية
Advanced Tactical Visualization Module for Search and Rescue Analytics
====================================================================

وحدة متخصصة لإنشاء رسوم بيانية وخرائط تفاعلية متقدمة
لدعم اتخاذ القرار في عمليات الإنقاذ والبحث.
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import logging
from typing import Dict, List, Tuple, Optional, Any
from pathlib import Path
from datetime import datetime
import warnings

try:
    import plotly.express as px
    import plotly.graph_objects as go
    from plotly.subplots import make_subplots
    import plotly.figure_factory as ff
    PLOTLY_AVAILABLE = True
except ImportError:
    PLOTLY_AVAILABLE = False

try:
    import folium
    from folium import plugins
    FOLIUM_AVAILABLE = True
except ImportError:
    FOLIUM_AVAILABLE = False

warnings.filterwarnings('ignore')


class TacticalVisualizationEngine:
    """
    محرك التصور التكتيكي المتقدم
    Advanced tactical visualization engine
    """
    
    def __init__(self, config: Dict):
        """
        تهيئة محرك التصور
        
        Args:
            config: إعدادات التكوين
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # إعدادات التصور
        self.viz_settings = self._load_visualization_settings()
        
        # ألوان متخصصة للإنقاذ والبحث
        self.rescue_colors = {
            'sea': '#1f77b4',
            'desert': '#ff7f0e',
            'coast': '#2ca02c', 
            'urban': '#d62728',
            'forest': '#9467bd',
            'mountain': '#8c564b',
            'critical': '#ff0000',
            'high': '#ff8c00',
            'medium': '#ffd700',
            'low': '#90ee90',
            'success': '#00ff00',
            'warning': '#ffff00',
            'danger': '#ff0000'
        }
        
        # إعداد الرسوم البيانية
        self._setup_plotting_style()
    
    def _load_visualization_settings(self) -> Dict:
        """تحميل إعدادات التصور"""
        return {
            'figure_size': self.config.get('figure_size', (15, 10)),
            'dpi': self.config.get('dpi', 300),
            'style': self.config.get('plot_style', 'seaborn-v0_8-darkgrid'),
            'color_palette': self.config.get('color_palette', 'viridis'),
            'font_size': self.config.get('font_size', 12),
            'title_size': self.config.get('title_size', 16),
            'enable_arabic_support': self.config.get('enable_arabic_support', True),
            'export_formats': self.config.get('export_formats', ['png', 'html', 'pdf']),
            'interactive_plots': self.config.get('enable_interactive_plots', True)
        }
    
    def _setup_plotting_style(self):
        """إعداد نمط الرسوم البيانية"""
        plt.style.use(self.viz_settings['style'])
        plt.rcParams['figure.figsize'] = self.viz_settings['figure_size']
        plt.rcParams['figure.dpi'] = self.viz_settings['dpi']
        plt.rcParams['font.size'] = self.viz_settings['font_size']
        plt.rcParams['axes.titlesize'] = self.viz_settings['title_size']
        
        # دعم النصوص العربية
        if self.viz_settings['enable_arabic_support']:
            plt.rcParams['font.family'] = ['DejaVu Sans', 'Arial Unicode MS', 'Tahoma']
            plt.rcParams['axes.unicode_minus'] = False
    
    def create_tactical_dashboard(self, analysis_results: Dict, output_dir: str) -> Dict:
        """
        إنشاء لوحة تحكم تكتيكية شاملة
        
        Args:
            analysis_results: نتائج التحليل التكتيكي
            output_dir: دليل الإخراج
            
        Returns:
            معلومات الرسوم البيانية المولدة
        """
        try:
            self.logger.info("إنشاء لوحة التحكم التكتيكية...")
            
            output_path = Path(output_dir)
            output_path.mkdir(parents=True, exist_ok=True)
            
            dashboard_components = {}
            
            # 1. نظرة عامة تنفيذية
            executive_overview = self._create_executive_overview(analysis_results, output_path)
            dashboard_components['executive_overview'] = executive_overview
            
            # 2. خرائط تكتيكية مكانية
            spatial_maps = self._create_spatial_tactical_maps(analysis_results, output_path)
            dashboard_components['spatial_maps'] = spatial_maps
            
            # 3. تحليلات زمنية تفاعلية
            temporal_charts = self._create_temporal_analysis_charts(analysis_results, output_path)
            dashboard_components['temporal_charts'] = temporal_charts
            
            # 4. مقاييس الأداء التشغيلي
            performance_metrics = self._create_performance_dashboards(analysis_results, output_path)
            dashboard_components['performance_metrics'] = performance_metrics
            
            # 5. خرائط حرارية للكشف
            detection_heatmaps = self._create_detection_heatmaps(analysis_results, output_path)
            dashboard_components['detection_heatmaps'] = detection_heatmaps
            
            # 6. رسوم بيانية للرؤى التكتيكية
            insights_visualizations = self._create_insights_visualizations(analysis_results, output_path)
            dashboard_components['insights_visualizations'] = insights_visualizations
            
            # 7. لوحة تحكم تفاعلية موحدة
            if PLOTLY_AVAILABLE and self.viz_settings['interactive_plots']:
                interactive_dashboard = self._create_interactive_dashboard(analysis_results, output_path)
                dashboard_components['interactive_dashboard'] = interactive_dashboard
            
            self.logger.info(f"تم إنشاء {len(dashboard_components)} مكون للوحة التحكم")
            return dashboard_components
            
        except Exception as e:
            self.logger.error(f"فشل في إنشاء لوحة التحكم التكتيكية: {e}")
            return {}
    
    def _create_executive_overview(self, analysis_results: Dict, output_path: Path) -> str:
        """إنشاء نظرة عامة تنفيذية"""
        fig, axes = plt.subplots(2, 3, figsize=(20, 12))
        fig.suptitle('نظرة عامة تنفيذية - عمليات الإنقاذ والبحث', fontsize=20, fontweight='bold')
        
        # 1. إحصائيات أساسية
        if 'basic_analysis' in analysis_results:
            basic_data = analysis_results['basic_analysis']
            self._plot_basic_statistics(axes[0, 0], basic_data)
        
        # 2. توزيع البيئات
        if 'basic_analysis' in analysis_results and 'environment_analysis' in analysis_results['basic_analysis']:
            env_data = analysis_results['basic_analysis']['environment_analysis']
            self._plot_environment_distribution(axes[0, 1], env_data)
        
        # 3. معدلات الكشف
        if 'basic_analysis' in analysis_results and 'detection_summary' in analysis_results['basic_analysis']:
            detection_data = analysis_results['basic_analysis']['detection_summary']
            self._plot_detection_rates(axes[0, 2], detection_data)
        
        # 4. مقاييس الجودة
        if 'basic_analysis' in analysis_results:
            quality_data = analysis_results['basic_analysis'].get('dataset_overview', {})
            self._plot_quality_metrics(axes[1, 0], quality_data)
        
        # 5. الكفاءة التشغيلية
        if 'efficiency_analysis' in analysis_results:
            efficiency_data = analysis_results['efficiency_analysis']
            self._plot_efficiency_overview(axes[1, 1], efficiency_data)
        
        # 6. مؤشرات الأولوية
        if 'basic_analysis' in analysis_results:
            priority_data = analysis_results['basic_analysis'].get('priority_distribution', {})
            self._plot_priority_distribution(axes[1, 2], priority_data)
        
        plt.tight_layout()
        
        overview_path = output_path / 'executive_overview.png'
        plt.savefig(overview_path, dpi=self.viz_settings['dpi'], bbox_inches='tight')
        plt.close()
        
        return str(overview_path)
    
    def _plot_basic_statistics(self, ax, basic_data: Dict):
        """رسم الإحصائيات الأساسية"""
        if 'dataset_overview' in basic_data:
            overview = basic_data['dataset_overview']
            
            stats = [
                ('إجمالي الصور', overview.get('total_images', 0)),
                ('التغطية الجغرافية', f"{overview.get('geographic_coverage', {}).get('geographic_coverage_percentage', 0):.1f}%"),
                ('متوسط الجودة', f"{overview.get('data_quality_distribution', {}).get('mean', 0):.1f}")
            ]
            
            for i, (label, value) in enumerate(stats):
                ax.text(0.1, 0.8 - i*0.3, f'{label}:', fontsize=12, fontweight='bold')
                ax.text(0.6, 0.8 - i*0.3, str(value), fontsize=12, color='blue')
        
        ax.set_xlim(0, 1)
        ax.set_ylim(0, 1)
        ax.set_title('الإحصائيات الأساسية')
        ax.axis('off')
    
    def _plot_environment_distribution(self, ax, env_data: Dict):
        """رسم توزيع البيئات"""
        if 'distribution_percentages' in env_data:
            environments = list(env_data['distribution_percentages'].keys())
            percentages = list(env_data['distribution_percentages'].values())
            
            colors = [self.rescue_colors.get(env, '#gray') for env in environments]
            
            wedges, texts, autotexts = ax.pie(
                percentages, 
                labels=environments, 
                autopct='%1.1f%%',
                colors=colors,
                startangle=90
            )
            
            ax.set_title('توزيع البيئات')
    
    def _plot_detection_rates(self, ax, detection_data: Dict):
        """رسم معدلات الكشف"""
        detection_rate = detection_data.get('detection_rate', 0)
        images_with_targets = detection_data.get('images_with_targets', 0)
        total_images = images_with_targets + detection_data.get('images_without_targets', 0)
        
        # رسم دائري للكشف
        sizes = [images_with_targets, total_images - images_with_targets]
        labels = ['تحتوي على أهداف', 'خالية من الأهداف']
        colors = [self.rescue_colors['success'], self.rescue_colors['low']]
        
        ax.pie(sizes, labels=labels, autopct='%1.1f%%', colors=colors, startangle=90)
        ax.set_title(f'معدل الكشف: {detection_rate:.1f}%')
    
    def _plot_quality_metrics(self, ax, quality_data: Dict):
        """رسم مقاييس الجودة"""
        if 'data_quality_distribution' in quality_data:
            quality_dist = quality_data['data_quality_distribution']
            
            # رسم مؤشر الجودة
            quality_score = quality_dist.get('mean', 0)
            
            # تحديد اللون حسب الجودة
            if quality_score >= 80:
                color = self.rescue_colors['success']
                status = 'ممتاز'
            elif quality_score >= 70:
                color = self.rescue_colors['medium']
                status = 'جيد'
            elif quality_score >= 60:
                color = self.rescue_colors['warning']
                status = 'مقبول'
            else:
                color = self.rescue_colors['danger']
                status = 'يحتاج تحسين'
            
            # رسم مؤشر دائري
            theta = np.linspace(0, 2*np.pi, 100)
            r = np.ones_like(theta)
            
            ax.fill_between(theta, 0, r, alpha=0.3, color='lightgray')
            
            # رسم القيمة الفعلية
            fill_angle = 2 * np.pi * (quality_score / 100)
            theta_fill = np.linspace(0, fill_angle, int(fill_angle * 100 / (2*np.pi)))
            r_fill = np.ones_like(theta_fill)
            
            ax.fill_between(theta_fill, 0, r_fill, alpha=0.7, color=color)
            
            # إضافة النص
            ax.text(0, 0, f'{quality_score:.1f}%\n{status}', 
                   ha='center', va='center', fontsize=12, fontweight='bold')
            
            ax.set_xlim(-1.2, 1.2)
            ax.set_ylim(-1.2, 1.2)
            ax.set_title('مؤشر الجودة العام')
            ax.axis('off')
    
    def _plot_efficiency_overview(self, ax, efficiency_data: Dict):
        """رسم نظرة عامة على الكفاءة"""
        if 'performance_metrics' in efficiency_data:
            metrics = efficiency_data['performance_metrics']
            
            # استخراج المقاييس الرئيسية
            productivity = metrics.get('productivity_rate_images_per_hour', 0)
            detection_rate = metrics.get('effective_detection_rate_percentage', 0)
            confidence = metrics.get('average_confidence_score', 0) * 100
            quality = metrics.get('average_quality_score', 0)
            
            # إنشاء رسم رادار
            categories = ['الإنتاجية', 'معدل الكشف', 'الثقة', 'الجودة']
            values = [
                min(productivity / 10, 10),  # تطبيع الإنتاجية
                detection_rate / 10,
                confidence / 10,
                quality / 10
            ]
            
            # إضافة النقطة الأولى في النهاية لإغلاق الشكل
            values += values[:1]
            
            angles = np.linspace(0, 2*np.pi, len(categories), endpoint=False).tolist()
            angles += angles[:1]
            
            ax.plot(angles, values, 'o-', linewidth=2, color=self.rescue_colors['high'])
            ax.fill(angles, values, alpha=0.25, color=self.rescue_colors['high'])
            
            ax.set_xticks(angles[:-1])
            ax.set_xticklabels(categories)
            ax.set_ylim(0, 10)
            ax.set_title('مؤشرات الكفاءة التشغيلية')
            ax.grid(True)
    
    def _plot_priority_distribution(self, ax, priority_data: Dict):
        """رسم توزيع الأولويات"""
        if priority_data:
            priorities = list(priority_data.keys())
            counts = list(priority_data.values())
            
            colors = [self.rescue_colors.get(p, '#gray') for p in priorities]
            
            bars = ax.bar(priorities, counts, color=colors)
            
            # إضافة قيم على الأعمدة
            for bar, count in zip(bars, counts):
                height = bar.get_height()
                ax.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                       f'{count}', ha='center', va='bottom')
            
            ax.set_title('توزيع مستويات الأولوية')
            ax.set_ylabel('عدد الصور')
            plt.setp(ax.get_xticklabels(), rotation=45)
    
    def _create_spatial_tactical_maps(self, analysis_results: Dict, output_path: Path) -> Dict:
        """إنشاء خرائط تكتيكية مكانية"""
        spatial_maps = {}
        
        if 'spatial_analysis' in analysis_results and FOLIUM_AVAILABLE:
            spatial_data = analysis_results['spatial_analysis']
            
            # خريطة النقاط الساخنة
            if 'hotspot_detection' in spatial_data:
                hotspot_map = self._create_hotspot_map(spatial_data['hotspot_detection'], output_path)
                spatial_maps['hotspot_map'] = hotspot_map
            
            # خريطة التجميع المكاني
            if 'clustering_analysis' in spatial_data:
                cluster_map = self._create_cluster_map(spatial_data['clustering_analysis'], output_path)
                spatial_maps['cluster_map'] = cluster_map
        
        return spatial_maps
    
    def _create_hotspot_map(self, hotspot_data: Dict, output_path: Path) -> str:
        """إنشاء خريطة النقاط الساخنة"""
        if 'high_density_zones' not in hotspot_data:
            return ""
        
        # إنشاء خريطة أساسية
        center_lat, center_lon = 25.0, 45.0  # إحداثيات افتراضية للمنطقة العربية
        m = folium.Map(location=[center_lat, center_lon], zoom_start=6)
        
        # إضافة النقاط الساخنة
        for zone in hotspot_data['high_density_zones']:
            center_coords = zone['center_coordinates']
            
            folium.CircleMarker(
                location=[center_coords[1], center_coords[0]],  # lat, lon
                radius=zone['point_count'] * 2,
                popup=f"منطقة ساخنة: {zone['point_count']} كشف",
                color='red',
                fill=True,
                fillColor='red',
                fillOpacity=0.6
            ).add_to(m)
        
        # حفظ الخريطة
        map_path = output_path / 'tactical_hotspot_map.html'
        m.save(str(map_path))
        
        return str(map_path)
    
    def _create_cluster_map(self, cluster_data: Dict, output_path: Path) -> str:
        """إنشاء خريطة التجميع المكاني"""
        if 'dbscan' not in cluster_data:
            return ""
        
        # إنشاء خريطة أساسية
        center_lat, center_lon = 25.0, 45.0
        m = folium.Map(location=[center_lat, center_lon], zoom_start=6)
        
        # إضافة معلومات التجميع
        dbscan_info = cluster_data['dbscan']
        
        # إضافة نص معلوماتي
        info_html = f"""
        <div style="position: fixed; 
                    top: 10px; left: 50px; width: 200px; height: 90px; 
                    background-color: white; border:2px solid grey; z-index:9999; 
                    font-size:14px; padding: 10px">
        <p><b>تحليل التجميع المكاني</b></p>
        <p>عدد المجموعات: {dbscan_info['n_clusters']}</p>
        <p>النقاط الشاذة: {dbscan_info['n_noise_points']}</p>
        <p>نقاط الجودة: {dbscan_info['silhouette_score']:.3f}</p>
        </div>
        """
        m.get_root().html.add_child(folium.Element(info_html))
        
        map_path = output_path / 'tactical_cluster_map.html'
        m.save(str(map_path))
        
        return str(map_path)
    
    def _create_temporal_analysis_charts(self, analysis_results: Dict, output_path: Path) -> List[str]:
        """إنشاء رسوم التحليل الزمني"""
        temporal_charts = []
        
        if 'temporal_analysis' in analysis_results:
            temporal_data = analysis_results['temporal_analysis']
            
            # رسم الأنماط الزمنية
            if 'seasonality_analysis' in temporal_data:
                seasonality_chart = self._create_seasonality_chart(temporal_data['seasonality_analysis'], output_path)
                temporal_charts.append(seasonality_chart)
            
            # رسم اكتشاف القمم
            if 'peak_detection' in temporal_data:
                peaks_chart = self._create_peaks_chart(temporal_data['peak_detection'], output_path)
                temporal_charts.append(peaks_chart)
        
        return temporal_charts
    
    def _create_seasonality_chart(self, seasonality_data: Dict, output_path: Path) -> str:
        """إنشاء رسم الأنماط الموسمية"""
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('تحليل الأنماط الزمنية والموسمية', fontsize=16, fontweight='bold')
        
        # النمط اليومي (حسب الساعة)
        if 'hourly_pattern' in seasonality_data:
            hourly_data = seasonality_data['hourly_pattern']['distribution']
            hours = list(hourly_data.keys())
            counts = list(hourly_data.values())
            
            axes[0, 0].bar(hours, counts, color=self.rescue_colors['sea'])
            axes[0, 0].set_title('النمط اليومي (حسب الساعة)')
            axes[0, 0].set_xlabel('الساعة')
            axes[0, 0].set_ylabel('عدد العمليات')
        
        # النمط الأسبوعي
        if 'weekly_pattern' in seasonality_data:
            weekly_data = seasonality_data['weekly_pattern']['distribution']
            days = list(weekly_data.keys())
            counts = list(weekly_data.values())
            
            axes[0, 1].bar(days, counts, color=self.rescue_colors['coast'])
            axes[0, 1].set_title('النمط الأسبوعي')
            axes[0, 1].set_xlabel('اليوم')
            axes[0, 1].set_ylabel('عدد العمليات')
            plt.setp(axes[0, 1].get_xticklabels(), rotation=45)
        
        # النمط الشهري (إذا متوفر)
        if 'monthly_pattern' in seasonality_data:
            monthly_data = seasonality_data['monthly_pattern']['distribution']
            months = list(monthly_data.keys())
            counts = list(monthly_data.values())
            
            axes[1, 0].bar(months, counts, color=self.rescue_colors['desert'])
            axes[1, 0].set_title('النمط الشهري')
            axes[1, 0].set_xlabel('الشهر')
            axes[1, 0].set_ylabel('عدد العمليات')
            plt.setp(axes[1, 0].get_xticklabels(), rotation=45)
        
        # ملخص الأنماط
        pattern_summary = []
        if 'hourly_pattern' in seasonality_data:
            peak_hour = seasonality_data['hourly_pattern'].get('peak_hour', 'غير محدد')
            pattern_summary.append(f'ساعة الذروة: {peak_hour}')
        
        if 'weekly_pattern' in seasonality_data:
            peak_day = seasonality_data['weekly_pattern'].get('peak_day', 'غير محدد')
            pattern_summary.append(f'يوم الذروة: {peak_day}')
        
        axes[1, 1].text(0.1, 0.8, 'ملخص الأنماط:', fontsize=14, fontweight='bold')
        for i, summary in enumerate(pattern_summary):
            axes[1, 1].text(0.1, 0.6 - i*0.2, summary, fontsize=12)
        
        axes[1, 1].set_xlim(0, 1)
        axes[1, 1].set_ylim(0, 1)
        axes[1, 1].axis('off')
        
        plt.tight_layout()
        
        chart_path = output_path / 'temporal_seasonality_analysis.png'
        plt.savefig(chart_path, dpi=self.viz_settings['dpi'], bbox_inches='tight')
        plt.close()
        
        return str(chart_path)
    
    def _create_peaks_chart(self, peaks_data: Dict, output_path: Path) -> str:
        """إنشاء رسم اكتشاف القمم"""
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(14, 10))
        fig.suptitle('تحليل قمم النشاط والفترات الهادئة', fontsize=16, fontweight='bold')
        
        # معلومات القمم
        total_peaks = peaks_data.get('total_peaks', 0)
        avg_peak_intensity = peaks_data.get('average_peak_intensity', 0)
        max_peak_intensity = peaks_data.get('max_peak_intensity', 0)
        
        # رسم إحصائيات القمم
        peak_stats = ['إجمالي القمم', 'متوسط الكثافة', 'أقصى كثافة']
        peak_values = [total_peaks, avg_peak_intensity, max_peak_intensity]
        
        bars1 = ax1.bar(peak_stats, peak_values, color=[self.rescue_colors['critical'], 
                                                       self.rescue_colors['high'], 
                                                       self.rescue_colors['medium']])
        
        ax1.set_title('إحصائيات قمم النشاط')
        ax1.set_ylabel('القيمة')
        
        # إضافة قيم على الأعمدة
        for bar, value in zip(bars1, peak_values):
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                    f'{value:.1f}', ha='center', va='bottom')
        
        # معلومات الفترات الهادئة
        if 'quiet_periods' in peaks_data:
            quiet_data = peaks_data['quiet_periods']
            quiet_hours = quiet_data.get('total_quiet_hours', 0)
            avg_quiet_intensity = quiet_data.get('average_quiet_intensity', 0)
            longest_quiet = quiet_data.get('longest_quiet_period_hours', 0)
            
            quiet_stats = ['ساعات الهدوء', 'متوسط الكثافة', 'أطول فترة هدوء']
            quiet_values = [quiet_hours, avg_quiet_intensity, longest_quiet]
            
            bars2 = ax2.bar(quiet_stats, quiet_values, color=[self.rescue_colors['low'], 
                                                             self.rescue_colors['medium'], 
                                                             self.rescue_colors['warning']])
            
            ax2.set_title('إحصائيات الفترات الهادئة')
            ax2.set_ylabel('القيمة')
            
            # إضافة قيم على الأعمدة
            for bar, value in zip(bars2, quiet_values):
                height = bar.get_height()
                ax2.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                        f'{value:.1f}', ha='center', va='bottom')
        
        plt.tight_layout()
        
        chart_path = output_path / 'temporal_peaks_analysis.png'
        plt.savefig(chart_path, dpi=self.viz_settings['dpi'], bbox_inches='tight')
        plt.close()
        
        return str(chart_path)
