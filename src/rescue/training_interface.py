"""
واجهة تدريب YOLO التفاعلية للإنقاذ والبحث
Interactive YOLO Training Interface for Search and Rescue
========================================================

واجهة تفاعلية لإدارة وتشغيل تدريب نماذج YOLO
مع مراقبة التقدم والتحكم في العملية.
"""

import os
import sys
import json
import time
import threading
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Optional, Callable
import logging

try:
    import tkinter as tk
    from tkinter import ttk, filedialog, messagebox, scrolledtext
    GUI_AVAILABLE = True
except ImportError:
    GUI_AVAILABLE = False

from .yolo_training_module import YOLOTrainingManager
from ..utils.exceptions import TrainingError


class TrainingProgressMonitor:
    """
    مراقب تقدم التدريب
    Training progress monitor
    """
    
    def __init__(self, training_manager: YOLOTrainingManager, update_callback: Optional[Callable] = None):
        """
        تهيئة مراقب التقدم
        
        Args:
            training_manager: مدير التدريب
            update_callback: دالة تحديث الواجهة
        """
        self.training_manager = training_manager
        self.update_callback = update_callback
        self.monitoring = False
        self.monitor_thread = None
        
        self.logger = logging.getLogger(__name__)
    
    def start_monitoring(self):
        """بدء مراقبة التقدم"""
        if not self.monitoring:
            self.monitoring = True
            self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
            self.monitor_thread.start()
            self.logger.info("بدء مراقبة تقدم التدريب")
    
    def stop_monitoring(self):
        """إيقاف مراقبة التقدم"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=1)
        self.logger.info("تم إيقاف مراقبة التقدم")
    
    def _monitor_loop(self):
        """حلقة مراقبة التقدم"""
        while self.monitoring:
            try:
                progress_info = self.training_manager.get_training_progress()
                
                if self.update_callback:
                    self.update_callback(progress_info)
                
                time.sleep(5)  # تحديث كل 5 ثوان
                
            except Exception as e:
                self.logger.error(f"خطأ في مراقبة التقدم: {e}")
                time.sleep(10)


class YOLOTrainingGUI:
    """
    واجهة رسومية لتدريب YOLO
    Graphical interface for YOLO training
    """
    
    def __init__(self):
        """تهيئة الواجهة الرسومية"""
        if not GUI_AVAILABLE:
            raise ImportError("tkinter غير متوفر للواجهة الرسومية")
        
        self.root = tk.Tk()
        self.root.title("واجهة تدريب YOLO للإنقاذ والبحث")
        self.root.geometry("1000x700")
        
        # متغيرات الواجهة
        self.dataset_path = tk.StringVar()
        self.model_version = tk.StringVar(value="yolov8m")
        self.epochs = tk.IntVar(value=100)
        self.batch_size = tk.IntVar(value=16)
        self.learning_rate = tk.DoubleVar(value=0.01)
        self.device = tk.StringVar(value="auto")
        
        # مدير التدريب
        self.training_manager = None
        self.progress_monitor = None
        self.training_thread = None
        
        # إعداد الواجهة
        self._setup_ui()
        
        self.logger = logging.getLogger(__name__)
    
    def _setup_ui(self):
        """إعداد عناصر الواجهة"""
        # إطار رئيسي
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # تكوين الشبكة
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # قسم إعدادات البيانات
        self._create_dataset_section(main_frame, 0)
        
        # قسم إعدادات النموذج
        self._create_model_section(main_frame, 1)
        
        # قسم إعدادات التدريب
        self._create_training_section(main_frame, 2)
        
        # قسم التحكم
        self._create_control_section(main_frame, 3)
        
        # قسم مراقبة التقدم
        self._create_progress_section(main_frame, 4)
        
        # قسم السجلات
        self._create_logs_section(main_frame, 5)
    
    def _create_dataset_section(self, parent, row):
        """إنشاء قسم إعدادات البيانات"""
        frame = ttk.LabelFrame(parent, text="إعدادات مجموعة البيانات", padding="5")
        frame.grid(row=row, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        frame.columnconfigure(1, weight=1)
        
        ttk.Label(frame, text="مسار مجموعة البيانات:").grid(row=0, column=0, sticky=tk.W, padx=5)
        ttk.Entry(frame, textvariable=self.dataset_path, width=50).grid(row=0, column=1, sticky=(tk.W, tk.E), padx=5)
        ttk.Button(frame, text="تصفح", command=self._browse_dataset).grid(row=0, column=2, padx=5)
    
    def _create_model_section(self, parent, row):
        """إنشاء قسم إعدادات النموذج"""
        frame = ttk.LabelFrame(parent, text="إعدادات النموذج", padding="5")
        frame.grid(row=row, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        
        ttk.Label(frame, text="إصدار النموذج:").grid(row=0, column=0, sticky=tk.W, padx=5)
        model_combo = ttk.Combobox(frame, textvariable=self.model_version, 
                                  values=["yolov8n", "yolov8s", "yolov8m", "yolov8l", "yolov8x"])
        model_combo.grid(row=0, column=1, sticky=tk.W, padx=5)
        
        ttk.Label(frame, text="الجهاز:").grid(row=0, column=2, sticky=tk.W, padx=5)
        device_combo = ttk.Combobox(frame, textvariable=self.device, 
                                   values=["auto", "cpu", "cuda", "mps"])
        device_combo.grid(row=0, column=3, sticky=tk.W, padx=5)
    
    def _create_training_section(self, parent, row):
        """إنشاء قسم إعدادات التدريب"""
        frame = ttk.LabelFrame(parent, text="إعدادات التدريب", padding="5")
        frame.grid(row=row, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        
        # الصف الأول
        ttk.Label(frame, text="عدد الحقب:").grid(row=0, column=0, sticky=tk.W, padx=5)
        ttk.Spinbox(frame, from_=1, to=1000, textvariable=self.epochs, width=10).grid(row=0, column=1, sticky=tk.W, padx=5)
        
        ttk.Label(frame, text="حجم الدفعة:").grid(row=0, column=2, sticky=tk.W, padx=5)
        ttk.Spinbox(frame, from_=1, to=128, textvariable=self.batch_size, width=10).grid(row=0, column=3, sticky=tk.W, padx=5)
        
        # الصف الثاني
        ttk.Label(frame, text="معدل التعلم:").grid(row=1, column=0, sticky=tk.W, padx=5)
        ttk.Entry(frame, textvariable=self.learning_rate, width=10).grid(row=1, column=1, sticky=tk.W, padx=5)
    
    def _create_control_section(self, parent, row):
        """إنشاء قسم التحكم"""
        frame = ttk.LabelFrame(parent, text="التحكم في التدريب", padding="5")
        frame.grid(row=row, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        
        self.start_button = ttk.Button(frame, text="بدء التدريب", command=self._start_training)
        self.start_button.grid(row=0, column=0, padx=5)
        
        self.stop_button = ttk.Button(frame, text="إيقاف التدريب", command=self._stop_training, state=tk.DISABLED)
        self.stop_button.grid(row=0, column=1, padx=5)
        
        self.evaluate_button = ttk.Button(frame, text="تقييم النموذج", command=self._evaluate_model, state=tk.DISABLED)
        self.evaluate_button.grid(row=0, column=2, padx=5)
        
        self.export_button = ttk.Button(frame, text="تصدير النموذج", command=self._export_model, state=tk.DISABLED)
        self.export_button.grid(row=0, column=3, padx=5)
    
    def _create_progress_section(self, parent, row):
        """إنشاء قسم مراقبة التقدم"""
        frame = ttk.LabelFrame(parent, text="تقدم التدريب", padding="5")
        frame.grid(row=row, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        frame.columnconfigure(0, weight=1)
        
        # شريط التقدم
        self.progress_bar = ttk.Progressbar(frame, mode='determinate')
        self.progress_bar.grid(row=0, column=0, sticky=(tk.W, tk.E), padx=5, pady=2)
        
        # معلومات التقدم
        self.progress_info = tk.StringVar(value="جاهز للبدء")
        ttk.Label(frame, textvariable=self.progress_info).grid(row=1, column=0, sticky=tk.W, padx=5)
        
        # إطار المقاييس
        metrics_frame = ttk.Frame(frame)
        metrics_frame.grid(row=2, column=0, sticky=(tk.W, tk.E), pady=5)
        metrics_frame.columnconfigure((0, 1, 2, 3), weight=1)
        
        self.epoch_label = ttk.Label(metrics_frame, text="الحقبة: -")
        self.epoch_label.grid(row=0, column=0, sticky=tk.W)
        
        self.loss_label = ttk.Label(metrics_frame, text="الخسارة: -")
        self.loss_label.grid(row=0, column=1, sticky=tk.W)
        
        self.map50_label = ttk.Label(metrics_frame, text="mAP50: -")
        self.map50_label.grid(row=0, column=2, sticky=tk.W)
        
        self.map95_label = ttk.Label(metrics_frame, text="mAP95: -")
        self.map95_label.grid(row=0, column=3, sticky=tk.W)
    
    def _create_logs_section(self, parent, row):
        """إنشاء قسم السجلات"""
        frame = ttk.LabelFrame(parent, text="سجلات التدريب", padding="5")
        frame.grid(row=row, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=5)
        frame.columnconfigure(0, weight=1)
        frame.rowconfigure(0, weight=1)
        
        self.log_text = scrolledtext.ScrolledText(frame, height=10, width=80)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # تكوين الشبكة الرئيسية للتوسع
        parent.rowconfigure(row, weight=1)
    
    def _browse_dataset(self):
        """تصفح مجموعة البيانات"""
        directory = filedialog.askdirectory(title="اختر مجلد مجموعة البيانات")
        if directory:
            self.dataset_path.set(directory)
    
    def _start_training(self):
        """بدء التدريب"""
        try:
            # التحقق من المدخلات
            if not self.dataset_path.get():
                messagebox.showerror("خطأ", "يرجى اختيار مجموعة البيانات")
                return
            
            if not Path(self.dataset_path.get()).exists():
                messagebox.showerror("خطأ", "مجموعة البيانات غير موجودة")
                return
            
            # إعداد التكوين
            config = {
                'model_version': self.model_version.get(),
                'epochs': self.epochs.get(),
                'batch_size': self.batch_size.get(),
                'learning_rate': self.learning_rate.get(),
                'device': self.device.get(),
                'project': 'rescue_training_gui',
                'name': f'training_{datetime.now().strftime("%Y%m%d_%H%M%S")}',
                'verbose': True
            }
            
            # إنشاء مدير التدريب
            self.training_manager = YOLOTrainingManager(config)
            
            # بدء التدريب في خيط منفصل
            self.training_thread = threading.Thread(target=self._training_worker, daemon=True)
            self.training_thread.start()
            
            # تحديث الواجهة
            self.start_button.config(state=tk.DISABLED)
            self.stop_button.config(state=tk.NORMAL)
            self.progress_info.set("جاري التحضير للتدريب...")
            
            # بدء مراقبة التقدم
            self.progress_monitor = TrainingProgressMonitor(self.training_manager, self._update_progress)
            self.progress_monitor.start_monitoring()
            
            self._log_message("بدء التدريب...")
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في بدء التدريب: {e}")
            self._log_message(f"خطأ: {e}")
    
    def _training_worker(self):
        """عامل التدريب (يعمل في خيط منفصل)"""
        try:
            # إعداد مجموعة البيانات
            data_yaml_path = self.training_manager.prepare_rescue_dataset(self.dataset_path.get())
            
            # بدء التدريب
            results = self.training_manager.train_model(data_yaml_path)
            
            # تحديث الواجهة عند الانتهاء
            self.root.after(0, self._training_completed, results)
            
        except Exception as e:
            self.root.after(0, self._training_failed, str(e))
    
    def _training_completed(self, results):
        """معالجة اكتمال التدريب"""
        self.start_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)
        self.evaluate_button.config(state=tk.NORMAL)
        self.export_button.config(state=tk.NORMAL)
        
        self.progress_info.set("اكتمل التدريب بنجاح!")
        self.progress_bar['value'] = 100
        
        if self.progress_monitor:
            self.progress_monitor.stop_monitoring()
        
        self._log_message("اكتمل التدريب بنجاح!")
        self._log_message(f"الأوزان المحفوظة: {len(results.get('saved_models', []))}")
        
        messagebox.showinfo("نجح", "اكتمل التدريب بنجاح!")
    
    def _training_failed(self, error_message):
        """معالجة فشل التدريب"""
        self.start_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)
        
        self.progress_info.set("فشل التدريب")
        
        if self.progress_monitor:
            self.progress_monitor.stop_monitoring()
        
        self._log_message(f"فشل التدريب: {error_message}")
        messagebox.showerror("خطأ", f"فشل التدريب: {error_message}")
    
    def _stop_training(self):
        """إيقاف التدريب"""
        if self.progress_monitor:
            self.progress_monitor.stop_monitoring()
        
        self.start_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)
        self.progress_info.set("تم إيقاف التدريب")
        
        self._log_message("تم إيقاف التدريب بواسطة المستخدم")
    
    def _evaluate_model(self):
        """تقييم النموذج"""
        if not self.training_manager:
            messagebox.showerror("خطأ", "لا يوجد نموذج مدرب")
            return
        
        try:
            # البحث عن أفضل نموذج
            weights_dir = self.training_manager.training_paths['training_dir'] / 'weights'
            best_model = weights_dir / 'best.pt'
            
            if not best_model.exists():
                messagebox.showerror("خطأ", "لم يتم العثور على النموذج المدرب")
                return
            
            # إعداد مجموعة البيانات
            data_yaml_path = self.training_manager.prepare_rescue_dataset(self.dataset_path.get())
            
            # تشغيل التقييم
            self._log_message("بدء تقييم النموذج...")
            eval_results = self.training_manager.evaluate_model(str(best_model), data_yaml_path)
            
            # عرض النتائج
            metrics = eval_results.get('metrics', {})
            message = f"""نتائج التقييم:
mAP50: {metrics.get('mAP50', 0):.3f}
mAP50-95: {metrics.get('mAP50-95', 0):.3f}
Precision: {metrics.get('precision', 0):.3f}
Recall: {metrics.get('recall', 0):.3f}"""
            
            messagebox.showinfo("نتائج التقييم", message)
            self._log_message("اكتمل تقييم النموذج")
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تقييم النموذج: {e}")
            self._log_message(f"خطأ في التقييم: {e}")
    
    def _export_model(self):
        """تصدير النموذج"""
        if not self.training_manager:
            messagebox.showerror("خطأ", "لا يوجد نموذج مدرب")
            return
        
        try:
            # البحث عن أفضل نموذج
            weights_dir = self.training_manager.training_paths['training_dir'] / 'weights'
            best_model = weights_dir / 'best.pt'
            
            if not best_model.exists():
                messagebox.showerror("خطأ", "لم يتم العثور على النموذج المدرب")
                return
            
            # تصدير النموذج
            self._log_message("بدء تصدير النموذج...")
            exported_models = self.training_manager.export_model(str(best_model), ['onnx', 'torchscript'])
            
            if exported_models:
                message = "تم تصدير النموذج بالتنسيقات:\n" + "\n".join(f"- {fmt}: {path}" for fmt, path in exported_models.items())
                messagebox.showinfo("نجح التصدير", message)
                self._log_message("اكتمل تصدير النموذج")
            else:
                messagebox.showwarning("تحذير", "لم يتم تصدير أي نماذج")
                
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تصدير النموذج: {e}")
            self._log_message(f"خطأ في التصدير: {e}")
    
    def _update_progress(self, progress_info):
        """تحديث معلومات التقدم"""
        try:
            # تحديث في الخيط الرئيسي
            self.root.after(0, self._update_progress_ui, progress_info)
        except Exception as e:
            self.logger.error(f"خطأ في تحديث التقدم: {e}")
    
    def _update_progress_ui(self, progress_info):
        """تحديث واجهة التقدم"""
        try:
            if 'latest_metrics' in progress_info:
                metrics = progress_info['latest_metrics']
                
                # تحديث شريط التقدم
                total_epochs = self.epochs.get()
                current_epoch = metrics.get('epoch', 0)
                progress_percentage = (current_epoch / total_epochs) * 100
                self.progress_bar['value'] = progress_percentage
                
                # تحديث المعلومات
                self.progress_info.set(f"الحقبة {current_epoch}/{total_epochs}")
                
                # تحديث المقاييس
                self.epoch_label.config(text=f"الحقبة: {current_epoch}")
                self.loss_label.config(text=f"الخسارة: {metrics.get('train_loss', 0):.4f}")
                self.map50_label.config(text=f"mAP50: {metrics.get('val_map50', 0):.3f}")
                self.map95_label.config(text=f"mAP95: {metrics.get('val_map50_95', 0):.3f}")
                
        except Exception as e:
            self.logger.error(f"خطأ في تحديث واجهة التقدم: {e}")
    
    def _log_message(self, message):
        """إضافة رسالة للسجل"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
    
    def run(self):
        """تشغيل الواجهة"""
        self.root.mainloop()


class CommandLineTrainingInterface:
    """
    واجهة سطر الأوامر لتدريب YOLO
    Command line interface for YOLO training
    """
    
    def __init__(self):
        """تهيئة واجهة سطر الأوامر"""
        self.logger = logging.getLogger(__name__)
    
    def run_training(self, config_file: str = None, **kwargs):
        """
        تشغيل التدريب من سطر الأوامر
        
        Args:
            config_file: ملف التكوين
            **kwargs: معاملات إضافية
        """
        try:
            # تحميل التكوين
            if config_file and Path(config_file).exists():
                with open(config_file, 'r', encoding='utf-8') as f:
                    import yaml
                    config = yaml.safe_load(f)
            else:
                config = {}
            
            # دمج المعاملات الإضافية
            config.update(kwargs)
            
            # إنشاء مدير التدريب
            training_manager = YOLOTrainingManager(config)
            
            # إعداد مجموعة البيانات
            dataset_path = config.get('dataset_path', './rescue_dataset')
            data_yaml_path = training_manager.prepare_rescue_dataset(dataset_path)
            
            # بدء التدريب
            self.logger.info("بدء تدريب YOLO...")
            results = training_manager.train_model(data_yaml_path)
            
            # عرض النتائج
            self.logger.info("اكتمل التدريب بنجاح!")
            self.logger.info(f"الأوزان المحفوظة: {len(results.get('saved_models', []))}")
            
            return results
            
        except Exception as e:
            self.logger.error(f"فشل في التدريب: {e}")
            raise
