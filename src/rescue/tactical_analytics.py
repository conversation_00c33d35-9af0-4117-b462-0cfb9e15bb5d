"""
تحليلات مجموعة البيانات التكتيكية للإنقاذ والبحث
Tactical Dataset Analytics for Search and Rescue Operations
=========================================================

نظام تحليلات متقدم مصمم خصيصاً لتحليل مجموعات بيانات عمليات البحث والإنقاذ
وتوليد رؤى تكتيكية لدعم القرار في العمليات الحرجة.
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import logging
from typing import Dict, List, Tuple, Optional, Any
from pathlib import Path
from datetime import datetime
import json
from collections import Counter, defaultdict

try:
    import plotly.express as px
    import plotly.graph_objects as go
    from plotly.subplots import make_subplots
    PLOTLY_AVAILABLE = True
except ImportError:
    PLOTLY_AVAILABLE = False

try:
    from scipy import stats
    from sklearn.metrics import classification_report, confusion_matrix
    SCIPY_SKLEARN_AVAILABLE = True
except ImportError:
    SCIPY_SKLEARN_AVAILABLE = False

from ..utils.exceptions import AnalyticsError


class TacticalRescueAnalytics:
    """
    محلل البيانات التكتيكي المتخصص لعمليات الإنقاذ والبحث
    Tactical data analyst specialized for search and rescue operations
    """
    
    def __init__(self, config: Dict):
        """
        تهيئة محلل البيانات التكتيكي
        
        Args:
            config: إعدادات التكوين
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # إعدادات التحليل
        self.analytics_settings = self._load_analytics_settings()
        
        # إعداد الرسوم البيانية
        self._setup_plotting_style()
        
        # مخزن البيانات المحللة
        self.analysis_cache = {}
        
    def _load_analytics_settings(self) -> Dict:
        """تحميل إعدادات التحليل من التكوين"""
        return {
            # إعدادات الرسوم البيانية
            'figure_size': self.config.get('figure_size', (12, 8)),
            'dpi': self.config.get('dpi', 300),
            'style': self.config.get('plot_style', 'seaborn-v0_8'),
            'color_palette': self.config.get('color_palette', 'viridis'),
            
            # إعدادات التحليل
            'enable_statistical_analysis': self.config.get('enable_statistical_analysis', True),
            'confidence_level': self.config.get('confidence_level', 0.95),
            'enable_heatmaps': self.config.get('enable_heatmaps', True),
            'enable_interactive_plots': self.config.get('enable_interactive_plots', True),
            
            # إعدادات التصدير
            'export_formats': self.config.get('export_formats', ['png', 'pdf', 'html']),
            'save_data_tables': self.config.get('save_data_tables', True),
            
            # إعدادات التحليل التكتيكي
            'analyze_temporal_patterns': self.config.get('analyze_temporal_patterns', True),
            'analyze_spatial_distribution': self.config.get('analyze_spatial_distribution', True),
            'analyze_detection_patterns': self.config.get('analyze_detection_patterns', True)
        }
    
    def _setup_plotting_style(self):
        """إعداد نمط الرسوم البيانية"""
        plt.style.use(self.analytics_settings['style'])
        plt.rcParams['figure.figsize'] = self.analytics_settings['figure_size']
        plt.rcParams['figure.dpi'] = self.analytics_settings['dpi']
        plt.rcParams['font.size'] = 12
        plt.rcParams['axes.titlesize'] = 14
        plt.rcParams['axes.labelsize'] = 12
        plt.rcParams['xtick.labelsize'] = 10
        plt.rcParams['ytick.labelsize'] = 10
        plt.rcParams['legend.fontsize'] = 10
        
        # دعم النصوص العربية
        plt.rcParams['font.family'] = ['DejaVu Sans', 'Arial Unicode MS', 'Tahoma']
    
    def analyze_rescue_dataset(self, dataset_path: str, metadata_file: str = None) -> Dict:
        """
        تحليل شامل لمجموعة بيانات الإنقاذ والبحث
        
        Args:
            dataset_path: مسار مجموعة البيانات
            metadata_file: ملف البيانات الوصفية
            
        Returns:
            نتائج التحليل الشامل
        """
        try:
            self.logger.info("بدء التحليل التكتيكي لمجموعة بيانات الإنقاذ...")
            
            # تحميل البيانات
            df = self._load_dataset_metadata(dataset_path, metadata_file)
            
            # التحليلات الأساسية
            basic_analysis = self._perform_basic_analysis(df)
            
            # تحليل توزيع البيئات
            environment_analysis = self._analyze_environment_distribution(df)
            
            # تحليل كشف الأهداف
            target_detection_analysis = self._analyze_target_detection(df)
            
            # تحليل توازن الفئات
            class_balance_analysis = self._analyze_class_balance(df)
            
            # تحليل جودة البيانات
            quality_analysis = self._analyze_data_quality(df)
            
            # التحليلات المتقدمة
            advanced_analysis = {}
            if self.analytics_settings['analyze_temporal_patterns']:
                advanced_analysis['temporal'] = self._analyze_temporal_patterns(df)
            
            if self.analytics_settings['analyze_spatial_distribution']:
                advanced_analysis['spatial'] = self._analyze_spatial_distribution(df)
            
            if self.analytics_settings['analyze_detection_patterns']:
                advanced_analysis['detection_patterns'] = self._analyze_detection_patterns(df)
            
            # تجميع النتائج
            analysis_results = {
                'basic_statistics': basic_analysis,
                'environment_analysis': environment_analysis,
                'target_detection_analysis': target_detection_analysis,
                'class_balance_analysis': class_balance_analysis,
                'quality_analysis': quality_analysis,
                'advanced_analysis': advanced_analysis,
                'analysis_timestamp': datetime.now().isoformat(),
                'dataset_path': dataset_path
            }
            
            # حفظ النتائج في الذاكرة المؤقتة
            self.analysis_cache[dataset_path] = analysis_results
            
            self.logger.info("اكتمل التحليل التكتيكي بنجاح")
            return analysis_results
            
        except Exception as e:
            self.logger.error(f"فشل في التحليل التكتيكي: {e}")
            raise AnalyticsError(f"فشل في التحليل: {e}")
    
    def _load_dataset_metadata(self, dataset_path: str, metadata_file: str = None) -> pd.DataFrame:
        """تحميل البيانات الوصفية لمجموعة البيانات"""
        dataset_dir = Path(dataset_path)
        
        if metadata_file:
            metadata_path = Path(metadata_file)
        else:
            # البحث عن ملف البيانات الوصفية
            metadata_candidates = [
                dataset_dir / 'rescue_metadata.csv',
                dataset_dir / 'rescue_metadata.json',
                dataset_dir / 'metadata.csv'
            ]
            
            metadata_path = None
            for candidate in metadata_candidates:
                if candidate.exists():
                    metadata_path = candidate
                    break
            
            if not metadata_path:
                raise AnalyticsError("لم يتم العثور على ملف البيانات الوصفية")
        
        # تحميل البيانات
        if metadata_path.suffix.lower() == '.csv':
            df = pd.read_csv(metadata_path)
        elif metadata_path.suffix.lower() == '.json':
            df = pd.read_json(metadata_path)
        else:
            raise AnalyticsError(f"تنسيق ملف غير مدعوم: {metadata_path.suffix}")
        
        self.logger.info(f"تم تحميل {len(df)} سجل من البيانات الوصفية")
        return df
    
    def _perform_basic_analysis(self, df: pd.DataFrame) -> Dict:
        """تحليل الإحصائيات الأساسية"""
        analysis = {
            'total_images': len(df),
            'unique_environments': df['environment_class'].nunique() if 'environment_class' in df.columns else 0,
            'images_with_targets': df['targets_detected'].sum() if 'targets_detected' in df.columns else 0,
            'images_without_targets': len(df) - df['targets_detected'].sum() if 'targets_detected' in df.columns else 0,
            'average_file_size_mb': df['file_size_bytes'].mean() / (1024*1024) if 'file_size_bytes' in df.columns else 0,
            'total_dataset_size_gb': df['file_size_bytes'].sum() / (1024*1024*1024) if 'file_size_bytes' in df.columns else 0
        }
        
        # إحصائيات الأبعاد
        if 'width' in df.columns and 'height' in df.columns:
            analysis.update({
                'average_width': df['width'].mean(),
                'average_height': df['height'].mean(),
                'min_resolution': f"{df['width'].min()}x{df['height'].min()}",
                'max_resolution': f"{df['width'].max()}x{df['height'].max()}"
            })
        
        # إحصائيات المعالجة
        if 'processing_time' in df.columns:
            analysis.update({
                'average_processing_time': df['processing_time'].mean(),
                'total_processing_time': df['processing_time'].sum()
            })
        
        return analysis
    
    def _analyze_environment_distribution(self, df: pd.DataFrame) -> Dict:
        """تحليل توزيع البيئات"""
        if 'environment_class' not in df.columns:
            return {'error': 'عمود تصنيف البيئة غير موجود'}
        
        env_counts = df['environment_class'].value_counts()
        env_percentages = df['environment_class'].value_counts(normalize=True) * 100
        
        # تحليل الثقة
        confidence_stats = {}
        if 'environment_confidence' in df.columns:
            confidence_stats = {
                'mean_confidence': df['environment_confidence'].mean(),
                'std_confidence': df['environment_confidence'].std(),
                'min_confidence': df['environment_confidence'].min(),
                'max_confidence': df['environment_confidence'].max()
            }
            
            # تحليل الثقة حسب البيئة
            confidence_by_env = df.groupby('environment_class')['environment_confidence'].agg(['mean', 'std', 'count'])
            confidence_stats['by_environment'] = confidence_by_env.to_dict('index')
        
        return {
            'distribution_counts': env_counts.to_dict(),
            'distribution_percentages': env_percentages.to_dict(),
            'most_common_environment': env_counts.index[0] if len(env_counts) > 0 else None,
            'least_common_environment': env_counts.index[-1] if len(env_counts) > 0 else None,
            'environment_diversity': len(env_counts),
            'confidence_statistics': confidence_stats
        }
    
    def _analyze_target_detection(self, df: pd.DataFrame) -> Dict:
        """تحليل كشف الأهداف"""
        analysis = {}
        
        # تحليل وجود الأهداف
        if 'targets_detected' in df.columns:
            targets_detected = df['targets_detected'].sum()
            detection_rate = (targets_detected / len(df)) * 100
            
            analysis.update({
                'images_with_targets': int(targets_detected),
                'images_without_targets': len(df) - targets_detected,
                'detection_rate_percentage': detection_rate
            })
        
        # تحليل عدد الكشوفات
        if 'total_detections' in df.columns:
            detection_stats = {
                'total_detections_across_dataset': df['total_detections'].sum(),
                'average_detections_per_image': df['total_detections'].mean(),
                'max_detections_in_single_image': df['total_detections'].max(),
                'std_detections': df['total_detections'].std()
            }
            analysis['detection_statistics'] = detection_stats
        
        # تحليل فئات الأهداف
        if 'unique_rescue_classes' in df.columns:
            all_classes = []
            for classes_list in df['unique_rescue_classes'].dropna():
                if isinstance(classes_list, list):
                    all_classes.extend(classes_list)
                elif isinstance(classes_list, str):
                    # محاولة تحليل النص كقائمة
                    try:
                        import ast
                        parsed_list = ast.literal_eval(classes_list)
                        if isinstance(parsed_list, list):
                            all_classes.extend(parsed_list)
                    except:
                        pass
            
            if all_classes:
                class_counts = Counter(all_classes)
                analysis['rescue_class_distribution'] = dict(class_counts)
                analysis['most_common_rescue_classes'] = dict(class_counts.most_common(5))
                analysis['total_unique_rescue_classes'] = len(class_counts)
        
        # تحليل الثقة في الكشف
        if 'detection_confidence_avg' in df.columns:
            confidence_analysis = {
                'mean_detection_confidence': df['detection_confidence_avg'].mean(),
                'std_detection_confidence': df['detection_confidence_avg'].std(),
                'high_confidence_detections': (df['detection_confidence_avg'] > 0.8).sum(),
                'low_confidence_detections': (df['detection_confidence_avg'] < 0.5).sum()
            }
            analysis['detection_confidence_analysis'] = confidence_analysis
        
        return analysis
    
    def _analyze_class_balance(self, df: pd.DataFrame) -> Dict:
        """تحليل توازن الفئات"""
        balance_analysis = {}
        
        # توازن البيئات
        if 'environment_class' in df.columns:
            env_counts = df['environment_class'].value_counts()
            env_balance = self._calculate_balance_metrics(env_counts)
            balance_analysis['environment_balance'] = env_balance
        
        # توازن كشف الأهداف
        if 'rescue_classification' in df.columns:
            rescue_counts = df['rescue_classification'].value_counts()
            rescue_balance = self._calculate_balance_metrics(rescue_counts)
            balance_analysis['rescue_target_balance'] = rescue_balance
        
        # تحليل التوازن المركب (بيئة × أهداف)
        if 'environment_class' in df.columns and 'rescue_classification' in df.columns:
            cross_tab = pd.crosstab(df['environment_class'], df['rescue_classification'])
            balance_analysis['cross_category_distribution'] = cross_tab.to_dict()
            
            # حساب معامل التوازن المركب
            total_combinations = cross_tab.size
            non_zero_combinations = (cross_tab > 0).sum().sum()
            balance_analysis['cross_category_coverage'] = (non_zero_combinations / total_combinations) * 100
        
        return balance_analysis
    
    def _calculate_balance_metrics(self, counts: pd.Series) -> Dict:
        """حساب مقاييس التوازن للفئات"""
        if len(counts) == 0:
            return {}
        
        # نسبة أكبر فئة إلى أصغر فئة
        imbalance_ratio = counts.max() / counts.min() if counts.min() > 0 else float('inf')
        
        # معامل جيني للتوزيع
        n = len(counts)
        sorted_counts = np.sort(counts.values)
        cumsum = np.cumsum(sorted_counts)
        gini = (n + 1 - 2 * np.sum(cumsum) / cumsum[-1]) / n if cumsum[-1] > 0 else 0
        
        # الانتروبيا
        probabilities = counts / counts.sum()
        entropy = -np.sum(probabilities * np.log2(probabilities + 1e-10))
        max_entropy = np.log2(len(counts))
        normalized_entropy = entropy / max_entropy if max_entropy > 0 else 0
        
        return {
            'imbalance_ratio': imbalance_ratio,
            'gini_coefficient': gini,
            'entropy': entropy,
            'normalized_entropy': normalized_entropy,
            'balance_score': normalized_entropy,  # كلما اقترب من 1 كان أكثر توازناً
            'most_frequent_class': counts.index[0],
            'least_frequent_class': counts.index[-1],
            'class_counts': counts.to_dict()
        }
    
    def _analyze_data_quality(self, df: pd.DataFrame) -> Dict:
        """تحليل جودة البيانات"""
        quality_metrics = {
            'total_records': len(df),
            'missing_data_analysis': {},
            'data_type_analysis': {},
            'outlier_analysis': {},
            'consistency_analysis': {}
        }
        
        # تحليل البيانات المفقودة
        missing_data = df.isnull().sum()
        quality_metrics['missing_data_analysis'] = {
            'columns_with_missing_data': missing_data[missing_data > 0].to_dict(),
            'total_missing_values': missing_data.sum(),
            'missing_data_percentage': (missing_data.sum() / (len(df) * len(df.columns))) * 100
        }
        
        # تحليل أنواع البيانات
        quality_metrics['data_type_analysis'] = df.dtypes.astype(str).to_dict()
        
        # تحليل القيم الشاذة للأعمدة الرقمية
        numeric_columns = df.select_dtypes(include=[np.number]).columns
        outlier_analysis = {}
        
        for col in numeric_columns:
            if col in df.columns and not df[col].empty:
                Q1 = df[col].quantile(0.25)
                Q3 = df[col].quantile(0.75)
                IQR = Q3 - Q1
                lower_bound = Q1 - 1.5 * IQR
                upper_bound = Q3 + 1.5 * IQR
                
                outliers = df[(df[col] < lower_bound) | (df[col] > upper_bound)]
                outlier_analysis[col] = {
                    'outlier_count': len(outliers),
                    'outlier_percentage': (len(outliers) / len(df)) * 100,
                    'lower_bound': lower_bound,
                    'upper_bound': upper_bound
                }
        
        quality_metrics['outlier_analysis'] = outlier_analysis
        
        # تحليل الاتساق
        consistency_issues = []
        
        # فحص اتساق الأبعاد
        if 'width' in df.columns and 'height' in df.columns and 'total_pixels' in df.columns:
            calculated_pixels = df['width'] * df['height']
            pixel_mismatch = abs(calculated_pixels - df['total_pixels']) > 1
            if pixel_mismatch.any():
                consistency_issues.append({
                    'issue': 'pixel_calculation_mismatch',
                    'affected_records': pixel_mismatch.sum(),
                    'description': 'عدم تطابق حساب إجمالي البكسلات'
                })
        
        # فحص اتساق التصنيف
        if 'targets_detected' in df.columns and 'total_detections' in df.columns:
            inconsistent = (df['targets_detected'] == True) & (df['total_detections'] == 0)
            if inconsistent.any():
                consistency_issues.append({
                    'issue': 'detection_flag_mismatch',
                    'affected_records': inconsistent.sum(),
                    'description': 'عدم تطابق علامة الكشف مع عدد الكشوفات'
                })
        
        quality_metrics['consistency_analysis'] = {
            'issues_found': len(consistency_issues),
            'consistency_issues': consistency_issues
        }
        
        # حساب نقاط الجودة الإجمالية
        total_possible_score = 100
        missing_penalty = (missing_data.sum() / (len(df) * len(df.columns))) * 30
        outlier_penalty = sum(oa.get('outlier_percentage', 0) for oa in outlier_analysis.values()) / len(outlier_analysis) * 0.2 if outlier_analysis else 0
        consistency_penalty = len(consistency_issues) * 5
        
        quality_score = max(0, total_possible_score - missing_penalty - outlier_penalty - consistency_penalty)
        quality_metrics['overall_quality_score'] = quality_score
        
        return quality_metrics

    def _analyze_temporal_patterns(self, df: pd.DataFrame) -> Dict:
        """تحليل الأنماط الزمنية"""
        temporal_analysis = {}

        # تحليل أوقات المسح
        if 'scan_timestamp' in df.columns:
            df['scan_datetime'] = pd.to_datetime(df['scan_timestamp'], errors='coerce')

            if not df['scan_datetime'].isna().all():
                temporal_analysis['scan_patterns'] = {
                    'earliest_scan': df['scan_datetime'].min().isoformat(),
                    'latest_scan': df['scan_datetime'].max().isoformat(),
                    'scan_duration_days': (df['scan_datetime'].max() - df['scan_datetime'].min()).days,
                    'scans_per_day': df.groupby(df['scan_datetime'].dt.date).size().to_dict()
                }

        # تحليل أوقات التصنيف
        if 'classification_timestamp' in df.columns:
            df['classification_datetime'] = pd.to_datetime(df['classification_timestamp'], errors='coerce')

            if not df['classification_datetime'].isna().all():
                temporal_analysis['classification_patterns'] = {
                    'classification_timespan': (df['classification_datetime'].max() - df['classification_datetime'].min()).total_seconds() / 3600,  # ساعات
                    'classifications_per_hour': df.groupby(df['classification_datetime'].dt.hour).size().to_dict()
                }

        return temporal_analysis

    def _analyze_spatial_distribution(self, df: pd.DataFrame) -> Dict:
        """تحليل التوزيع المكاني"""
        spatial_analysis = {}

        # تحليل البيانات الجغرافية
        if 'has_geospatial_data' in df.columns:
            geo_images = df[df['has_geospatial_data'] == True]
            spatial_analysis['geospatial_coverage'] = {
                'images_with_geospatial_data': len(geo_images),
                'geospatial_percentage': (len(geo_images) / len(df)) * 100
            }

            # تحليل الحدود الجغرافية
            if 'bounds' in df.columns and len(geo_images) > 0:
                bounds_data = []
                for bounds_str in geo_images['bounds'].dropna():
                    try:
                        if isinstance(bounds_str, str):
                            import ast
                            bounds = ast.literal_eval(bounds_str)
                        else:
                            bounds = bounds_str

                        if isinstance(bounds, list) and len(bounds) == 4:
                            bounds_data.append(bounds)
                    except:
                        continue

                if bounds_data:
                    bounds_df = pd.DataFrame(bounds_data, columns=['min_x', 'min_y', 'max_x', 'max_y'])
                    spatial_analysis['geographic_extent'] = {
                        'overall_bounds': [
                            bounds_df['min_x'].min(),
                            bounds_df['min_y'].min(),
                            bounds_df['max_x'].max(),
                            bounds_df['max_y'].max()
                        ],
                        'coverage_area_sq_degrees': (bounds_df['max_x'].max() - bounds_df['min_x'].min()) *
                                                   (bounds_df['max_y'].max() - bounds_df['min_y'].min())
                    }

        return spatial_analysis

    def _analyze_detection_patterns(self, df: pd.DataFrame) -> Dict:
        """تحليل أنماط الكشف"""
        detection_patterns = {}

        # تحليل أنماط الكشف حسب البيئة
        if 'environment_class' in df.columns and 'targets_detected' in df.columns:
            env_detection = df.groupby('environment_class')['targets_detected'].agg(['sum', 'count', 'mean'])
            detection_patterns['detection_by_environment'] = {
                'detection_rates': (env_detection['mean'] * 100).to_dict(),
                'total_detections': env_detection['sum'].to_dict(),
                'sample_sizes': env_detection['count'].to_dict()
            }

        # تحليل أنماط الثقة
        if 'environment_confidence' in df.columns and 'detection_confidence_avg' in df.columns:
            # تحليل العلاقة بين ثقة البيئة وثقة الكشف
            correlation = df[['environment_confidence', 'detection_confidence_avg']].corr().iloc[0, 1]
            detection_patterns['confidence_correlation'] = {
                'environment_detection_correlation': correlation,
                'interpretation': 'قوية' if abs(correlation) > 0.7 else 'متوسطة' if abs(correlation) > 0.3 else 'ضعيفة'
            }

        # تحليل أنماط الحاجة للتحقق البشري
        if 'needs_human_verification' in df.columns:
            verification_analysis = {
                'total_needing_verification': df['needs_human_verification'].sum(),
                'verification_rate': (df['needs_human_verification'].sum() / len(df)) * 100
            }

            # تحليل الحاجة للتحقق حسب البيئة
            if 'environment_class' in df.columns:
                verification_by_env = df.groupby('environment_class')['needs_human_verification'].agg(['sum', 'count', 'mean'])
                verification_analysis['verification_by_environment'] = (verification_by_env['mean'] * 100).to_dict()

            detection_patterns['human_verification_analysis'] = verification_analysis

        return detection_patterns

    def generate_tactical_visualizations(self, analysis_results: Dict, output_dir: str) -> Dict:
        """
        توليد الرسوم البيانية التكتيكية

        Args:
            analysis_results: نتائج التحليل
            output_dir: دليل الإخراج

        Returns:
            معلومات الرسوم البيانية المولدة
        """
        try:
            self.logger.info("توليد الرسوم البيانية التكتيكية...")

            output_path = Path(output_dir)
            output_path.mkdir(parents=True, exist_ok=True)

            generated_plots = {}

            # رسم توزيع البيئات
            if 'environment_analysis' in analysis_results:
                env_plot = self._create_environment_distribution_plot(
                    analysis_results['environment_analysis'], output_path
                )
                generated_plots['environment_distribution'] = env_plot

            # رسم تحليل كشف الأهداف
            if 'target_detection_analysis' in analysis_results:
                detection_plot = self._create_target_detection_plot(
                    analysis_results['target_detection_analysis'], output_path
                )
                generated_plots['target_detection'] = detection_plot

            # رسم تحليل توازن الفئات
            if 'class_balance_analysis' in analysis_results:
                balance_plot = self._create_class_balance_plot(
                    analysis_results['class_balance_analysis'], output_path
                )
                generated_plots['class_balance'] = balance_plot

            # رسم تحليل الجودة
            if 'quality_analysis' in analysis_results:
                quality_plot = self._create_quality_analysis_plot(
                    analysis_results['quality_analysis'], output_path
                )
                generated_plots['quality_analysis'] = quality_plot

            # الخرائط الحرارية للكشف
            if self.analytics_settings['enable_heatmaps']:
                heatmap_plot = self._create_detection_heatmap(analysis_results, output_path)
                if heatmap_plot:
                    generated_plots['detection_heatmap'] = heatmap_plot

            # الرسوم التفاعلية
            if self.analytics_settings['enable_interactive_plots'] and PLOTLY_AVAILABLE:
                interactive_plots = self._create_interactive_plots(analysis_results, output_path)
                generated_plots.update(interactive_plots)

            self.logger.info(f"تم توليد {len(generated_plots)} رسم بياني")
            return generated_plots

        except Exception as e:
            self.logger.error(f"فشل في توليد الرسوم البيانية: {e}")
            raise AnalyticsError(f"فشل في توليد الرسوم البيانية: {e}")

    def _create_environment_distribution_plot(self, env_analysis: Dict, output_path: Path) -> str:
        """إنشاء رسم توزيع البيئات"""
        if 'distribution_counts' not in env_analysis:
            return None

        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

        # رسم دائري
        counts = env_analysis['distribution_counts']
        ax1.pie(counts.values(), labels=counts.keys(), autopct='%1.1f%%', startangle=90)
        ax1.set_title('توزيع البيئات - رسم دائري')

        # رسم أعمدة
        ax2.bar(counts.keys(), counts.values(), color=plt.cm.viridis(np.linspace(0, 1, len(counts))))
        ax2.set_title('توزيع البيئات - رسم أعمدة')
        ax2.set_xlabel('نوع البيئة')
        ax2.set_ylabel('عدد الصور')
        ax2.tick_params(axis='x', rotation=45)

        plt.tight_layout()

        plot_path = output_path / 'environment_distribution.png'
        plt.savefig(plot_path, dpi=self.analytics_settings['dpi'], bbox_inches='tight')
        plt.close()

        return str(plot_path)

    def _create_target_detection_plot(self, detection_analysis: Dict, output_path: Path) -> str:
        """إنشاء رسم تحليل كشف الأهداف"""
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))

        # رسم معدل الكشف
        if 'images_with_targets' in detection_analysis and 'images_without_targets' in detection_analysis:
            labels = ['تحتوي على أهداف', 'خالية من الأهداف']
            sizes = [detection_analysis['images_with_targets'], detection_analysis['images_without_targets']]

            axes[0, 0].pie(sizes, labels=labels, autopct='%1.1f%%', startangle=90)
            axes[0, 0].set_title('توزيع كشف الأهداف')

        # رسم توزيع فئات الأهداف
        if 'rescue_class_distribution' in detection_analysis:
            class_dist = detection_analysis['rescue_class_distribution']
            axes[0, 1].bar(class_dist.keys(), class_dist.values())
            axes[0, 1].set_title('توزيع فئات أهداف الإنقاذ')
            axes[0, 1].tick_params(axis='x', rotation=45)

        # رسم إحصائيات الكشف
        if 'detection_statistics' in detection_analysis:
            stats = detection_analysis['detection_statistics']
            stat_names = ['المتوسط', 'الحد الأقصى', 'الانحراف المعياري']
            stat_values = [
                stats.get('average_detections_per_image', 0),
                stats.get('max_detections_in_single_image', 0),
                stats.get('std_detections', 0)
            ]

            axes[1, 0].bar(stat_names, stat_values)
            axes[1, 0].set_title('إحصائيات الكشف')
            axes[1, 0].tick_params(axis='x', rotation=45)

        # رسم تحليل الثقة
        if 'detection_confidence_analysis' in detection_analysis:
            conf_analysis = detection_analysis['detection_confidence_analysis']
            conf_categories = ['عالية الثقة', 'منخفضة الثقة']
            conf_values = [
                conf_analysis.get('high_confidence_detections', 0),
                conf_analysis.get('low_confidence_detections', 0)
            ]

            axes[1, 1].bar(conf_categories, conf_values, color=['green', 'red'])
            axes[1, 1].set_title('تحليل ثقة الكشف')

        plt.tight_layout()

        plot_path = output_path / 'target_detection_analysis.png'
        plt.savefig(plot_path, dpi=self.analytics_settings['dpi'], bbox_inches='tight')
        plt.close()

        return str(plot_path)

    def _create_class_balance_plot(self, balance_analysis: Dict, output_path: Path) -> str:
        """إنشاء رسم تحليل توازن الفئات"""
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))

        # توازن البيئات
        if 'environment_balance' in balance_analysis:
            env_balance = balance_analysis['environment_balance']
            if 'class_counts' in env_balance:
                counts = env_balance['class_counts']
                axes[0, 0].bar(counts.keys(), counts.values())
                axes[0, 0].set_title(f'توازن البيئات (نقاط التوازن: {env_balance.get("balance_score", 0):.2f})')
                axes[0, 0].tick_params(axis='x', rotation=45)

        # توازن أهداف الإنقاذ
        if 'rescue_target_balance' in balance_analysis:
            rescue_balance = balance_analysis['rescue_target_balance']
            if 'class_counts' in rescue_balance:
                counts = rescue_balance['class_counts']
                axes[0, 1].bar(counts.keys(), counts.values())
                axes[0, 1].set_title(f'توازن أهداف الإنقاذ (نقاط التوازن: {rescue_balance.get("balance_score", 0):.2f})')
                axes[0, 1].tick_params(axis='x', rotation=45)

        # التوزيع المركب
        if 'cross_category_distribution' in balance_analysis:
            cross_dist = balance_analysis['cross_category_distribution']
            if cross_dist:
                # تحويل إلى DataFrame للرسم
                cross_df = pd.DataFrame(cross_dist).fillna(0)

                im = axes[1, 0].imshow(cross_df.values, cmap='viridis', aspect='auto')
                axes[1, 0].set_xticks(range(len(cross_df.columns)))
                axes[1, 0].set_yticks(range(len(cross_df.index)))
                axes[1, 0].set_xticklabels(cross_df.columns, rotation=45)
                axes[1, 0].set_yticklabels(cross_df.index)
                axes[1, 0].set_title('التوزيع المركب (بيئة × أهداف)')

                # إضافة شريط الألوان
                plt.colorbar(im, ax=axes[1, 0])

        # مقاييس التوازن
        balance_metrics = []
        metric_names = []

        if 'environment_balance' in balance_analysis:
            env_balance = balance_analysis['environment_balance']
            balance_metrics.append(env_balance.get('balance_score', 0))
            metric_names.append('البيئات')

        if 'rescue_target_balance' in balance_analysis:
            rescue_balance = balance_analysis['rescue_target_balance']
            balance_metrics.append(rescue_balance.get('balance_score', 0))
            metric_names.append('الأهداف')

        if balance_metrics:
            axes[1, 1].bar(metric_names, balance_metrics, color=['blue', 'orange'])
            axes[1, 1].set_title('مقاييس التوازن العامة')
            axes[1, 1].set_ylabel('نقاط التوازن (0-1)')
            axes[1, 1].set_ylim(0, 1)

        plt.tight_layout()

        plot_path = output_path / 'class_balance_analysis.png'
        plt.savefig(plot_path, dpi=self.analytics_settings['dpi'], bbox_inches='tight')
        plt.close()

        return str(plot_path)

    def _create_quality_analysis_plot(self, quality_analysis: Dict, output_path: Path) -> str:
        """إنشاء رسم تحليل الجودة"""
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))

        # نقاط الجودة الإجمالية
        overall_score = quality_analysis.get('overall_quality_score', 0)
        axes[0, 0].bar(['نقاط الجودة'], [overall_score], color='green' if overall_score > 80 else 'orange' if overall_score > 60 else 'red')
        axes[0, 0].set_title(f'نقاط الجودة الإجمالية: {overall_score:.1f}/100')
        axes[0, 0].set_ylim(0, 100)

        # تحليل البيانات المفقودة
        if 'missing_data_analysis' in quality_analysis:
            missing_data = quality_analysis['missing_data_analysis']
            if 'columns_with_missing_data' in missing_data and missing_data['columns_with_missing_data']:
                missing_cols = missing_data['columns_with_missing_data']
                axes[0, 1].bar(missing_cols.keys(), missing_cols.values())
                axes[0, 1].set_title('البيانات المفقودة حسب العمود')
                axes[0, 1].tick_params(axis='x', rotation=45)

        # تحليل القيم الشاذة
        if 'outlier_analysis' in quality_analysis:
            outlier_data = quality_analysis['outlier_analysis']
            if outlier_data:
                outlier_percentages = [data.get('outlier_percentage', 0) for data in outlier_data.values()]
                column_names = list(outlier_data.keys())

                axes[1, 0].bar(column_names, outlier_percentages)
                axes[1, 0].set_title('نسبة القيم الشاذة حسب العمود')
                axes[1, 0].set_ylabel('النسبة المئوية')
                axes[1, 0].tick_params(axis='x', rotation=45)

        # تحليل الاتساق
        if 'consistency_analysis' in quality_analysis:
            consistency_data = quality_analysis['consistency_analysis']
            issues_count = consistency_data.get('issues_found', 0)

            axes[1, 1].bar(['مشاكل الاتساق'], [issues_count], color='red' if issues_count > 0 else 'green')
            axes[1, 1].set_title(f'مشاكل الاتساق: {issues_count}')

        plt.tight_layout()

        plot_path = output_path / 'quality_analysis.png'
        plt.savefig(plot_path, dpi=self.analytics_settings['dpi'], bbox_inches='tight')
        plt.close()

        return str(plot_path)

    def _create_detection_heatmap(self, analysis_results: Dict, output_path: Path) -> Optional[str]:
        """إنشاء خريطة حرارية للكشف"""
        if 'class_balance_analysis' not in analysis_results:
            return None

        balance_analysis = analysis_results['class_balance_analysis']
        if 'cross_category_distribution' not in balance_analysis:
            return None

        cross_dist = balance_analysis['cross_category_distribution']
        if not cross_dist:
            return None

        # تحويل إلى DataFrame
        cross_df = pd.DataFrame(cross_dist).fillna(0)

        plt.figure(figsize=(12, 8))
        sns.heatmap(cross_df, annot=True, cmap='YlOrRd', fmt='g', cbar_kws={'label': 'عدد الصور'})
        plt.title('خريطة حرارية للكشف: البيئة × أهداف الإنقاذ')
        plt.xlabel('تصنيف أهداف الإنقاذ')
        plt.ylabel('نوع البيئة')
        plt.xticks(rotation=45)
        plt.yticks(rotation=0)

        plot_path = output_path / 'detection_heatmap.png'
        plt.savefig(plot_path, dpi=self.analytics_settings['dpi'], bbox_inches='tight')
        plt.close()

        return str(plot_path)

    def _create_interactive_plots(self, analysis_results: Dict, output_path: Path) -> Dict:
        """إنشاء الرسوم التفاعلية باستخدام Plotly"""
        if not PLOTLY_AVAILABLE:
            return {}

        interactive_plots = {}

        # رسم تفاعلي لتوزيع البيئات
        if 'environment_analysis' in analysis_results:
            env_analysis = analysis_results['environment_analysis']
            if 'distribution_counts' in env_analysis:
                counts = env_analysis['distribution_counts']

                fig = px.pie(
                    values=list(counts.values()),
                    names=list(counts.keys()),
                    title='توزيع البيئات - رسم تفاعلي'
                )

                plot_path = output_path / 'interactive_environment_distribution.html'
                fig.write_html(plot_path)
                interactive_plots['interactive_environment'] = str(plot_path)

        # رسم تفاعلي لكشف الأهداف
        if 'target_detection_analysis' in analysis_results:
            detection_analysis = analysis_results['target_detection_analysis']
            if 'rescue_class_distribution' in detection_analysis:
                class_dist = detection_analysis['rescue_class_distribution']

                fig = px.bar(
                    x=list(class_dist.keys()),
                    y=list(class_dist.values()),
                    title='توزيع فئات أهداف الإنقاذ - رسم تفاعلي',
                    labels={'x': 'فئة الهدف', 'y': 'عدد الكشوفات'}
                )

                plot_path = output_path / 'interactive_target_detection.html'
                fig.write_html(plot_path)
                interactive_plots['interactive_detection'] = str(plot_path)

        return interactive_plots

    def export_tactical_report(self, analysis_results: Dict, output_path: str) -> str:
        """
        تصدير تقرير تكتيكي شامل

        Args:
            analysis_results: نتائج التحليل
            output_path: مسار الإخراج

        Returns:
            مسار التقرير المصدر
        """
        report_path = Path(output_path) / f"tactical_rescue_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"

        # إضافة معلومات إضافية للتقرير
        enhanced_report = {
            'report_metadata': {
                'report_type': 'Tactical Rescue Analytics Report',
                'generation_timestamp': datetime.now().isoformat(),
                'analysis_version': '1.0.0',
                'report_language': 'Arabic/English'
            },
            'executive_summary': self._generate_executive_summary(analysis_results),
            'detailed_analysis': analysis_results,
            'recommendations': self._generate_tactical_recommendations(analysis_results),
            'data_quality_assessment': self._assess_data_readiness(analysis_results)
        }

        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(enhanced_report, f, ensure_ascii=False, indent=2, default=str)

        self.logger.info(f"تم تصدير التقرير التكتيكي إلى: {report_path}")
        return str(report_path)

    def _generate_executive_summary(self, analysis_results: Dict) -> Dict:
        """توليد ملخص تنفيذي للتحليل"""
        summary = {
            'dataset_overview': {},
            'key_findings': [],
            'critical_insights': [],
            'operational_readiness': 'غير محدد'
        }

        # نظرة عامة على مجموعة البيانات
        if 'basic_statistics' in analysis_results:
            basic_stats = analysis_results['basic_statistics']
            summary['dataset_overview'] = {
                'total_images': basic_stats.get('total_images', 0),
                'images_with_targets': basic_stats.get('images_with_targets', 0),
                'target_detection_rate': (basic_stats.get('images_with_targets', 0) / basic_stats.get('total_images', 1)) * 100,
                'environment_diversity': basic_stats.get('unique_environments', 0),
                'dataset_size_gb': basic_stats.get('total_dataset_size_gb', 0)
            }

        # النتائج الرئيسية
        key_findings = []

        # تحليل توزيع البيئات
        if 'environment_analysis' in analysis_results:
            env_analysis = analysis_results['environment_analysis']
            most_common = env_analysis.get('most_common_environment', 'غير محدد')
            key_findings.append(f"البيئة الأكثر شيوعاً: {most_common}")

            if 'confidence_statistics' in env_analysis:
                avg_confidence = env_analysis['confidence_statistics'].get('mean_confidence', 0)
                key_findings.append(f"متوسط ثقة تصنيف البيئة: {avg_confidence:.2f}")

        # تحليل كشف الأهداف
        if 'target_detection_analysis' in analysis_results:
            detection_analysis = analysis_results['target_detection_analysis']
            detection_rate = detection_analysis.get('detection_rate_percentage', 0)
            key_findings.append(f"معدل كشف الأهداف: {detection_rate:.1f}%")

            if 'most_common_rescue_classes' in detection_analysis:
                most_common_targets = list(detection_analysis['most_common_rescue_classes'].keys())[:3]
                key_findings.append(f"أكثر أهداف الإنقاذ شيوعاً: {', '.join(most_common_targets)}")

        summary['key_findings'] = key_findings

        # الرؤى الحرجة
        critical_insights = []

        # تحليل توازن الفئات
        if 'class_balance_analysis' in analysis_results:
            balance_analysis = analysis_results['class_balance_analysis']

            if 'environment_balance' in balance_analysis:
                env_balance_score = balance_analysis['environment_balance'].get('balance_score', 0)
                if env_balance_score < 0.5:
                    critical_insights.append("عدم توازن كبير في توزيع البيئات - قد يؤثر على أداء النموذج")

            if 'rescue_target_balance' in balance_analysis:
                target_balance_score = balance_analysis['rescue_target_balance'].get('balance_score', 0)
                if target_balance_score < 0.5:
                    critical_insights.append("عدم توازن في توزيع أهداف الإنقاذ - يحتاج لمعالجة")

        # تحليل الجودة
        if 'quality_analysis' in analysis_results:
            quality_score = analysis_results['quality_analysis'].get('overall_quality_score', 0)
            if quality_score < 70:
                critical_insights.append(f"جودة البيانات منخفضة ({quality_score:.1f}/100) - تحتاج لتحسين")

            missing_percentage = analysis_results['quality_analysis'].get('missing_data_analysis', {}).get('missing_data_percentage', 0)
            if missing_percentage > 10:
                critical_insights.append(f"نسبة عالية من البيانات المفقودة ({missing_percentage:.1f}%)")

        summary['critical_insights'] = critical_insights

        # تقييم الجاهزية التشغيلية
        readiness_score = 0

        if 'quality_analysis' in analysis_results:
            quality_score = analysis_results['quality_analysis'].get('overall_quality_score', 0)
            readiness_score += (quality_score / 100) * 40  # 40% من النقاط للجودة

        if 'class_balance_analysis' in analysis_results:
            balance_analysis = analysis_results['class_balance_analysis']
            env_balance = balance_analysis.get('environment_balance', {}).get('balance_score', 0)
            target_balance = balance_analysis.get('rescue_target_balance', {}).get('balance_score', 0)
            avg_balance = (env_balance + target_balance) / 2
            readiness_score += avg_balance * 30  # 30% للتوازن

        if 'basic_statistics' in analysis_results:
            total_images = analysis_results['basic_statistics'].get('total_images', 0)
            if total_images > 1000:
                readiness_score += 30  # 30% لحجم مجموعة البيانات
            elif total_images > 500:
                readiness_score += 20
            elif total_images > 100:
                readiness_score += 10

        if readiness_score >= 80:
            summary['operational_readiness'] = 'جاهز للتشغيل'
        elif readiness_score >= 60:
            summary['operational_readiness'] = 'جاهز مع تحفظات'
        elif readiness_score >= 40:
            summary['operational_readiness'] = 'يحتاج تحسينات'
        else:
            summary['operational_readiness'] = 'غير جاهز للتشغيل'

        summary['readiness_score'] = readiness_score

        return summary

    def _generate_tactical_recommendations(self, analysis_results: Dict) -> List[Dict]:
        """توليد توصيات تكتيكية"""
        recommendations = []

        # توصيات بناءً على توازن الفئات
        if 'class_balance_analysis' in analysis_results:
            balance_analysis = analysis_results['class_balance_analysis']

            if 'environment_balance' in balance_analysis:
                env_balance_score = balance_analysis['environment_balance'].get('balance_score', 0)
                if env_balance_score < 0.5:
                    recommendations.append({
                        'category': 'data_collection',
                        'priority': 'high',
                        'title': 'تحسين توازن البيئات',
                        'description': 'جمع المزيد من الصور للبيئات الأقل تمثيلاً لتحسين توازن مجموعة البيانات',
                        'action_items': [
                            'تحديد البيئات الأقل تمثيلاً',
                            'تخطيط مهام جمع بيانات مستهدفة',
                            'استخدام تقنيات تعزيز البيانات للفئات النادرة'
                        ]
                    })

        # توصيات بناءً على جودة البيانات
        if 'quality_analysis' in analysis_results:
            quality_score = analysis_results['quality_analysis'].get('overall_quality_score', 0)
            if quality_score < 70:
                recommendations.append({
                    'category': 'data_quality',
                    'priority': 'high',
                    'title': 'تحسين جودة البيانات',
                    'description': 'معالجة مشاكل جودة البيانات المكتشفة لضمان أداء أفضل للنماذج',
                    'action_items': [
                        'مراجعة وتنظيف البيانات المفقودة',
                        'معالجة القيم الشاذة',
                        'تحسين عملية جمع البيانات'
                    ]
                })

        # توصيات بناءً على كشف الأهداف
        if 'target_detection_analysis' in analysis_results:
            detection_analysis = analysis_results['target_detection_analysis']
            detection_rate = detection_analysis.get('detection_rate_percentage', 0)

            if detection_rate < 30:
                recommendations.append({
                    'category': 'data_collection',
                    'priority': 'critical',
                    'title': 'زيادة صور الأهداف',
                    'description': 'معدل كشف الأهداف منخفض جداً، يحتاج لجمع المزيد من الصور التي تحتوي على أهداف إنقاذ',
                    'action_items': [
                        'تركيز جهود الجمع على المناطق عالية الاحتمالية',
                        'استخدام محاكيات التدريب',
                        'التعاون مع فرق الإنقاذ لجمع صور حقيقية'
                    ]
                })

        # توصيات تقنية
        recommendations.append({
            'category': 'technical',
            'priority': 'medium',
            'title': 'تحسين النماذج',
            'description': 'تطبيق تقنيات متقدمة لتحسين أداء النماذج',
            'action_items': [
                'استخدام تقنيات Transfer Learning',
                'تطبيق Data Augmentation المتخصص',
                'ضبط معاملات النماذج (Hyperparameter Tuning)',
                'استخدام Ensemble Methods'
            ]
        })

        return recommendations

    def _assess_data_readiness(self, analysis_results: Dict) -> Dict:
        """تقييم جاهزية البيانات للتدريب"""
        assessment = {
            'overall_readiness': 'غير محدد',
            'readiness_score': 0,
            'readiness_factors': {},
            'blocking_issues': [],
            'improvement_areas': []
        }

        factors = {}

        # عامل حجم البيانات
        if 'basic_statistics' in analysis_results:
            total_images = analysis_results['basic_statistics'].get('total_images', 0)
            if total_images >= 1000:
                factors['data_size'] = {'score': 100, 'status': 'ممتاز'}
            elif total_images >= 500:
                factors['data_size'] = {'score': 80, 'status': 'جيد'}
            elif total_images >= 100:
                factors['data_size'] = {'score': 60, 'status': 'مقبول'}
            else:
                factors['data_size'] = {'score': 30, 'status': 'غير كافي'}
                assessment['blocking_issues'].append('حجم مجموعة البيانات صغير جداً')

        # عامل جودة البيانات
        if 'quality_analysis' in analysis_results:
            quality_score = analysis_results['quality_analysis'].get('overall_quality_score', 0)
            factors['data_quality'] = {'score': quality_score, 'status': 'ممتاز' if quality_score > 80 else 'جيد' if quality_score > 60 else 'يحتاج تحسين'}

            if quality_score < 50:
                assessment['blocking_issues'].append('جودة البيانات منخفضة جداً')

        # عامل توازن الفئات
        if 'class_balance_analysis' in analysis_results:
            balance_analysis = analysis_results['class_balance_analysis']
            env_balance = balance_analysis.get('environment_balance', {}).get('balance_score', 0) * 100
            target_balance = balance_analysis.get('rescue_target_balance', {}).get('balance_score', 0) * 100
            avg_balance = (env_balance + target_balance) / 2

            factors['class_balance'] = {'score': avg_balance, 'status': 'متوازن' if avg_balance > 70 else 'متوسط' if avg_balance > 50 else 'غير متوازن'}

            if avg_balance < 30:
                assessment['blocking_issues'].append('عدم توازن شديد في الفئات')

        # حساب النقاط الإجمالية
        if factors:
            total_score = sum(factor['score'] for factor in factors.values()) / len(factors)
            assessment['readiness_score'] = total_score
            assessment['readiness_factors'] = factors

            if total_score >= 80:
                assessment['overall_readiness'] = 'جاهز للتدريب'
            elif total_score >= 60:
                assessment['overall_readiness'] = 'جاهز مع تحسينات بسيطة'
            elif total_score >= 40:
                assessment['overall_readiness'] = 'يحتاج تحسينات كبيرة'
            else:
                assessment['overall_readiness'] = 'غير جاهز للتدريب'

        return assessment

    def generate_comprehensive_tactical_report(self, dataset_path: str, output_dir: str, metadata_file: str = None) -> Dict:
        """
        توليد تقرير تكتيكي شامل ومتقدم

        Args:
            dataset_path: مسار مجموعة البيانات
            output_dir: دليل الإخراج
            metadata_file: ملف البيانات الوصفية

        Returns:
            تقرير تكتيكي شامل مع جميع المكونات
        """
        try:
            self.logger.info("بدء توليد التقرير التكتيكي الشامل...")

            # إنشاء دليل الإخراج
            output_path = Path(output_dir)
            output_path.mkdir(parents=True, exist_ok=True)

            # التحليل الأساسي
            basic_analysis = self.analyze_rescue_dataset(dataset_path, metadata_file)

            # التحليل المتقدم (إذا كانت الوحدة المتقدمة متوفرة)
            advanced_analysis = {}
            try:
                from .advanced_tactical_analytics import AdvancedTacticalAnalytics
                advanced_analyzer = AdvancedTacticalAnalytics(self.config)
                advanced_analysis = advanced_analyzer.perform_comprehensive_tactical_analysis(dataset_path, metadata_file)
            except ImportError:
                self.logger.warning("الوحدة المتقدمة للتحليل التكتيكي غير متوفرة")

            # توليد الرؤى التكتيكية
            tactical_insights = []
            try:
                from .tactical_insights_generator import TacticalInsightsGenerator
                insights_generator = TacticalInsightsGenerator(self.config)

                # دمج نتائج التحليل الأساسي والمتقدم
                combined_analysis = {**basic_analysis, **advanced_analysis}
                tactical_insights = insights_generator.generate_comprehensive_insights(combined_analysis)

                # تصدير تقرير الرؤى
                insights_report_path = insights_generator.export_insights_report(str(output_path))

            except ImportError:
                self.logger.warning("مولد الرؤى التكتيكية غير متوفر")

            # إنشاء التصورات المتقدمة
            visualization_components = {}
            try:
                from .tactical_visualization import TacticalVisualizationEngine
                viz_engine = TacticalVisualizationEngine(self.config)

                # دمج جميع نتائج التحليل
                all_analysis_results = {**basic_analysis, **advanced_analysis}
                visualization_components = viz_engine.create_tactical_dashboard(all_analysis_results, str(output_path / 'visualizations'))

            except ImportError:
                self.logger.warning("محرك التصور التكتيكي غير متوفر")

            # توليد الرسوم البيانية الأساسية
            basic_visualizations = self.generate_tactical_visualizations(basic_analysis, str(output_path / 'basic_charts'))

            # تجميع التقرير الشامل
            comprehensive_report = {
                'report_metadata': {
                    'title': 'التقرير التكتيكي الشامل للإنقاذ والبحث',
                    'subtitle': 'تحليل متقدم ورؤى تكتيكية لدعم القرار',
                    'generation_timestamp': datetime.now().isoformat(),
                    'dataset_path': dataset_path,
                    'output_directory': str(output_path),
                    'report_version': '2.0.0',
                    'analysis_components': {
                        'basic_analysis': True,
                        'advanced_analysis': bool(advanced_analysis),
                        'tactical_insights': bool(tactical_insights),
                        'advanced_visualizations': bool(visualization_components)
                    }
                },

                'executive_summary': self._generate_enhanced_executive_summary(basic_analysis, advanced_analysis, tactical_insights),

                'basic_analysis': basic_analysis,
                'advanced_analysis': advanced_analysis,
                'tactical_insights': [insight.to_dict() if hasattr(insight, 'to_dict') else insight for insight in tactical_insights],

                'visualizations': {
                    'basic_charts': basic_visualizations,
                    'advanced_dashboard': visualization_components
                },

                'recommendations': self._generate_comprehensive_recommendations(basic_analysis, advanced_analysis, tactical_insights),
                'action_plan': self._generate_detailed_action_plan(tactical_insights),
                'risk_assessment': self._generate_risk_assessment(basic_analysis, advanced_analysis),
                'resource_optimization': self._generate_resource_optimization_plan(basic_analysis, advanced_analysis)
            }

            # حفظ التقرير الشامل
            report_path = output_path / f'comprehensive_tactical_report_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
            with open(report_path, 'w', encoding='utf-8') as f:
                import json
                json.dump(comprehensive_report, f, ensure_ascii=False, indent=2, default=str)

            # إنشاء ملخص تنفيذي منفصل
            executive_summary_path = self._create_executive_summary_document(comprehensive_report, output_path)

            self.logger.info(f"تم إنجاز التقرير التكتيكي الشامل: {report_path}")

            return {
                'comprehensive_report_path': str(report_path),
                'executive_summary_path': executive_summary_path,
                'visualizations_directory': str(output_path / 'visualizations'),
                'insights_report_path': insights_report_path if 'insights_report_path' in locals() else None,
                'report_components': comprehensive_report['report_metadata']['analysis_components'],
                'total_insights_generated': len(tactical_insights),
                'report_size_mb': report_path.stat().st_size / (1024 * 1024) if report_path.exists() else 0
            }

        except Exception as e:
            self.logger.error(f"فشل في توليد التقرير التكتيكي الشامل: {e}")
            raise AnalyticsError(f"فشل في توليد التقرير: {e}")

    def _generate_enhanced_executive_summary(self, basic_analysis: Dict, advanced_analysis: Dict, tactical_insights: List) -> Dict:
        """توليد ملخص تنفيذي محسن"""
        summary = {
            'key_findings': [],
            'critical_insights': [],
            'operational_status': 'غير محدد',
            'readiness_assessment': {},
            'priority_actions': [],
            'performance_indicators': {}
        }

        # استخراج النتائج الرئيسية من التحليل الأساسي
        if 'basic_statistics' in basic_analysis:
            basic_stats = basic_analysis['basic_statistics']
            summary['key_findings'].extend([
                f"تم تحليل {basic_stats.get('total_images', 0)} صورة",
                f"معدل الكشف: {basic_stats.get('images_with_targets', 0) / basic_stats.get('total_images', 1) * 100:.1f}%",
                f"التنوع البيئي: {basic_stats.get('unique_environments', 0)} بيئة مختلفة"
            ])

        # استخراج الرؤى الحرجة من التحليل المتقدم
        if advanced_analysis and 'efficiency_analysis' in advanced_analysis:
            efficiency = advanced_analysis['efficiency_analysis']
            if 'optimization_opportunities' in efficiency:
                critical_opportunities = [
                    opp for opp in efficiency['optimization_opportunities']
                    if opp.get('impact') in ['critical', 'high']
                ]
                summary['critical_insights'].extend([
                    f"تم تحديد {len(critical_opportunities)} فرصة تحسين حرجة",
                    "يوجد إمكانيات لتحسين الكفاءة التشغيلية"
                ])

        # استخراج الإجراءات ذات الأولوية من الرؤى التكتيكية
        if tactical_insights:
            critical_insights = [insight for insight in tactical_insights
                               if (hasattr(insight, 'priority') and insight.priority == 'critical') or
                                  (isinstance(insight, dict) and insight.get('priority') == 'critical')]

            summary['priority_actions'] = [
                f"معالجة {len(critical_insights)} رؤية حرجة",
                "تنفيذ خطة التحسين الفورية",
                "مراجعة العمليات التشغيلية"
            ]

        # تقييم الحالة التشغيلية
        if 'quality_analysis' in basic_analysis:
            quality_score = basic_analysis['quality_analysis'].get('overall_quality_score', 0)
            if quality_score >= 80:
                summary['operational_status'] = 'ممتاز'
            elif quality_score >= 70:
                summary['operational_status'] = 'جيد'
            elif quality_score >= 60:
                summary['operational_status'] = 'مقبول'
            else:
                summary['operational_status'] = 'يحتاج تحسين'

        return summary

    def _generate_comprehensive_recommendations(self, basic_analysis: Dict, advanced_analysis: Dict, tactical_insights: List) -> List[Dict]:
        """توليد توصيات شاملة"""
        recommendations = []

        # توصيات من التحليل الأساسي
        basic_recommendations = self._generate_tactical_recommendations(basic_analysis)
        recommendations.extend(basic_recommendations)

        # توصيات من التحليل المتقدم
        if advanced_analysis and 'efficiency_analysis' in advanced_analysis:
            efficiency_data = advanced_analysis['efficiency_analysis']
            if 'optimization_opportunities' in efficiency_data:
                for opportunity in efficiency_data['optimization_opportunities']:
                    recommendations.append({
                        'category': 'efficiency_optimization',
                        'priority': opportunity.get('impact', 'medium'),
                        'title': opportunity.get('description', 'تحسين الكفاءة'),
                        'description': opportunity.get('potential_improvement', 'تحسين عام في الأداء'),
                        'source': 'advanced_efficiency_analysis'
                    })

        # توصيات من الرؤى التكتيكية
        if tactical_insights:
            for insight in tactical_insights:
                if hasattr(insight, 'recommendations'):
                    for rec in insight.recommendations:
                        recommendations.append({
                            'category': 'tactical_insight',
                            'priority': insight.priority if hasattr(insight, 'priority') else 'medium',
                            'title': rec,
                            'description': f"مستمدة من الرؤية: {insight.title if hasattr(insight, 'title') else 'رؤية تكتيكية'}",
                            'source': 'tactical_insights'
                        })

        return recommendations

    def _generate_detailed_action_plan(self, tactical_insights: List) -> List[Dict]:
        """توليد خطة عمل مفصلة"""
        action_plan = []

        # ترتيب الرؤى حسب الأولوية
        priority_order = {'critical': 4, 'high': 3, 'medium': 2, 'low': 1}

        sorted_insights = sorted(
            tactical_insights,
            key=lambda x: priority_order.get(
                x.priority if hasattr(x, 'priority') else x.get('priority', 'low'), 0
            ),
            reverse=True
        )

        for i, insight in enumerate(sorted_insights[:10]):  # أول 10 رؤى
            if hasattr(insight, 'recommendations'):
                recommendations = insight.recommendations
            elif isinstance(insight, dict) and 'recommendations' in insight:
                recommendations = insight['recommendations']
            else:
                recommendations = ['مراجعة وتحليل إضافي مطلوب']

            for j, recommendation in enumerate(recommendations):
                action_plan.append({
                    'action_id': f"action_{i+1}_{j+1}",
                    'insight_source': insight.title if hasattr(insight, 'title') else insight.get('title', f'رؤية {i+1}'),
                    'action_description': recommendation,
                    'priority': insight.priority if hasattr(insight, 'priority') else insight.get('priority', 'medium'),
                    'timeline': insight.timeline if hasattr(insight, 'timeline') else insight.get('timeline', 'medium_term'),
                    'stakeholders': insight.stakeholders if hasattr(insight, 'stakeholders') else insight.get('stakeholders', ['فريق العمليات']),
                    'estimated_effort': self._estimate_action_effort(recommendation),
                    'expected_impact': self._estimate_action_impact(recommendation)
                })

        return action_plan

    def _estimate_action_effort(self, action_description: str) -> str:
        """تقدير الجهد المطلوب للإجراء"""
        if any(keyword in action_description.lower() for keyword in ['تدريب', 'تطوير', 'تحسين خوارزميات']):
            return 'high'
        elif any(keyword in action_description.lower() for keyword in ['مراجعة', 'تحليل', 'فحص']):
            return 'medium'
        else:
            return 'low'

    def _estimate_action_impact(self, action_description: str) -> str:
        """تقدير التأثير المتوقع للإجراء"""
        if any(keyword in action_description.lower() for keyword in ['تحسين', 'زيادة', 'تطوير']):
            return 'high'
        elif any(keyword in action_description.lower() for keyword in ['مراجعة', 'تحديث']):
            return 'medium'
        else:
            return 'low'

    def _generate_risk_assessment(self, basic_analysis: Dict, advanced_analysis: Dict) -> Dict:
        """توليد تقييم المخاطر"""
        risk_assessment = {
            'identified_risks': [],
            'risk_levels': {'critical': 0, 'high': 0, 'medium': 0, 'low': 0},
            'mitigation_strategies': [],
            'overall_risk_score': 0
        }

        # تحليل مخاطر الجودة
        if 'quality_analysis' in basic_analysis:
            quality_score = basic_analysis['quality_analysis'].get('overall_quality_score', 0)
            if quality_score < 50:
                risk_assessment['identified_risks'].append({
                    'risk_type': 'data_quality',
                    'description': 'جودة البيانات منخفضة جداً',
                    'level': 'critical',
                    'impact': 'تأثير كبير على دقة التحليل والقرارات'
                })
                risk_assessment['risk_levels']['critical'] += 1

        # تحليل مخاطر الكشف
        if 'target_detection_analysis' in basic_analysis:
            detection_rate = basic_analysis['target_detection_analysis'].get('detection_rate_percentage', 0)
            if detection_rate < 30:
                risk_assessment['identified_risks'].append({
                    'risk_type': 'detection_efficiency',
                    'description': 'معدل كشف منخفض جداً',
                    'level': 'high',
                    'impact': 'فشل في اكتشاف أهداف الإنقاذ المهمة'
                })
                risk_assessment['risk_levels']['high'] += 1

        # حساب نقاط المخاطر الإجمالية
        risk_weights = {'critical': 4, 'high': 3, 'medium': 2, 'low': 1}
        total_risk_score = sum(
            risk_assessment['risk_levels'][level] * weight
            for level, weight in risk_weights.items()
        )
        risk_assessment['overall_risk_score'] = total_risk_score

        return risk_assessment

    def _generate_resource_optimization_plan(self, basic_analysis: Dict, advanced_analysis: Dict) -> Dict:
        """توليد خطة تحسين الموارد"""
        optimization_plan = {
            'current_resource_allocation': {},
            'optimization_opportunities': [],
            'recommended_reallocation': {},
            'expected_improvements': {}
        }

        # تحليل التوزيع الحالي للموارد
        if 'environment_analysis' in basic_analysis:
            env_distribution = basic_analysis['environment_analysis'].get('distribution_percentages', {})
            optimization_plan['current_resource_allocation'] = env_distribution

            # تحديد عدم التوازن
            if env_distribution:
                max_percentage = max(env_distribution.values())
                min_percentage = min(env_distribution.values())

                if max_percentage > 60:  # تركز مفرط
                    optimization_plan['optimization_opportunities'].append({
                        'type': 'rebalance_environments',
                        'description': 'إعادة توزيع الموارد بين البيئات المختلفة',
                        'priority': 'medium',
                        'expected_benefit': 'تحسين التغطية الشاملة'
                    })

        return optimization_plan

    def _create_executive_summary_document(self, comprehensive_report: Dict, output_path: Path) -> str:
        """إنشاء وثيقة الملخص التنفيذي"""
        summary_content = f"""
# الملخص التنفيذي - التقرير التكتيكي للإنقاذ والبحث

## معلومات التقرير
- **تاريخ التوليد**: {comprehensive_report['report_metadata']['generation_timestamp']}
- **مسار البيانات**: {comprehensive_report['report_metadata']['dataset_path']}
- **إصدار التقرير**: {comprehensive_report['report_metadata']['report_version']}

## النتائج الرئيسية
"""

        executive_summary = comprehensive_report.get('executive_summary', {})

        # إضافة النتائج الرئيسية
        if 'key_findings' in executive_summary:
            summary_content += "\n### النتائج الرئيسية:\n"
            for finding in executive_summary['key_findings']:
                summary_content += f"- {finding}\n"

        # إضافة الرؤى الحرجة
        if 'critical_insights' in executive_summary:
            summary_content += "\n### الرؤى الحرجة:\n"
            for insight in executive_summary['critical_insights']:
                summary_content += f"- {insight}\n"

        # إضافة الإجراءات ذات الأولوية
        if 'priority_actions' in executive_summary:
            summary_content += "\n### الإجراءات ذات الأولوية:\n"
            for action in executive_summary['priority_actions']:
                summary_content += f"- {action}\n"

        # إضافة الحالة التشغيلية
        operational_status = executive_summary.get('operational_status', 'غير محدد')
        summary_content += f"\n### الحالة التشغيلية: {operational_status}\n"

        # حفظ الملخص التنفيذي
        summary_path = output_path / 'executive_summary.md'
        with open(summary_path, 'w', encoding='utf-8') as f:
            f.write(summary_content)

        return str(summary_path)
