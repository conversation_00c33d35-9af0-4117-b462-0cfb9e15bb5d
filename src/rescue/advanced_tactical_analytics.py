"""
تحليلات تكتيكية متقدمة للإنقاذ والبحث
Advanced Tactical Analytics for Search and Rescue Operations
==========================================================

وحدة تحليلات متقدمة تتضمن تحليلات الأنماط المكانية والزمنية،
تحليل الكفاءة التشغيلية، وتوليد رؤى استراتيجية لعمليات الإنقاذ.
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import logging
from typing import Dict, List, Tuple, Optional, Any, Union
from pathlib import Path
from datetime import datetime, timedelta
import json
import warnings
from collections import Counter, defaultdict
from dataclasses import dataclass

try:
    import plotly.express as px
    import plotly.graph_objects as go
    from plotly.subplots import make_subplots
    import plotly.figure_factory as ff
    PLOTLY_AVAILABLE = True
except ImportError:
    PLOTLY_AVAILABLE = False

try:
    from scipy import stats, spatial
    from scipy.cluster.hierarchy import dendrogram, linkage, fcluster
    from sklearn.cluster import DBSCAN, KMeans
    from sklearn.metrics import silhouette_score
    from sklearn.preprocessing import StandardScaler
    SCIPY_SKLEARN_AVAILABLE = True
except ImportError:
    SCIPY_SKLEARN_AVAILABLE = False

try:
    import folium
    from folium import plugins
    FOLIUM_AVAILABLE = True
except ImportError:
    FOLIUM_AVAILABLE = False

warnings.filterwarnings('ignore')


@dataclass
class TacticalInsight:
    """هيكل بيانات للرؤى التكتيكية"""
    category: str
    priority: str  # 'critical', 'high', 'medium', 'low'
    title: str
    description: str
    confidence: float
    supporting_data: Dict
    recommendations: List[str]
    timestamp: str


class AdvancedTacticalAnalytics:
    """
    محلل البيانات التكتيكي المتقدم للإنقاذ والبحث
    Advanced tactical data analyst for search and rescue operations
    """
    
    def __init__(self, config: Dict):
        """
        تهيئة المحلل التكتيكي المتقدم
        
        Args:
            config: إعدادات التكوين
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # إعدادات التحليل المتقدم
        self.analytics_settings = self._load_advanced_settings()
        
        # مخزن الرؤى التكتيكية
        self.tactical_insights = []
        
        # إعداد الرسوم البيانية
        self._setup_advanced_plotting()
        
    def _load_advanced_settings(self) -> Dict:
        """تحميل إعدادات التحليل المتقدم"""
        return {
            # إعدادات التحليل المكاني
            'spatial_analysis': {
                'enable_clustering': self.config.get('enable_spatial_clustering', True),
                'clustering_algorithm': self.config.get('clustering_algorithm', 'dbscan'),
                'cluster_eps': self.config.get('cluster_eps', 0.1),
                'min_samples': self.config.get('min_samples', 5),
                'enable_hotspot_detection': self.config.get('enable_hotspot_detection', True)
            },
            
            # إعدادات التحليل الزمني
            'temporal_analysis': {
                'enable_trend_analysis': self.config.get('enable_trend_analysis', True),
                'time_window_hours': self.config.get('time_window_hours', 24),
                'enable_seasonality_detection': self.config.get('enable_seasonality_detection', True),
                'forecast_horizon_days': self.config.get('forecast_horizon_days', 7)
            },
            
            # إعدادات تحليل الكفاءة
            'efficiency_analysis': {
                'enable_performance_metrics': self.config.get('enable_performance_metrics', True),
                'response_time_threshold_minutes': self.config.get('response_time_threshold', 30),
                'success_rate_threshold': self.config.get('success_rate_threshold', 0.8),
                'enable_resource_optimization': self.config.get('enable_resource_optimization', True)
            },
            
            # إعدادات الرؤى التكتيكية
            'tactical_insights': {
                'confidence_threshold': self.config.get('insight_confidence_threshold', 0.7),
                'max_insights_per_category': self.config.get('max_insights_per_category', 5),
                'enable_predictive_insights': self.config.get('enable_predictive_insights', True)
            }
        }
    
    def _setup_advanced_plotting(self):
        """إعداد نمط الرسوم البيانية المتقدم"""
        # إعداد نمط متقدم للرسوم
        plt.style.use('seaborn-v0_8-darkgrid')
        
        # ألوان متخصصة للإنقاذ والبحث
        self.rescue_colors = {
            'sea': '#1f77b4',
            'desert': '#ff7f0e', 
            'coast': '#2ca02c',
            'urban': '#d62728',
            'forest': '#9467bd',
            'mountain': '#8c564b',
            'critical': '#ff0000',
            'high': '#ff8c00',
            'medium': '#ffd700',
            'low': '#90ee90',
            'success': '#00ff00',
            'failure': '#ff0000'
        }
        
        # إعداد الخطوط للنصوص العربية
        plt.rcParams['font.family'] = ['DejaVu Sans', 'Arial Unicode MS', 'Tahoma']
        plt.rcParams['axes.unicode_minus'] = False
    
    def perform_comprehensive_tactical_analysis(self, dataset_path: str, metadata_file: str = None) -> Dict:
        """
        تحليل تكتيكي شامل ومتقدم
        
        Args:
            dataset_path: مسار مجموعة البيانات
            metadata_file: ملف البيانات الوصفية
            
        Returns:
            نتائج التحليل التكتيكي الشامل
        """
        try:
            self.logger.info("بدء التحليل التكتيكي الشامل المتقدم...")
            
            # تحميل البيانات
            df = self._load_and_prepare_data(dataset_path, metadata_file)
            
            # التحليلات الأساسية المحسنة
            basic_analysis = self._enhanced_basic_analysis(df)
            
            # التحليل المكاني المتقدم
            spatial_analysis = self._advanced_spatial_analysis(df)
            
            # التحليل الزمني المتقدم
            temporal_analysis = self._advanced_temporal_analysis(df)
            
            # تحليل الكفاءة التشغيلية
            efficiency_analysis = self._operational_efficiency_analysis(df)
            
            # تحليل أنماط الكشف المتقدم
            detection_patterns = self._advanced_detection_pattern_analysis(df)
            
            # تحليل المخاطر والتهديدات
            risk_analysis = self._risk_and_threat_analysis(df)
            
            # تحليل الموارد والتخصيص
            resource_analysis = self._resource_allocation_analysis(df)
            
            # توليد الرؤى التكتيكية
            tactical_insights = self._generate_tactical_insights(df, {
                'basic': basic_analysis,
                'spatial': spatial_analysis,
                'temporal': temporal_analysis,
                'efficiency': efficiency_analysis,
                'detection': detection_patterns,
                'risk': risk_analysis,
                'resource': resource_analysis
            })
            
            # تحليل السيناريوهات المستقبلية
            scenario_analysis = self._scenario_analysis(df)
            
            # تجميع النتائج الشاملة
            comprehensive_results = {
                'analysis_metadata': {
                    'analysis_type': 'comprehensive_tactical',
                    'analysis_timestamp': datetime.now().isoformat(),
                    'dataset_path': dataset_path,
                    'total_records': len(df),
                    'analysis_version': '2.0.0'
                },
                'basic_analysis': basic_analysis,
                'spatial_analysis': spatial_analysis,
                'temporal_analysis': temporal_analysis,
                'efficiency_analysis': efficiency_analysis,
                'detection_patterns': detection_patterns,
                'risk_analysis': risk_analysis,
                'resource_analysis': resource_analysis,
                'tactical_insights': tactical_insights,
                'scenario_analysis': scenario_analysis,
                'executive_summary': self._generate_executive_summary(df, tactical_insights)
            }
            
            self.logger.info("اكتمل التحليل التكتيكي الشامل بنجاح")
            return comprehensive_results
            
        except Exception as e:
            self.logger.error(f"فشل في التحليل التكتيكي الشامل: {e}")
            raise
    
    def _load_and_prepare_data(self, dataset_path: str, metadata_file: str = None) -> pd.DataFrame:
        """تحميل وإعداد البيانات للتحليل المتقدم"""
        # تحميل البيانات الأساسية
        if metadata_file and Path(metadata_file).exists():
            if metadata_file.endswith('.csv'):
                df = pd.read_csv(metadata_file)
            elif metadata_file.endswith('.json'):
                df = pd.read_json(metadata_file)
            else:
                raise ValueError(f"تنسيق ملف غير مدعوم: {metadata_file}")
        else:
            # البحث عن ملفات البيانات الوصفية
            dataset_dir = Path(dataset_path)
            metadata_candidates = [
                dataset_dir / 'rescue_metadata.csv',
                dataset_dir / 'metadata.csv',
                dataset_dir / 'rescue_metadata.json'
            ]
            
            df = None
            for candidate in metadata_candidates:
                if candidate.exists():
                    if candidate.suffix == '.csv':
                        df = pd.read_csv(candidate)
                    elif candidate.suffix == '.json':
                        df = pd.read_json(candidate)
                    break
            
            if df is None:
                raise FileNotFoundError("لم يتم العثور على ملف البيانات الوصفية")
        
        # إعداد وتنظيف البيانات
        df = self._prepare_dataframe(df)
        
        self.logger.info(f"تم تحميل وإعداد {len(df)} سجل للتحليل")
        return df
    
    def _prepare_dataframe(self, df: pd.DataFrame) -> pd.DataFrame:
        """إعداد وتنظيف DataFrame للتحليل"""
        # تحويل الأعمدة الزمنية
        time_columns = ['scan_timestamp', 'classification_timestamp', 'modification_time']
        for col in time_columns:
            if col in df.columns:
                df[col] = pd.to_datetime(df[col], errors='coerce')
        
        # إضافة أعمدة مشتقة للتحليل الزمني
        if 'scan_timestamp' in df.columns:
            df['scan_hour'] = df['scan_timestamp'].dt.hour
            df['scan_day_of_week'] = df['scan_timestamp'].dt.dayofweek
            df['scan_month'] = df['scan_timestamp'].dt.month
        
        # تنظيف البيانات الجغرافية
        if 'bounds' in df.columns:
            df['has_valid_bounds'] = df['bounds'].notna() & (df['bounds'] != '[]')
        
        # إضافة مؤشرات الجودة
        df['quality_score'] = self._calculate_quality_score(df)
        
        # إضافة فئات الأولوية
        df['priority_level'] = self._assign_priority_levels(df)
        
        return df
    
    def _calculate_quality_score(self, df: pd.DataFrame) -> pd.Series:
        """حساب نقاط الجودة لكل سجل"""
        quality_score = pd.Series(100.0, index=df.index)
        
        # خصم نقاط للبيانات المفقودة
        missing_penalty = df.isnull().sum(axis=1) * 2
        quality_score -= missing_penalty
        
        # خصم نقاط للثقة المنخفضة
        if 'environment_confidence' in df.columns:
            low_confidence_penalty = (1 - df['environment_confidence'].fillna(0)) * 20
            quality_score -= low_confidence_penalty
        
        # مكافأة للبيانات الجغرافية
        if 'has_geospatial_data' in df.columns:
            geo_bonus = df['has_geospatial_data'].fillna(False) * 10
            quality_score += geo_bonus
        
        return quality_score.clip(0, 100)
    
    def _assign_priority_levels(self, df: pd.DataFrame) -> pd.Series:
        """تعيين مستويات الأولوية للسجلات"""
        priority = pd.Series('medium', index=df.index)
        
        # أولوية عالية للصور التي تحتوي على أهداف
        if 'targets_detected' in df.columns:
            priority.loc[df['targets_detected'] == True] = 'high'
        
        # أولوية حرجة للصور عالية الثقة مع أهداف متعددة
        if 'total_detections' in df.columns and 'detection_confidence_avg' in df.columns:
            critical_mask = (df['total_detections'] > 2) & (df['detection_confidence_avg'] > 0.8)
            priority.loc[critical_mask] = 'critical'
        
        # أولوية منخفضة للصور منخفضة الجودة
        low_quality_mask = df['quality_score'] < 50
        priority.loc[low_quality_mask] = 'low'
        
        return priority
    
    def _enhanced_basic_analysis(self, df: pd.DataFrame) -> Dict:
        """تحليل أساسي محسن مع مقاييس متقدمة"""
        analysis = {
            'dataset_overview': {
                'total_images': len(df),
                'date_range': self._get_date_range(df),
                'geographic_coverage': self._calculate_geographic_coverage(df),
                'data_quality_distribution': df['quality_score'].describe().to_dict()
            },
            
            'detection_summary': {
                'total_targets_detected': df['total_detections'].sum() if 'total_detections' in df.columns else 0,
                'images_with_targets': df['targets_detected'].sum() if 'targets_detected' in df.columns else 0,
                'detection_rate': (df['targets_detected'].sum() / len(df)) * 100 if 'targets_detected' in df.columns else 0,
                'average_targets_per_image': df['total_detections'].mean() if 'total_detections' in df.columns else 0
            },
            
            'priority_distribution': df['priority_level'].value_counts().to_dict(),
            
            'environment_analysis': self._analyze_environment_distribution_enhanced(df),
            
            'confidence_analysis': self._analyze_confidence_metrics(df)
        }
        
        return analysis
    
    def _get_date_range(self, df: pd.DataFrame) -> Dict:
        """حساب النطاق الزمني للبيانات"""
        if 'scan_timestamp' in df.columns:
            timestamps = df['scan_timestamp'].dropna()
            if not timestamps.empty:
                return {
                    'start_date': timestamps.min().isoformat(),
                    'end_date': timestamps.max().isoformat(),
                    'duration_days': (timestamps.max() - timestamps.min()).days,
                    'total_scans': len(timestamps)
                }
        return {'start_date': None, 'end_date': None, 'duration_days': 0, 'total_scans': 0}
    
    def _calculate_geographic_coverage(self, df: pd.DataFrame) -> Dict:
        """حساب التغطية الجغرافية"""
        if 'has_valid_bounds' in df.columns:
            geo_images = df[df['has_valid_bounds'] == True]
            if not geo_images.empty:
                return {
                    'images_with_coordinates': len(geo_images),
                    'geographic_coverage_percentage': (len(geo_images) / len(df)) * 100,
                    'coordinate_systems_used': geo_images['coordinate_system'].value_counts().to_dict() if 'coordinate_system' in geo_images.columns else {}
                }
        return {'images_with_coordinates': 0, 'geographic_coverage_percentage': 0, 'coordinate_systems_used': {}}
    
    def _analyze_environment_distribution_enhanced(self, df: pd.DataFrame) -> Dict:
        """تحليل محسن لتوزيع البيئات"""
        if 'environment_class' not in df.columns:
            return {}
        
        env_counts = df['environment_class'].value_counts()
        env_percentages = df['environment_class'].value_counts(normalize=True) * 100
        
        # تحليل الثقة حسب البيئة
        confidence_by_env = {}
        if 'environment_confidence' in df.columns:
            confidence_by_env = df.groupby('environment_class')['environment_confidence'].agg([
                'mean', 'std', 'min', 'max', 'count'
            ]).to_dict('index')
        
        # تحليل الأهداف حسب البيئة
        targets_by_env = {}
        if 'targets_detected' in df.columns:
            targets_by_env = df.groupby('environment_class')['targets_detected'].agg([
                'sum', 'mean', 'count'
            ]).to_dict('index')
        
        return {
            'distribution_counts': env_counts.to_dict(),
            'distribution_percentages': env_percentages.to_dict(),
            'confidence_by_environment': confidence_by_env,
            'targets_by_environment': targets_by_env,
            'diversity_index': self._calculate_diversity_index(env_counts),
            'dominant_environment': env_counts.index[0] if len(env_counts) > 0 else None
        }
    
    def _calculate_diversity_index(self, counts: pd.Series) -> float:
        """حساب مؤشر التنوع (Shannon Diversity Index)"""
        if len(counts) == 0:
            return 0.0
        
        proportions = counts / counts.sum()
        return -np.sum(proportions * np.log2(proportions + 1e-10))
    
    def _analyze_confidence_metrics(self, df: pd.DataFrame) -> Dict:
        """تحليل مقاييس الثقة"""
        confidence_analysis = {}
        
        # تحليل ثقة البيئة
        if 'environment_confidence' in df.columns:
            env_conf = df['environment_confidence'].dropna()
            confidence_analysis['environment_confidence'] = {
                'mean': env_conf.mean(),
                'std': env_conf.std(),
                'median': env_conf.median(),
                'high_confidence_percentage': (env_conf > 0.8).mean() * 100,
                'low_confidence_percentage': (env_conf < 0.5).mean() * 100
            }
        
        # تحليل ثقة الكشف
        if 'detection_confidence_avg' in df.columns:
            det_conf = df['detection_confidence_avg'].dropna()
            confidence_analysis['detection_confidence'] = {
                'mean': det_conf.mean(),
                'std': det_conf.std(),
                'median': det_conf.median(),
                'high_confidence_percentage': (det_conf > 0.8).mean() * 100,
                'low_confidence_percentage': (det_conf < 0.5).mean() * 100
            }
        
        return confidence_analysis

    def _advanced_spatial_analysis(self, df: pd.DataFrame) -> Dict:
        """تحليل مكاني متقدم للبيانات"""
        spatial_analysis = {
            'clustering_analysis': {},
            'hotspot_detection': {},
            'spatial_distribution': {},
            'geographic_patterns': {}
        }

        if not SCIPY_SKLEARN_AVAILABLE:
            self.logger.warning("مكتبات التحليل المكاني غير متوفرة")
            return spatial_analysis

        # استخراج الإحداثيات
        coordinates = self._extract_coordinates(df)

        if coordinates is not None and len(coordinates) > 0:
            # تحليل التجميع المكاني
            spatial_analysis['clustering_analysis'] = self._perform_spatial_clustering(coordinates, df)

            # كشف النقاط الساخنة
            spatial_analysis['hotspot_detection'] = self._detect_hotspots(coordinates, df)

            # تحليل التوزيع المكاني
            spatial_analysis['spatial_distribution'] = self._analyze_spatial_distribution(coordinates, df)

            # تحليل الأنماط الجغرافية
            spatial_analysis['geographic_patterns'] = self._analyze_geographic_patterns(coordinates, df)

        return spatial_analysis

    def _extract_coordinates(self, df: pd.DataFrame) -> Optional[np.ndarray]:
        """استخراج الإحداثيات من البيانات"""
        coordinates = []

        if 'bounds' in df.columns:
            for bounds_str in df['bounds'].dropna():
                try:
                    if isinstance(bounds_str, str) and bounds_str != '[]':
                        import ast
                        bounds = ast.literal_eval(bounds_str)
                        if isinstance(bounds, list) and len(bounds) == 4:
                            # حساب النقطة المركزية
                            center_x = (bounds[0] + bounds[2]) / 2
                            center_y = (bounds[1] + bounds[3]) / 2
                            coordinates.append([center_x, center_y])
                except:
                    continue

        return np.array(coordinates) if coordinates else None

    def _perform_spatial_clustering(self, coordinates: np.ndarray, df: pd.DataFrame) -> Dict:
        """تنفيذ التجميع المكاني"""
        clustering_results = {}

        if len(coordinates) < 5:
            return {'error': 'عدد النقاط غير كافي للتجميع'}

        # تطبيع الإحداثيات
        scaler = StandardScaler()
        coords_scaled = scaler.fit_transform(coordinates)

        # تجميع DBSCAN
        if self.analytics_settings['spatial_analysis']['clustering_algorithm'] == 'dbscan':
            eps = self.analytics_settings['spatial_analysis']['cluster_eps']
            min_samples = self.analytics_settings['spatial_analysis']['min_samples']

            dbscan = DBSCAN(eps=eps, min_samples=min_samples)
            cluster_labels = dbscan.fit_predict(coords_scaled)

            clustering_results['dbscan'] = {
                'n_clusters': len(set(cluster_labels)) - (1 if -1 in cluster_labels else 0),
                'n_noise_points': list(cluster_labels).count(-1),
                'cluster_labels': cluster_labels.tolist(),
                'silhouette_score': silhouette_score(coords_scaled, cluster_labels) if len(set(cluster_labels)) > 1 else 0
            }

        # تجميع K-Means
        for k in range(2, min(8, len(coordinates))):
            kmeans = KMeans(n_clusters=k, random_state=42, n_init=10)
            cluster_labels = kmeans.fit_predict(coords_scaled)

            clustering_results[f'kmeans_k{k}'] = {
                'n_clusters': k,
                'cluster_labels': cluster_labels.tolist(),
                'silhouette_score': silhouette_score(coords_scaled, cluster_labels),
                'inertia': kmeans.inertia_,
                'cluster_centers': scaler.inverse_transform(kmeans.cluster_centers_).tolist()
            }

        return clustering_results

    def _detect_hotspots(self, coordinates: np.ndarray, df: pd.DataFrame) -> Dict:
        """كشف النقاط الساخنة للأهداف"""
        hotspot_analysis = {}

        if 'targets_detected' not in df.columns or len(coordinates) == 0:
            return hotspot_analysis

        # تحديد النقاط التي تحتوي على أهداف
        target_mask = df['targets_detected'].fillna(False)
        target_coordinates = coordinates[target_mask[:len(coordinates)]]

        if len(target_coordinates) > 0:
            # حساب كثافة الأهداف
            density_analysis = self._calculate_density_analysis(target_coordinates, coordinates)
            hotspot_analysis['density_analysis'] = density_analysis

            # تحديد المناطق عالية الكثافة
            high_density_zones = self._identify_high_density_zones(target_coordinates)
            hotspot_analysis['high_density_zones'] = high_density_zones

        return hotspot_analysis

    def _calculate_density_analysis(self, target_coords: np.ndarray, all_coords: np.ndarray) -> Dict:
        """حساب تحليل الكثافة"""
        if len(target_coords) == 0:
            return {}

        # حساب المسافات بين النقاط
        distances = spatial.distance_matrix(target_coords, target_coords)

        # حساب متوسط المسافة للجيران الأقرب
        k = min(5, len(target_coords) - 1)
        if k > 0:
            nearest_distances = np.sort(distances, axis=1)[:, 1:k+1]
            avg_nearest_distance = np.mean(nearest_distances)
        else:
            avg_nearest_distance = 0

        return {
            'total_target_points': len(target_coords),
            'average_nearest_neighbor_distance': avg_nearest_distance,
            'density_ratio': len(target_coords) / len(all_coords),
            'spatial_concentration_index': self._calculate_concentration_index(target_coords)
        }

    def _calculate_concentration_index(self, coordinates: np.ndarray) -> float:
        """حساب مؤشر التركز المكاني"""
        if len(coordinates) < 2:
            return 0.0

        # حساب المركز الجغرافي
        center = np.mean(coordinates, axis=0)

        # حساب المسافات من المركز
        distances = np.linalg.norm(coordinates - center, axis=1)

        # حساب مؤشر التركز (معكوس التشتت)
        return 1.0 / (1.0 + np.std(distances))

    def _identify_high_density_zones(self, coordinates: np.ndarray) -> List[Dict]:
        """تحديد المناطق عالية الكثافة"""
        if len(coordinates) < 3:
            return []

        # تجميع النقاط لتحديد المناطق
        dbscan = DBSCAN(eps=0.1, min_samples=3)
        cluster_labels = dbscan.fit_predict(coordinates)

        zones = []
        for cluster_id in set(cluster_labels):
            if cluster_id != -1:  # تجاهل النقاط الشاذة
                cluster_points = coordinates[cluster_labels == cluster_id]

                # حساب الحدود الجغرافية للمنطقة
                min_coords = np.min(cluster_points, axis=0)
                max_coords = np.max(cluster_points, axis=0)
                center = np.mean(cluster_points, axis=0)

                zones.append({
                    'zone_id': int(cluster_id),
                    'point_count': len(cluster_points),
                    'center_coordinates': center.tolist(),
                    'bounding_box': {
                        'min_x': float(min_coords[0]),
                        'min_y': float(min_coords[1]),
                        'max_x': float(max_coords[0]),
                        'max_y': float(max_coords[1])
                    },
                    'density_score': len(cluster_points) / np.prod(max_coords - min_coords + 1e-10)
                })

        # ترتيب المناطق حسب الكثافة
        zones.sort(key=lambda x: x['density_score'], reverse=True)
        return zones

    def _analyze_spatial_distribution(self, coordinates: np.ndarray, df: pd.DataFrame) -> Dict:
        """تحليل التوزيع المكاني"""
        if len(coordinates) == 0:
            return {}

        # حساب الإحصائيات المكانية الأساسية
        center = np.mean(coordinates, axis=0)
        std_dev = np.std(coordinates, axis=0)

        # حساب المدى الجغرافي
        min_coords = np.min(coordinates, axis=0)
        max_coords = np.max(coordinates, axis=0)

        # حساب مؤشر التشتت
        distances_from_center = np.linalg.norm(coordinates - center, axis=1)
        dispersion_index = np.std(distances_from_center) / np.mean(distances_from_center) if np.mean(distances_from_center) > 0 else 0

        return {
            'geographic_center': center.tolist(),
            'standard_deviation': std_dev.tolist(),
            'geographic_extent': {
                'min_coordinates': min_coords.tolist(),
                'max_coordinates': max_coords.tolist(),
                'coverage_area': np.prod(max_coords - min_coords)
            },
            'dispersion_index': float(dispersion_index),
            'spatial_autocorrelation': self._calculate_spatial_autocorrelation(coordinates, df)
        }

    def _calculate_spatial_autocorrelation(self, coordinates: np.ndarray, df: pd.DataFrame) -> Dict:
        """حساب الارتباط الذاتي المكاني"""
        if len(coordinates) < 5 or 'targets_detected' not in df.columns:
            return {}

        # إنشاء مصفوفة الأوزان المكانية
        distances = spatial.distance_matrix(coordinates, coordinates)

        # تحويل المسافات إلى أوزان (الأوزان تقل مع المسافة)
        max_distance = np.max(distances)
        weights = 1 - (distances / max_distance)
        np.fill_diagonal(weights, 0)  # لا وزن للنقطة مع نفسها

        # حساب مؤشر موران للارتباط الذاتي المكاني
        target_values = df['targets_detected'].fillna(False).astype(int)[:len(coordinates)]

        if len(target_values) == len(coordinates):
            moran_i = self._calculate_moran_i(target_values, weights)
            return {
                'moran_i_statistic': moran_i,
                'spatial_clustering_strength': 'قوي' if abs(moran_i) > 0.5 else 'متوسط' if abs(moran_i) > 0.2 else 'ضعيف'
            }

        return {}

    def _calculate_moran_i(self, values: np.ndarray, weights: np.ndarray) -> float:
        """حساب إحصائية موران I للارتباط الذاتي المكاني"""
        n = len(values)
        if n == 0:
            return 0.0

        # تطبيع القيم
        mean_val = np.mean(values)
        deviations = values - mean_val

        # حساب البسط والمقام
        numerator = np.sum(weights * np.outer(deviations, deviations))
        denominator = np.sum(deviations ** 2)

        # حساب مجموع الأوزان
        w_sum = np.sum(weights)

        if denominator == 0 or w_sum == 0:
            return 0.0

        return (n / w_sum) * (numerator / denominator)

    def _analyze_geographic_patterns(self, coordinates: np.ndarray, df: pd.DataFrame) -> Dict:
        """تحليل الأنماط الجغرافية"""
        patterns = {}

        if len(coordinates) == 0:
            return patterns

        # تحليل التوزيع حسب البيئة
        if 'environment_class' in df.columns:
            env_patterns = self._analyze_environmental_spatial_patterns(coordinates, df)
            patterns['environmental_patterns'] = env_patterns

        # تحليل الأنماط الخطية
        linear_patterns = self._detect_linear_patterns(coordinates)
        patterns['linear_patterns'] = linear_patterns

        # تحليل الأنماط الدائرية
        circular_patterns = self._detect_circular_patterns(coordinates)
        patterns['circular_patterns'] = circular_patterns

        return patterns

    def _analyze_environmental_spatial_patterns(self, coordinates: np.ndarray, df: pd.DataFrame) -> Dict:
        """تحليل الأنماط المكانية حسب البيئة"""
        env_patterns = {}

        environments = df['environment_class'].unique()

        for env in environments:
            if pd.isna(env):
                continue

            env_mask = df['environment_class'] == env
            env_coords = coordinates[env_mask[:len(coordinates)]]

            if len(env_coords) > 0:
                # حساب المركز الجغرافي للبيئة
                center = np.mean(env_coords, axis=0)

                # حساب التشتت
                if len(env_coords) > 1:
                    distances = np.linalg.norm(env_coords - center, axis=1)
                    dispersion = np.std(distances)
                else:
                    dispersion = 0

                env_patterns[env] = {
                    'point_count': len(env_coords),
                    'geographic_center': center.tolist(),
                    'spatial_dispersion': float(dispersion),
                    'coverage_area': self._calculate_convex_hull_area(env_coords) if len(env_coords) >= 3 else 0
                }

        return env_patterns

    def _calculate_convex_hull_area(self, coordinates: np.ndarray) -> float:
        """حساب مساحة الغلاف المحدب"""
        if len(coordinates) < 3:
            return 0.0

        try:
            from scipy.spatial import ConvexHull
            hull = ConvexHull(coordinates)
            return float(hull.volume)  # في 2D، volume هو المساحة
        except:
            return 0.0

    def _detect_linear_patterns(self, coordinates: np.ndarray) -> Dict:
        """كشف الأنماط الخطية في التوزيع"""
        if len(coordinates) < 3:
            return {}

        # تطبيق تحليل المكونات الرئيسية
        from sklearn.decomposition import PCA

        pca = PCA(n_components=2)
        pca.fit(coordinates)

        # حساب نسبة التباين المفسر
        explained_variance_ratio = pca.explained_variance_ratio_

        # إذا كان المكون الأول يفسر نسبة عالية من التباين، فهناك نمط خطي
        linearity_score = explained_variance_ratio[0] / (explained_variance_ratio[0] + explained_variance_ratio[1])

        return {
            'linearity_score': float(linearity_score),
            'is_linear_pattern': linearity_score > 0.8,
            'principal_direction': pca.components_[0].tolist(),
            'explained_variance_ratio': explained_variance_ratio.tolist()
        }

    def _detect_circular_patterns(self, coordinates: np.ndarray) -> Dict:
        """كشف الأنماط الدائرية في التوزيع"""
        if len(coordinates) < 4:
            return {}

        # حساب المركز
        center = np.mean(coordinates, axis=0)

        # حساب المسافات من المركز
        distances = np.linalg.norm(coordinates - center, axis=1)

        # حساب معامل التباين في المسافات
        distance_cv = np.std(distances) / np.mean(distances) if np.mean(distances) > 0 else float('inf')

        # نمط دائري إذا كان التباين في المسافات منخفض
        is_circular = distance_cv < 0.3

        return {
            'circularity_score': float(1.0 / (1.0 + distance_cv)),
            'is_circular_pattern': is_circular,
            'center_coordinates': center.tolist(),
            'average_radius': float(np.mean(distances)),
            'radius_standard_deviation': float(np.std(distances))
        }

    def _advanced_temporal_analysis(self, df: pd.DataFrame) -> Dict:
        """تحليل زمني متقدم للبيانات"""
        temporal_analysis = {
            'time_series_analysis': {},
            'trend_analysis': {},
            'seasonality_analysis': {},
            'peak_detection': {},
            'temporal_patterns': {}
        }

        if 'scan_timestamp' not in df.columns:
            return temporal_analysis

        # تحضير البيانات الزمنية
        df_time = df.dropna(subset=['scan_timestamp']).copy()
        df_time = df_time.sort_values('scan_timestamp')

        if len(df_time) == 0:
            return temporal_analysis

        # تحليل السلاسل الزمنية
        temporal_analysis['time_series_analysis'] = self._analyze_time_series(df_time)

        # تحليل الاتجاهات
        temporal_analysis['trend_analysis'] = self._analyze_trends(df_time)

        # تحليل الموسمية
        temporal_analysis['seasonality_analysis'] = self._analyze_seasonality(df_time)

        # كشف القمم والأنشطة المكثفة
        temporal_analysis['peak_detection'] = self._detect_activity_peaks(df_time)

        # تحليل الأنماط الزمنية
        temporal_analysis['temporal_patterns'] = self._analyze_temporal_patterns(df_time)

        return temporal_analysis

    def _analyze_time_series(self, df: pd.DataFrame) -> Dict:
        """تحليل السلاسل الزمنية"""
        # تجميع البيانات حسب الساعة
        hourly_counts = df.set_index('scan_timestamp').resample('H').size()
        daily_counts = df.set_index('scan_timestamp').resample('D').size()

        # إحصائيات أساسية
        time_stats = {
            'total_time_span_hours': (df['scan_timestamp'].max() - df['scan_timestamp'].min()).total_seconds() / 3600,
            'average_scans_per_hour': hourly_counts.mean(),
            'max_scans_per_hour': hourly_counts.max(),
            'average_scans_per_day': daily_counts.mean(),
            'max_scans_per_day': daily_counts.max(),
            'scanning_intensity_variance': hourly_counts.var()
        }

        # تحليل الفجوات الزمنية
        time_gaps = df['scan_timestamp'].diff().dropna()
        gap_stats = {
            'average_gap_minutes': time_gaps.mean().total_seconds() / 60,
            'max_gap_hours': time_gaps.max().total_seconds() / 3600,
            'min_gap_seconds': time_gaps.min().total_seconds(),
            'gap_variance_minutes': (time_gaps.var().total_seconds() / 60) if time_gaps.var() else 0
        }

        return {
            'time_statistics': time_stats,
            'gap_analysis': gap_stats,
            'hourly_distribution': hourly_counts.to_dict(),
            'daily_distribution': daily_counts.to_dict()
        }

    def _analyze_trends(self, df: pd.DataFrame) -> Dict:
        """تحليل الاتجاهات الزمنية"""
        # تجميع البيانات يومياً
        daily_data = df.set_index('scan_timestamp').resample('D').agg({
            'targets_detected': 'sum' if 'targets_detected' in df.columns else 'count',
            'total_detections': 'sum' if 'total_detections' in df.columns else 'count',
            'environment_confidence': 'mean' if 'environment_confidence' in df.columns else 'count'
        })

        trends = {}

        # تحليل اتجاه الكشوفات
        if 'targets_detected' in daily_data.columns:
            detection_trend = self._calculate_trend(daily_data['targets_detected'])
            trends['detection_trend'] = detection_trend

        # تحليل اتجاه الثقة
        if 'environment_confidence' in daily_data.columns:
            confidence_trend = self._calculate_trend(daily_data['environment_confidence'])
            trends['confidence_trend'] = confidence_trend

        # تحليل اتجاه النشاط العام
        activity_counts = daily_data.index.to_series().resample('D').count()
        activity_trend = self._calculate_trend(activity_counts)
        trends['activity_trend'] = activity_trend

        return trends

    def _calculate_trend(self, series: pd.Series) -> Dict:
        """حساب الاتجاه لسلسلة زمنية"""
        if len(series) < 3:
            return {'trend': 'insufficient_data'}

        # إزالة القيم المفقودة
        clean_series = series.dropna()
        if len(clean_series) < 3:
            return {'trend': 'insufficient_data'}

        # حساب الانحدار الخطي
        x = np.arange(len(clean_series))
        y = clean_series.values

        if len(x) > 1 and np.var(x) > 0:
            slope, intercept, r_value, p_value, std_err = stats.linregress(x, y)

            # تحديد اتجاه الاتجاه
            if abs(slope) < std_err:
                trend_direction = 'stable'
            elif slope > 0:
                trend_direction = 'increasing'
            else:
                trend_direction = 'decreasing'

            return {
                'trend': trend_direction,
                'slope': float(slope),
                'r_squared': float(r_value ** 2),
                'p_value': float(p_value),
                'significance': 'significant' if p_value < 0.05 else 'not_significant',
                'trend_strength': 'strong' if abs(r_value) > 0.7 else 'moderate' if abs(r_value) > 0.3 else 'weak'
            }

        return {'trend': 'no_trend'}

    def _analyze_seasonality(self, df: pd.DataFrame) -> Dict:
        """تحليل الموسمية والأنماط الدورية"""
        seasonality = {}

        # تحليل الأنماط اليومية (حسب الساعة)
        hourly_pattern = df.groupby('scan_hour').size()
        seasonality['hourly_pattern'] = {
            'distribution': hourly_pattern.to_dict(),
            'peak_hour': int(hourly_pattern.idxmax()),
            'low_hour': int(hourly_pattern.idxmin()),
            'pattern_strength': float(hourly_pattern.std() / hourly_pattern.mean()) if hourly_pattern.mean() > 0 else 0
        }

        # تحليل الأنماط الأسبوعية
        weekly_pattern = df.groupby('scan_day_of_week').size()
        day_names = ['الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت', 'الأحد']
        seasonality['weekly_pattern'] = {
            'distribution': {day_names[i]: int(weekly_pattern.get(i, 0)) for i in range(7)},
            'peak_day': day_names[weekly_pattern.idxmax()] if len(weekly_pattern) > 0 else None,
            'low_day': day_names[weekly_pattern.idxmin()] if len(weekly_pattern) > 0 else None,
            'weekend_vs_weekday_ratio': float(weekly_pattern[5:].sum() / weekly_pattern[:5].sum()) if weekly_pattern[:5].sum() > 0 else 0
        }

        # تحليل الأنماط الشهرية
        if len(df) > 30:  # فقط إذا كانت البيانات تغطي أكثر من شهر
            monthly_pattern = df.groupby('scan_month').size()
            month_names = ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
                          'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر']
            seasonality['monthly_pattern'] = {
                'distribution': {month_names[i-1]: int(monthly_pattern.get(i, 0)) for i in range(1, 13)},
                'peak_month': month_names[monthly_pattern.idxmax() - 1] if len(monthly_pattern) > 0 else None,
                'seasonal_variation': float(monthly_pattern.std() / monthly_pattern.mean()) if monthly_pattern.mean() > 0 else 0
            }

        return seasonality

    def _detect_activity_peaks(self, df: pd.DataFrame) -> Dict:
        """كشف قمم النشاط والفترات المكثفة"""
        # تجميع البيانات حسب الساعة
        hourly_activity = df.set_index('scan_timestamp').resample('H').size()

        if len(hourly_activity) < 5:
            return {'error': 'بيانات غير كافية لكشف القمم'}

        # حساب العتبة للقمم (المتوسط + انحراف معياري)
        threshold = hourly_activity.mean() + hourly_activity.std()

        # تحديد القمم
        peaks = hourly_activity[hourly_activity > threshold]

        peak_analysis = {
            'total_peaks': len(peaks),
            'peak_threshold': float(threshold),
            'average_peak_intensity': float(peaks.mean()) if len(peaks) > 0 else 0,
            'max_peak_intensity': float(peaks.max()) if len(peaks) > 0 else 0,
            'peak_times': [timestamp.isoformat() for timestamp in peaks.index] if len(peaks) > 0 else []
        }

        # تحليل فترات الهدوء
        low_threshold = hourly_activity.mean() - hourly_activity.std()
        quiet_periods = hourly_activity[hourly_activity < low_threshold]

        peak_analysis['quiet_periods'] = {
            'total_quiet_hours': len(quiet_periods),
            'average_quiet_intensity': float(quiet_periods.mean()) if len(quiet_periods) > 0 else 0,
            'longest_quiet_period_hours': self._find_longest_consecutive_period(quiet_periods)
        }

        return peak_analysis

    def _find_longest_consecutive_period(self, periods: pd.Series) -> int:
        """العثور على أطول فترة متتالية"""
        if len(periods) == 0:
            return 0

        # تحويل إلى قائمة من الفهارس
        indices = periods.index.tolist()

        max_consecutive = 1
        current_consecutive = 1

        for i in range(1, len(indices)):
            # فحص إذا كانت الساعة التالية متتالية
            if (indices[i] - indices[i-1]).total_seconds() == 3600:  # ساعة واحدة
                current_consecutive += 1
                max_consecutive = max(max_consecutive, current_consecutive)
            else:
                current_consecutive = 1

        return max_consecutive

    def _analyze_temporal_patterns(self, df: pd.DataFrame) -> Dict:
        """تحليل الأنماط الزمنية المعقدة"""
        patterns = {}

        # تحليل أنماط الكشف حسب الوقت
        if 'targets_detected' in df.columns:
            detection_patterns = self._analyze_detection_temporal_patterns(df)
            patterns['detection_patterns'] = detection_patterns

        # تحليل أنماط البيئة حسب الوقت
        if 'environment_class' in df.columns:
            environment_patterns = self._analyze_environment_temporal_patterns(df)
            patterns['environment_patterns'] = environment_patterns

        # تحليل أنماط الثقة حسب الوقت
        if 'environment_confidence' in df.columns:
            confidence_patterns = self._analyze_confidence_temporal_patterns(df)
            patterns['confidence_patterns'] = confidence_patterns

        return patterns

    def _analyze_detection_temporal_patterns(self, df: pd.DataFrame) -> Dict:
        """تحليل أنماط الكشف الزمنية"""
        # معدل الكشف حسب الساعة
        hourly_detection_rate = df.groupby('scan_hour')['targets_detected'].mean()

        # معدل الكشف حسب يوم الأسبوع
        daily_detection_rate = df.groupby('scan_day_of_week')['targets_detected'].mean()

        return {
            'hourly_detection_rates': hourly_detection_rate.to_dict(),
            'daily_detection_rates': daily_detection_rate.to_dict(),
            'peak_detection_hour': int(hourly_detection_rate.idxmax()) if len(hourly_detection_rate) > 0 else None,
            'peak_detection_day': int(daily_detection_rate.idxmax()) if len(daily_detection_rate) > 0 else None,
            'detection_time_correlation': self._calculate_time_correlation(df, 'targets_detected')
        }

    def _analyze_environment_temporal_patterns(self, df: pd.DataFrame) -> Dict:
        """تحليل أنماط البيئة الزمنية"""
        env_time_patterns = {}

        for env in df['environment_class'].unique():
            if pd.isna(env):
                continue

            env_data = df[df['environment_class'] == env]

            # توزيع زمني للبيئة
            hourly_dist = env_data.groupby('scan_hour').size()
            daily_dist = env_data.groupby('scan_day_of_week').size()

            env_time_patterns[env] = {
                'hourly_distribution': hourly_dist.to_dict(),
                'daily_distribution': daily_dist.to_dict(),
                'peak_scanning_hour': int(hourly_dist.idxmax()) if len(hourly_dist) > 0 else None,
                'total_scans': len(env_data)
            }

        return env_time_patterns

    def _analyze_confidence_temporal_patterns(self, df: pd.DataFrame) -> Dict:
        """تحليل أنماط الثقة الزمنية"""
        # متوسط الثقة حسب الساعة
        hourly_confidence = df.groupby('scan_hour')['environment_confidence'].mean()

        # متوسط الثقة حسب يوم الأسبوع
        daily_confidence = df.groupby('scan_day_of_week')['environment_confidence'].mean()

        return {
            'hourly_confidence_averages': hourly_confidence.to_dict(),
            'daily_confidence_averages': daily_confidence.to_dict(),
            'peak_confidence_hour': int(hourly_confidence.idxmax()) if len(hourly_confidence) > 0 else None,
            'low_confidence_hour': int(hourly_confidence.idxmin()) if len(hourly_confidence) > 0 else None,
            'confidence_time_correlation': self._calculate_time_correlation(df, 'environment_confidence')
        }

    def _calculate_time_correlation(self, df: pd.DataFrame, column: str) -> Dict:
        """حساب الارتباط بين الوقت والمتغير المحدد"""
        if column not in df.columns:
            return {}

        # ارتباط مع الساعة
        hour_corr = df['scan_hour'].corr(df[column]) if df[column].dtype in ['int64', 'float64', 'bool'] else 0

        # ارتباط مع يوم الأسبوع
        day_corr = df['scan_day_of_week'].corr(df[column]) if df[column].dtype in ['int64', 'float64', 'bool'] else 0

        return {
            'hour_correlation': float(hour_corr) if not pd.isna(hour_corr) else 0,
            'day_correlation': float(day_corr) if not pd.isna(day_corr) else 0,
            'correlation_strength': 'strong' if max(abs(hour_corr), abs(day_corr)) > 0.5 else 'moderate' if max(abs(hour_corr), abs(day_corr)) > 0.3 else 'weak'
        }

    def _operational_efficiency_analysis(self, df: pd.DataFrame) -> Dict:
        """تحليل الكفاءة التشغيلية"""
        efficiency_analysis = {
            'performance_metrics': {},
            'resource_utilization': {},
            'response_time_analysis': {},
            'success_rate_analysis': {},
            'optimization_opportunities': {}
        }

        # مقاييس الأداء الأساسية
        efficiency_analysis['performance_metrics'] = self._calculate_performance_metrics(df)

        # تحليل استخدام الموارد
        efficiency_analysis['resource_utilization'] = self._analyze_resource_utilization(df)

        # تحليل أوقات الاستجابة
        efficiency_analysis['response_time_analysis'] = self._analyze_response_times(df)

        # تحليل معدلات النجاح
        efficiency_analysis['success_rate_analysis'] = self._analyze_success_rates(df)

        # فرص التحسين
        efficiency_analysis['optimization_opportunities'] = self._identify_optimization_opportunities(df)

        return efficiency_analysis

    def _calculate_performance_metrics(self, df: pd.DataFrame) -> Dict:
        """حساب مقاييس الأداء الأساسية"""
        metrics = {}

        # معدل الإنتاجية (صور في الساعة)
        if 'scan_timestamp' in df.columns and len(df) > 1:
            time_span_hours = (df['scan_timestamp'].max() - df['scan_timestamp'].min()).total_seconds() / 3600
            productivity_rate = len(df) / time_span_hours if time_span_hours > 0 else 0
            metrics['productivity_rate_images_per_hour'] = float(productivity_rate)

        # معدل الكشف الفعال
        if 'targets_detected' in df.columns:
            detection_rate = df['targets_detected'].sum() / len(df) * 100
            metrics['effective_detection_rate_percentage'] = float(detection_rate)

        # معدل الثقة العام
        if 'environment_confidence' in df.columns:
            avg_confidence = df['environment_confidence'].mean()
            metrics['average_confidence_score'] = float(avg_confidence) if not pd.isna(avg_confidence) else 0

        # كفاءة المعالجة
        if 'processing_time' in df.columns:
            avg_processing_time = df['processing_time'].mean()
            metrics['average_processing_time_seconds'] = float(avg_processing_time) if not pd.isna(avg_processing_time) else 0

        # معدل الجودة
        quality_score = df['quality_score'].mean() if 'quality_score' in df.columns else 0
        metrics['average_quality_score'] = float(quality_score)

        return metrics

    def _analyze_resource_utilization(self, df: pd.DataFrame) -> Dict:
        """تحليل استخدام الموارد"""
        utilization = {}

        # توزيع العمل حسب الوقت
        if 'scan_timestamp' in df.columns:
            hourly_workload = df.set_index('scan_timestamp').resample('H').size()

            utilization['temporal_distribution'] = {
                'peak_hour_workload': int(hourly_workload.max()) if len(hourly_workload) > 0 else 0,
                'average_hourly_workload': float(hourly_workload.mean()) if len(hourly_workload) > 0 else 0,
                'workload_variance': float(hourly_workload.var()) if len(hourly_workload) > 0 else 0,
                'utilization_efficiency': self._calculate_utilization_efficiency(hourly_workload)
            }

        # استخدام الموارد حسب البيئة
        if 'environment_class' in df.columns:
            env_workload = df['environment_class'].value_counts()
            total_workload = len(df)

            utilization['environment_distribution'] = {
                'workload_by_environment': env_workload.to_dict(),
                'environment_utilization_percentages': (env_workload / total_workload * 100).to_dict(),
                'most_utilized_environment': env_workload.index[0] if len(env_workload) > 0 else None,
                'resource_balance_score': self._calculate_resource_balance_score(env_workload)
            }

        return utilization

    def _calculate_utilization_efficiency(self, workload_series: pd.Series) -> float:
        """حساب كفاءة الاستخدام"""
        if len(workload_series) == 0:
            return 0.0

        # كفاءة عالية عندما يكون التوزيع متساوي
        mean_workload = workload_series.mean()
        std_workload = workload_series.std()

        if mean_workload == 0:
            return 0.0

        # معامل التباين المعكوس (كلما قل التباين، زادت الكفاءة)
        cv = std_workload / mean_workload
        efficiency = 1.0 / (1.0 + cv)

        return float(efficiency)

    def _calculate_resource_balance_score(self, workload_counts: pd.Series) -> float:
        """حساب نقاط توازن الموارد"""
        if len(workload_counts) == 0:
            return 0.0

        # حساب مؤشر جيني للتوازن
        sorted_counts = np.sort(workload_counts.values)
        n = len(sorted_counts)
        cumsum = np.cumsum(sorted_counts)

        if cumsum[-1] == 0:
            return 1.0  # توازن مثالي إذا كانت جميع القيم صفر

        gini = (n + 1 - 2 * np.sum(cumsum) / cumsum[-1]) / n
        balance_score = 1.0 - gini  # تحويل جيني إلى نقاط توازن

        return float(balance_score)

    def _analyze_response_times(self, df: pd.DataFrame) -> Dict:
        """تحليل أوقات الاستجابة"""
        response_analysis = {}

        if 'processing_time' in df.columns:
            processing_times = df['processing_time'].dropna()

            if len(processing_times) > 0:
                response_analysis['processing_time_statistics'] = {
                    'mean_seconds': float(processing_times.mean()),
                    'median_seconds': float(processing_times.median()),
                    'std_seconds': float(processing_times.std()),
                    'min_seconds': float(processing_times.min()),
                    'max_seconds': float(processing_times.max()),
                    'percentile_95': float(processing_times.quantile(0.95))
                }

                # تحليل الأداء حسب العتبات
                threshold = self.analytics_settings['efficiency_analysis']['response_time_threshold_minutes'] * 60
                fast_responses = (processing_times <= threshold).sum()
                response_analysis['performance_against_threshold'] = {
                    'threshold_seconds': threshold,
                    'responses_within_threshold': int(fast_responses),
                    'percentage_within_threshold': float(fast_responses / len(processing_times) * 100),
                    'performance_grade': self._grade_response_performance(fast_responses / len(processing_times))
                }

        return response_analysis

    def _grade_response_performance(self, percentage: float) -> str:
        """تقييم أداء الاستجابة"""
        if percentage >= 0.95:
            return 'ممتاز'
        elif percentage >= 0.85:
            return 'جيد جداً'
        elif percentage >= 0.70:
            return 'جيد'
        elif percentage >= 0.50:
            return 'مقبول'
        else:
            return 'يحتاج تحسين'

    def _analyze_success_rates(self, df: pd.DataFrame) -> Dict:
        """تحليل معدلات النجاح"""
        success_analysis = {}

        # معدل نجاح الكشف
        if 'targets_detected' in df.columns:
            detection_success_rate = df['targets_detected'].mean()
            success_analysis['detection_success_rate'] = {
                'overall_rate': float(detection_success_rate),
                'success_grade': self._grade_success_rate(detection_success_rate),
                'successful_detections': int(df['targets_detected'].sum()),
                'total_attempts': len(df)
            }

        # معدل نجاح الثقة
        if 'environment_confidence' in df.columns:
            high_confidence_threshold = 0.8
            high_confidence_rate = (df['environment_confidence'] >= high_confidence_threshold).mean()
            success_analysis['confidence_success_rate'] = {
                'high_confidence_rate': float(high_confidence_rate),
                'confidence_threshold': high_confidence_threshold,
                'success_grade': self._grade_success_rate(high_confidence_rate)
            }

        # معدل نجاح الجودة
        if 'quality_score' in df.columns:
            high_quality_threshold = 80
            high_quality_rate = (df['quality_score'] >= high_quality_threshold).mean()
            success_analysis['quality_success_rate'] = {
                'high_quality_rate': float(high_quality_rate),
                'quality_threshold': high_quality_threshold,
                'success_grade': self._grade_success_rate(high_quality_rate)
            }

        return success_analysis

    def _grade_success_rate(self, rate: float) -> str:
        """تقييم معدل النجاح"""
        if rate >= 0.90:
            return 'ممتاز'
        elif rate >= 0.80:
            return 'جيد جداً'
        elif rate >= 0.70:
            return 'جيد'
        elif rate >= 0.60:
            return 'مقبول'
        else:
            return 'يحتاج تحسين'

    def _identify_optimization_opportunities(self, df: pd.DataFrame) -> List[Dict]:
        """تحديد فرص التحسين"""
        opportunities = []

        # فرص تحسين الكفاءة الزمنية
        if 'processing_time' in df.columns:
            slow_processing = df[df['processing_time'] > df['processing_time'].quantile(0.9)]
            if len(slow_processing) > 0:
                opportunities.append({
                    'category': 'performance',
                    'type': 'processing_speed',
                    'description': f'تحسين سرعة المعالجة للصور البطيئة ({len(slow_processing)} صورة)',
                    'impact': 'high',
                    'affected_records': len(slow_processing),
                    'potential_improvement': '20-30% تحسن في السرعة'
                })

        # فرص تحسين معدل الكشف
        if 'targets_detected' in df.columns:
            detection_rate = df['targets_detected'].mean()
            if detection_rate < 0.5:
                opportunities.append({
                    'category': 'detection',
                    'type': 'detection_rate',
                    'description': f'تحسين معدل الكشف الحالي ({detection_rate:.1%})',
                    'impact': 'critical',
                    'current_rate': float(detection_rate),
                    'target_rate': 0.7,
                    'potential_improvement': 'زيادة 40% في الكشوفات الناجحة'
                })

        # فرص تحسين توزيع الموارد
        if 'environment_class' in df.columns:
            env_distribution = df['environment_class'].value_counts(normalize=True)
            if env_distribution.max() > 0.7:  # تركز مفرط في بيئة واحدة
                opportunities.append({
                    'category': 'resource_allocation',
                    'type': 'environment_balance',
                    'description': 'إعادة توزيع الموارد لتحقيق توازن أفضل بين البيئات',
                    'impact': 'medium',
                    'dominant_environment': env_distribution.index[0],
                    'concentration_percentage': float(env_distribution.max() * 100),
                    'potential_improvement': 'توزيع أكثر عدالة للموارد'
                })

        # فرص تحسين الجودة
        if 'quality_score' in df.columns:
            low_quality_images = df[df['quality_score'] < 60]
            if len(low_quality_images) > len(df) * 0.1:  # أكثر من 10% منخفضة الجودة
                opportunities.append({
                    'category': 'quality',
                    'type': 'image_quality',
                    'description': f'تحسين جودة الصور المنخفضة ({len(low_quality_images)} صورة)',
                    'impact': 'medium',
                    'affected_records': len(low_quality_images),
                    'percentage_affected': float(len(low_quality_images) / len(df) * 100),
                    'potential_improvement': 'تحسين 15-25% في دقة التصنيف'
                })

        return opportunities
