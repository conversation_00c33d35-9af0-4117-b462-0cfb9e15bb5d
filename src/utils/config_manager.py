"""
Configuration Manager Module
===========================

Handles configuration file loading, validation, and management.
"""

import logging
from pathlib import Path
from typing import Dict, Any, Optional
import yaml
import json
from copy import deepcopy

from .exceptions import ConfigurationError


class ConfigManager:
    """
    Configuration manager with support for YAML and JSON formats.
    """
    
    DEFAULT_CONFIG = {
        # Scanner settings
        'validate_mime_type': True,
        
        # Processor settings
        'target_size': [224, 224],
        'maintain_aspect_ratio': True,
        'convert_to_grayscale': False,
        'normalize': True,
        'enhance_quality': True,
        'batch_size': 32,
        'brightness_factor': 1.0,
        'contrast_factor': 1.0,
        'sharpness_factor': 1.0,
        'noise_reduction': True,
        
        # Classifier settings
        'use_pretrained_model': True,
        'model_name': 'resnet50',
        'confidence_threshold': 0.5,
        'custom_categories': [],

        # Enhanced File Size Classification
        'file_size_classification': {
            'enabled': True,
            'thresholds': {
                'small': 512000,    # < 500KB
                'medium': 2097152,  # < 2MB
            }
        },

        # Enhanced Dimension Classification
        'dimension_classification': {
            'enabled': True,
            'resolution_thresholds': {
                'low_resolution': [640, 480],
                'standard_resolution': [1920, 1080],
            },
            'aspect_ratio_tolerance': 0.1
        },

        # Content Classification Settings
        'content_classification': {
            'enabled': True,
            'available_models': ['resnet50', 'efficientnet-b0', 'mobilenetv2'],
            'model_confidence_threshold': 0.3,
            'max_predictions': 5
        },

        # Object Detection Settings
        'object_detection': {
            'enabled': False,
            'model_name': 'yolov8n',
            'confidence_threshold': 0.5,
            'iou_threshold': 0.45,
            'max_detections': 100,
            'save_detection_images': False,
            'object_categories': {
                'contains_objects': 'Contains Objects',
                'object_free': 'Object-Free'
            }
        },

        # Legacy settings (backward compatibility)
        'size_thresholds': {
            'small': 100000,    # < 100KB
            'medium': 1000000,  # < 1MB
            'large': 5000000    # < 5MB
        },
        'dimension_thresholds': {
            'thumbnail': [150, 150],
            'small': [512, 512],
            'medium': [1024, 1024],
            'large': [2048, 2048]
        },
        
        # Dataset manager settings
        'split_ratios': [0.7, 0.2, 0.1],
        'naming_pattern': '{category}_{index:06d}',
        'preserve_original_names': False,
        'copy_images': True,
        'create_symlinks': False,
        'export_formats': ['csv', 'json', 'yaml'],
        
        # General settings
        'resume': False,
        'log_level': 'INFO',
        'log_file': None
    }
    
    def __init__(self, config_path: Optional[str] = None):
        """
        Initialize configuration manager.
        
        Args:
            config_path: Path to configuration file
        """
        self.config_path = Path(config_path) if config_path else None
        self.logger = logging.getLogger(__name__)
        self._config = deepcopy(self.DEFAULT_CONFIG)
    
    def load_config(self) -> Dict[str, Any]:
        """
        Load configuration from file or return default configuration.
        
        Returns:
            Configuration dictionary
            
        Raises:
            ConfigurationError: If configuration loading fails
        """
        if self.config_path and self.config_path.exists():
            try:
                self.logger.info(f"Loading configuration from {self.config_path}")
                
                if self.config_path.suffix.lower() in ['.yaml', '.yml']:
                    config_data = self._load_yaml()
                elif self.config_path.suffix.lower() == '.json':
                    config_data = self._load_json()
                else:
                    raise ConfigurationError(f"Unsupported configuration file format: {self.config_path.suffix}")
                
                # Merge with default configuration
                self._config.update(config_data)
                
                # Validate configuration
                self._validate_config()
                
                self.logger.info("Configuration loaded successfully")
                
            except Exception as e:
                raise ConfigurationError(f"Failed to load configuration: {e}")
        else:
            if self.config_path:
                self.logger.warning(f"Configuration file not found: {self.config_path}")
            self.logger.info("Using default configuration")
        
        return self._config.copy()
    
    def _load_yaml(self) -> Dict[str, Any]:
        """
        Load configuration from YAML file.
        
        Returns:
            Configuration dictionary
        """
        with open(self.config_path, 'r') as f:
            return yaml.safe_load(f) or {}
    
    def _load_json(self) -> Dict[str, Any]:
        """
        Load configuration from JSON file.
        
        Returns:
            Configuration dictionary
        """
        with open(self.config_path, 'r') as f:
            return json.load(f)
    
    def _validate_config(self):
        """
        Validate configuration values.
        
        Raises:
            ConfigurationError: If configuration is invalid
        """
        # Validate split ratios
        split_ratios = self._config.get('split_ratios', [])
        if len(split_ratios) != 3:
            raise ConfigurationError("split_ratios must contain exactly 3 values")
        
        if abs(sum(split_ratios) - 1.0) > 1e-6:
            raise ConfigurationError("split_ratios must sum to 1.0")
        
        if any(ratio < 0 or ratio > 1 for ratio in split_ratios):
            raise ConfigurationError("split_ratios values must be between 0 and 1")
        
        # Validate target size
        target_size = self._config.get('target_size', [])
        if len(target_size) != 2 or any(not isinstance(x, int) or x <= 0 for x in target_size):
            raise ConfigurationError("target_size must be a list of two positive integers")
        
        # Validate confidence threshold
        confidence_threshold = self._config.get('confidence_threshold', 0)
        if not 0 <= confidence_threshold <= 1:
            raise ConfigurationError("confidence_threshold must be between 0 and 1")
        
        # Validate batch size
        batch_size = self._config.get('batch_size', 0)
        if not isinstance(batch_size, int) or batch_size <= 0:
            raise ConfigurationError("batch_size must be a positive integer")
        
        # Validate enhancement factors
        for factor_name in ['brightness_factor', 'contrast_factor', 'sharpness_factor']:
            factor = self._config.get(factor_name, 0)
            if not isinstance(factor, (int, float)) or factor <= 0:
                raise ConfigurationError(f"{factor_name} must be a positive number")
        
        # Validate export formats
        valid_formats = {'csv', 'json', 'yaml'}
        export_formats = set(self._config.get('export_formats', []))
        if not export_formats.issubset(valid_formats):
            invalid_formats = export_formats - valid_formats
            raise ConfigurationError(f"Invalid export formats: {invalid_formats}")

        # Validate enhanced classification settings
        self._validate_classification_settings()

    def _validate_classification_settings(self):
        """Validate enhanced classification configuration settings."""
        # Validate file size classification
        file_size_config = self._config.get('file_size_classification', {})
        if file_size_config.get('enabled', True):
            thresholds = file_size_config.get('thresholds', {})
            small_threshold = thresholds.get('small', 0)
            medium_threshold = thresholds.get('medium', 0)

            if small_threshold <= 0 or medium_threshold <= 0:
                raise ConfigurationError("File size thresholds must be positive")
            if small_threshold >= medium_threshold:
                raise ConfigurationError("Small threshold must be less than medium threshold")

        # Validate dimension classification
        dim_config = self._config.get('dimension_classification', {})
        if dim_config.get('enabled', True):
            res_thresholds = dim_config.get('resolution_thresholds', {})
            low_res = res_thresholds.get('low_resolution', [640, 480])
            standard_res = res_thresholds.get('standard_resolution', [1920, 1080])

            if len(low_res) != 2 or len(standard_res) != 2:
                raise ConfigurationError("Resolution thresholds must be [width, height] pairs")
            if any(x <= 0 for x in low_res + standard_res):
                raise ConfigurationError("Resolution values must be positive")
            if low_res[0] * low_res[1] >= standard_res[0] * standard_res[1]:
                raise ConfigurationError("Low resolution threshold must be smaller than standard")

        # Validate content classification
        content_config = self._config.get('content_classification', {})
        if content_config.get('enabled', True):
            available_models = content_config.get('available_models', [])
            valid_models = {'resnet50', 'efficientnet-b0', 'mobilenetv2'}
            if not set(available_models).issubset(valid_models):
                invalid_models = set(available_models) - valid_models
                raise ConfigurationError(f"Invalid content classification models: {invalid_models}")

            confidence = content_config.get('model_confidence_threshold', 0.3)
            if not 0 <= confidence <= 1:
                raise ConfigurationError("Content classification confidence threshold must be between 0 and 1")

        # Validate object detection
        obj_config = self._config.get('object_detection', {})
        if obj_config.get('enabled', False):
            confidence = obj_config.get('confidence_threshold', 0.5)
            iou = obj_config.get('iou_threshold', 0.45)
            max_detections = obj_config.get('max_detections', 100)

            if not 0 <= confidence <= 1:
                raise ConfigurationError("Object detection confidence threshold must be between 0 and 1")
            if not 0 <= iou <= 1:
                raise ConfigurationError("Object detection IoU threshold must be between 0 and 1")
            if max_detections <= 0:
                raise ConfigurationError("Max detections must be positive")

    def save_config(self, config: Dict[str, Any], output_path: Optional[str] = None):
        """
        Save configuration to file.
        
        Args:
            config: Configuration dictionary to save
            output_path: Optional output path (defaults to original config path)
            
        Raises:
            ConfigurationError: If saving fails
        """
        save_path = Path(output_path) if output_path else self.config_path
        
        if not save_path:
            raise ConfigurationError("No output path specified for saving configuration")
        
        try:
            save_path.parent.mkdir(parents=True, exist_ok=True)
            
            if save_path.suffix.lower() in ['.yaml', '.yml']:
                with open(save_path, 'w') as f:
                    yaml.dump(config, f, default_flow_style=False, indent=2)
            elif save_path.suffix.lower() == '.json':
                with open(save_path, 'w') as f:
                    json.dump(config, f, indent=2)
            else:
                raise ConfigurationError(f"Unsupported output format: {save_path.suffix}")
            
            self.logger.info(f"Configuration saved to {save_path}")
            
        except Exception as e:
            raise ConfigurationError(f"Failed to save configuration: {e}")
    
    def create_template_config(self, output_path: str):
        """
        Create a template configuration file with all available options.
        
        Args:
            output_path: Path where to save the template
        """
        template_config = deepcopy(self.DEFAULT_CONFIG)
        
        # Add comments/documentation
        template_with_comments = {
            '# Image Processing and Classification Configuration': None,
            '# Scanner Settings': None,
            'validate_mime_type': template_config['validate_mime_type'],
            
            '# Processor Settings': None,
            'target_size': template_config['target_size'],
            'maintain_aspect_ratio': template_config['maintain_aspect_ratio'],
            'convert_to_grayscale': template_config['convert_to_grayscale'],
            'normalize': template_config['normalize'],
            'enhance_quality': template_config['enhance_quality'],
            'batch_size': template_config['batch_size'],
            'brightness_factor': template_config['brightness_factor'],
            'contrast_factor': template_config['contrast_factor'],
            'sharpness_factor': template_config['sharpness_factor'],
            'noise_reduction': template_config['noise_reduction'],
            
            '# Classifier Settings': None,
            'use_pretrained_model': template_config['use_pretrained_model'],
            'model_name': template_config['model_name'],
            'confidence_threshold': template_config['confidence_threshold'],
            'custom_categories': template_config['custom_categories'],
            'size_thresholds': template_config['size_thresholds'],
            'dimension_thresholds': template_config['dimension_thresholds'],
            
            '# Dataset Manager Settings': None,
            'split_ratios': template_config['split_ratios'],
            'naming_pattern': template_config['naming_pattern'],
            'preserve_original_names': template_config['preserve_original_names'],
            'copy_images': template_config['copy_images'],
            'create_symlinks': template_config['create_symlinks'],
            'export_formats': template_config['export_formats'],
            
            '# General Settings': None,
            'resume': template_config['resume'],
            'log_level': template_config['log_level'],
            'log_file': template_config['log_file']
        }
        
        # Remove comment keys for actual saving
        clean_config = {k: v for k, v in template_with_comments.items() if not k.startswith('#')}
        
        self.save_config(clean_config, output_path)
        self.logger.info(f"Template configuration created at {output_path}")
    
    def get_config(self) -> Dict[str, Any]:
        """
        Get current configuration.
        
        Returns:
            Current configuration dictionary
        """
        return self._config.copy()
    
    def update_config(self, updates: Dict[str, Any]):
        """
        Update configuration with new values.
        
        Args:
            updates: Dictionary of configuration updates
        """
        self._config.update(updates)
        self._validate_config()
