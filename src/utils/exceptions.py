"""
Custom Exception Classes
========================

Custom exceptions for the image processing application.
"""


class ImageProcessingError(Exception):
    """Base exception for image processing errors."""
    pass


class ScannerError(ImageProcessingError):
    """Exception raised by the image scanner."""
    pass


class ProcessorError(ImageProcessingError):
    """Exception raised by the image processor."""
    pass


class ClassifierError(ImageProcessingError):
    """Exception raised by the image classifier."""
    pass


class DatasetError(ImageProcessingError):
    """Exception raised by the dataset manager."""
    pass


class ConfigurationError(ImageProcessingError):
    """Exception raised for configuration-related errors."""
    pass


class ValidationError(ImageProcessingError):
    """Exception raised for data validation errors."""
    pass


class AnalyticsError(ImageProcessingError):
    """Exception raised by analytics modules."""
    pass


class TrainingError(ImageProcessingError):
    """Exception raised during model training."""
    pass


class ApplicationError(ImageProcessingError):
    """General application error."""
    pass


class DataManagerError(ImageProcessingError):
    """Exception raised by data management modules."""
    pass
