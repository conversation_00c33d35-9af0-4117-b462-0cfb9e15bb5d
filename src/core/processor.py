"""
Image Processor Module
=====================

Comprehensive image preprocessing pipeline with quality enhancement,
resizing, format conversion, and normalization.
"""

import logging
from pathlib import Path
from typing import List, Dict, Tuple, Optional
import numpy as np
from PIL import Image, ImageEnhance, ImageFilter
import cv2
from tqdm import tqdm

from ..utils.exceptions import ProcessorError


class ImageProcessor:
    """
    Advanced image processor with quality enhancement and normalization.
    """
    
    def __init__(self, config: Dict):
        """
        Initialize the image processor.
        
        Args:
            config: Configuration dictionary
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Processing parameters
        self.target_size = tuple(config.get('target_size', (224, 224)))
        self.maintain_aspect_ratio = config.get('maintain_aspect_ratio', True)
        self.convert_to_grayscale = config.get('convert_to_grayscale', False)
        self.normalize = config.get('normalize', True)
        self.enhance_quality = config.get('enhance_quality', True)
        self.batch_size = config.get('batch_size', 32)
        
        # Quality enhancement parameters
        self.brightness_factor = config.get('brightness_factor', 1.0)
        self.contrast_factor = config.get('contrast_factor', 1.0)
        self.sharpness_factor = config.get('sharpness_factor', 1.0)
        self.noise_reduction = config.get('noise_reduction', True)
        
        self.stats = {
            'processed': 0,
            'failed': 0,
            'skipped': 0
        }
    
    def process_images(self, image_files: List[Dict]) -> List[Dict]:
        """
        Process a list of image files.
        
        Args:
            image_files: List of image file information dictionaries
            
        Returns:
            List of processed image data dictionaries
            
        Raises:
            ProcessorError: If processing fails
        """
        if not image_files:
            raise ProcessorError("No image files provided for processing")
        
        self.logger.info(f"Starting processing of {len(image_files)} images")
        
        processed_data = []
        
        # Process in batches
        for i in tqdm(range(0, len(image_files), self.batch_size), desc="Processing batches"):
            batch = image_files[i:i + self.batch_size]
            batch_results = self._process_batch(batch)
            processed_data.extend(batch_results)
        
        self.logger.info(f"Processing completed: {self.stats['processed']} successful, "
                        f"{self.stats['failed']} failed, {self.stats['skipped']} skipped")
        
        return processed_data
    
    def _process_batch(self, batch: List[Dict]) -> List[Dict]:
        """
        Process a batch of images.
        
        Args:
            batch: List of image file information dictionaries
            
        Returns:
            List of processed image data dictionaries
        """
        batch_results = []
        
        for file_info in batch:
            try:
                processed_info = self._process_single_image(file_info)
                if processed_info:
                    batch_results.append(processed_info)
                    self.stats['processed'] += 1
                else:
                    self.stats['skipped'] += 1
                    
            except Exception as e:
                self.logger.warning(f"Failed to process {file_info['filename']}: {e}")
                self.stats['failed'] += 1
        
        return batch_results
    
    def _process_single_image(self, file_info: Dict) -> Optional[Dict]:
        """
        Process a single image file.
        
        Args:
            file_info: Image file information dictionary
            
        Returns:
            Processed image data dictionary or None if processing failed
        """
        try:
            # Load image
            image_path = Path(file_info['absolute_path'])
            image = Image.open(image_path)
            
            # Convert to RGB if necessary
            if image.mode != 'RGB':
                image = image.convert('RGB')
            
            # Get original dimensions
            original_width, original_height = image.size
            
            # Apply quality enhancements
            if self.enhance_quality:
                image = self._enhance_image_quality(image)
            
            # Resize image
            if self.maintain_aspect_ratio:
                image = self._resize_with_aspect_ratio(image)
            else:
                image = image.resize(self.target_size, Image.Resampling.LANCZOS)
            
            # Convert to grayscale if requested
            if self.convert_to_grayscale:
                image = image.convert('L')
            
            # Convert to numpy array
            image_array = np.array(image)
            
            # Normalize if requested
            if self.normalize:
                image_array = self._normalize_image(image_array)
            
            # Create processed image info
            processed_info = file_info.copy()
            processed_info.update({
                'processed_array': image_array,
                'original_dimensions': (original_width, original_height),
                'processed_dimensions': image_array.shape[:2] if len(image_array.shape) == 3 else image_array.shape,
                'channels': 1 if self.convert_to_grayscale else 3,
                'normalized': self.normalize,
                'enhanced': self.enhance_quality
            })
            
            return processed_info
            
        except Exception as e:
            self.logger.error(f"Error processing image {file_info['filename']}: {e}")
            return None
    
    def _enhance_image_quality(self, image: Image.Image) -> Image.Image:
        """
        Apply quality enhancements to the image.
        
        Args:
            image: PIL Image object
            
        Returns:
            Enhanced PIL Image object
        """
        # Brightness adjustment
        if self.brightness_factor != 1.0:
            enhancer = ImageEnhance.Brightness(image)
            image = enhancer.enhance(self.brightness_factor)
        
        # Contrast adjustment
        if self.contrast_factor != 1.0:
            enhancer = ImageEnhance.Contrast(image)
            image = enhancer.enhance(self.contrast_factor)
        
        # Sharpness adjustment
        if self.sharpness_factor != 1.0:
            enhancer = ImageEnhance.Sharpness(image)
            image = enhancer.enhance(self.sharpness_factor)
        
        # Noise reduction
        if self.noise_reduction:
            image = image.filter(ImageFilter.MedianFilter(size=3))
        
        return image
    
    def _resize_with_aspect_ratio(self, image: Image.Image) -> Image.Image:
        """
        Resize image while maintaining aspect ratio.
        
        Args:
            image: PIL Image object
            
        Returns:
            Resized PIL Image object
        """
        original_width, original_height = image.size
        target_width, target_height = self.target_size
        
        # Calculate scaling factor
        scale_w = target_width / original_width
        scale_h = target_height / original_height
        scale = min(scale_w, scale_h)
        
        # Calculate new dimensions
        new_width = int(original_width * scale)
        new_height = int(original_height * scale)
        
        # Resize image
        image = image.resize((new_width, new_height), Image.Resampling.LANCZOS)
        
        # Create new image with target size and paste resized image
        new_image = Image.new('RGB', self.target_size, (0, 0, 0))
        
        # Calculate position to center the image
        x = (target_width - new_width) // 2
        y = (target_height - new_height) // 2
        
        new_image.paste(image, (x, y))
        
        return new_image
    
    def _normalize_image(self, image_array: np.ndarray) -> np.ndarray:
        """
        Normalize image array for deep learning compatibility.
        
        Args:
            image_array: Numpy array representing the image
            
        Returns:
            Normalized numpy array
        """
        # Convert to float32 and normalize to [0, 1]
        normalized = image_array.astype(np.float32) / 255.0
        
        # Apply standard normalization (ImageNet statistics)
        if len(normalized.shape) == 3 and normalized.shape[2] == 3:
            # RGB normalization
            mean = np.array([0.485, 0.456, 0.406])
            std = np.array([0.229, 0.224, 0.225])
            normalized = (normalized - mean) / std
        elif len(normalized.shape) == 2:
            # Grayscale normalization
            normalized = (normalized - 0.5) / 0.5
        
        return normalized
    
    def get_statistics(self) -> Dict:
        """
        Get processing statistics.
        
        Returns:
            Dictionary containing processing statistics
        """
        return self.stats.copy()
