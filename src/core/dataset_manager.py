"""
Dataset Manager Module
=====================

Manages dataset creation, naming conventions, metadata generation,
and export functionality.
"""

import logging
from pathlib import Path
from typing import List, Dict, Tuple, Optional
import json
import csv
import yaml
import shutil
from collections import defaultdict, Counter
import pandas as pd
from PIL import Image
from tqdm import tqdm

from ..utils.exceptions import DatasetError


class DatasetManager:
    """
    Comprehensive dataset manager with metadata generation and export.
    """
    
    def __init__(self, config: Dict):
        """
        Initialize the dataset manager.
        
        Args:
            config: Configuration dictionary
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Dataset parameters
        self.split_ratios = config.get('split_ratios', [0.7, 0.2, 0.1])
        self.naming_pattern = config.get('naming_pattern', '{category}_{index:06d}')
        self.preserve_original_names = config.get('preserve_original_names', False)
        self.copy_images = config.get('copy_images', True)
        self.create_symlinks = config.get('create_symlinks', False)
        
        # Export formats
        self.export_formats = config.get('export_formats', ['csv', 'json', 'yaml'])
        
        self.stats = {
            'total_images': 0,
            'train_images': 0,
            'val_images': 0,
            'test_images': 0,
            'categories': 0
        }
    
    def create_dataset(self, classified_data: List[Dict], output_path: Path):
        """
        Create organized dataset with metadata.
        
        Args:
            classified_data: List of classified image data dictionaries
            output_path: Output directory path
            
        Raises:
            DatasetError: If dataset creation fails
        """
        if not classified_data:
            raise DatasetError("No classified data provided for dataset creation")
        
        self.logger.info(f"Creating dataset with {len(classified_data)} images")
        
        # Create output directory structure
        output_path.mkdir(parents=True, exist_ok=True)
        
        # Organize data by categories
        categorized_data = self._categorize_data(classified_data)
        
        # Split data into train/validation/test sets
        split_data = self._split_data(categorized_data)
        
        # Create directory structure and copy/link images
        self._create_directory_structure(output_path, split_data)
        
        # Generate metadata
        metadata = self._generate_metadata(classified_data, split_data)
        
        # Export metadata in multiple formats
        self._export_metadata(metadata, output_path)
        
        # Generate dataset manifest
        manifest = self._generate_manifest(metadata, split_data)
        self._export_manifest(manifest, output_path)
        
        # Generate analytics and visualizations
        self._generate_analytics(metadata, output_path)
        
        self.logger.info("Dataset creation completed successfully")
    
    def _categorize_data(self, classified_data: List[Dict]) -> Dict[str, List[Dict]]:
        """
        Organize data by categories.
        
        Args:
            classified_data: List of classified image data dictionaries
            
        Returns:
            Dictionary mapping categories to lists of image data
        """
        categorized = defaultdict(list)
        
        for data in classified_data:
            # Determine primary category
            category = self._determine_primary_category(data)
            categorized[category].append(data)
        
        self.stats['categories'] = len(categorized)
        self.logger.info(f"Organized data into {len(categorized)} categories")
        
        return dict(categorized)
    
    def _determine_primary_category(self, data: Dict) -> str:
        """
        Determine the primary category for an image.
        
        Args:
            data: Image data dictionary
            
        Returns:
            Primary category string
        """
        # Priority order for category determination
        if 'best_custom_category' in data and data.get('best_custom_score', 0) > 0.5:
            return data['best_custom_category']
        
        if 'predicted_class' in data and data.get('high_confidence', False):
            return data['predicted_class']
        
        if 'directory_hint' in data and data['directory_hint'] != 'unknown':
            return f"dir_{data['directory_hint']}"
        
        # Fallback to size category
        return f"size_{data.get('size_category', 'unknown')}"
    
    def _split_data(self, categorized_data: Dict[str, List[Dict]]) -> Dict[str, Dict[str, List[Dict]]]:
        """
        Split data into train/validation/test sets.
        
        Args:
            categorized_data: Dictionary mapping categories to image data lists
            
        Returns:
            Dictionary with split data structure
        """
        split_data = {
            'train': defaultdict(list),
            'val': defaultdict(list),
            'test': defaultdict(list)
        }
        
        train_ratio, val_ratio, test_ratio = self.split_ratios
        
        for category, images in categorized_data.items():
            # Shuffle images for random split
            import random
            random.shuffle(images)
            
            total = len(images)
            train_end = int(total * train_ratio)
            val_end = train_end + int(total * val_ratio)
            
            split_data['train'][category] = images[:train_end]
            split_data['val'][category] = images[train_end:val_end]
            split_data['test'][category] = images[val_end:]
            
            self.stats['train_images'] += len(split_data['train'][category])
            self.stats['val_images'] += len(split_data['val'][category])
            self.stats['test_images'] += len(split_data['test'][category])
        
        self.stats['total_images'] = sum([
            self.stats['train_images'],
            self.stats['val_images'],
            self.stats['test_images']
        ])
        
        return split_data
    
    def _create_directory_structure(self, output_path: Path, split_data: Dict):
        """
        Create directory structure and organize images.
        
        Args:
            output_path: Output directory path
            split_data: Split data structure
        """
        self.logger.info("Creating directory structure and organizing images")
        
        for split_name, categories in split_data.items():
            split_path = output_path / split_name
            split_path.mkdir(exist_ok=True)
            
            for category, images in categories.items():
                category_path = split_path / category
                category_path.mkdir(exist_ok=True)
                
                # Process images in category
                for idx, image_data in enumerate(tqdm(images, desc=f"Processing {split_name}/{category}")):
                    self._process_image_file(image_data, category_path, category, idx)
    
    def _process_image_file(self, image_data: Dict, category_path: Path, category: str, index: int):
        """
        Process and organize individual image file.
        
        Args:
            image_data: Image data dictionary
            category_path: Category directory path
            category: Category name
            index: Image index within category
        """
        source_path = Path(image_data['absolute_path'])
        
        # Generate new filename
        if self.preserve_original_names:
            new_filename = image_data['filename']
        else:
            extension = source_path.suffix
            new_filename = f"{self.naming_pattern.format(category=category, index=index)}{extension}"
        
        target_path = category_path / new_filename
        
        # Update image data with new path
        image_data['dataset_path'] = str(target_path.relative_to(category_path.parent.parent))
        image_data['dataset_filename'] = new_filename
        
        try:
            if self.copy_images:
                shutil.copy2(source_path, target_path)
            elif self.create_symlinks:
                target_path.symlink_to(source_path.absolute())
            
        except Exception as e:
            self.logger.warning(f"Failed to process {source_path}: {e}")
    
    def _generate_metadata(self, classified_data: List[Dict], split_data: Dict) -> List[Dict]:
        """
        Generate comprehensive metadata for all images.
        
        Args:
            classified_data: List of classified image data
            split_data: Split data structure
            
        Returns:
            List of metadata dictionaries
        """
        metadata = []
        
        # Create mapping from image to split
        image_to_split = {}
        for split_name, categories in split_data.items():
            for category, images in categories.items():
                for image in images:
                    image_to_split[image['absolute_path']] = split_name
        
        for data in classified_data:
            metadata_entry = {
                # Basic file information
                'filename': data['filename'],
                'original_path': data['relative_path'],
                'absolute_path': data['absolute_path'],
                'dataset_path': data.get('dataset_path', ''),
                'dataset_filename': data.get('dataset_filename', ''),
                
                # File properties
                'format': data['format'],
                'size_bytes': data['size_bytes'],
                'directory': data['directory'],
                
                # Image properties
                'original_width': data.get('original_dimensions', [0, 0])[0],
                'original_height': data.get('original_dimensions', [0, 0])[1],
                'channels': data.get('channels', 3),
                
                # Processing information
                'processed': 'processed_array' in data,
                'normalized': data.get('normalized', False),
                'enhanced': data.get('enhanced', False),
                
                # Classification results
                'primary_category': self._determine_primary_category(data),
                'size_category': data.get('size_category', ''),
                'dimension_category': data.get('dimension_category', ''),
                'orientation': data.get('orientation', ''),
                'aspect_ratio': data.get('aspect_ratio', 0),

                # Enhanced file size classification
                'enhanced_size_category': data.get('enhanced_size_category', ''),
                'size_description': data.get('size_description', ''),
                'size_bytes_formatted': data.get('size_bytes_formatted', ''),

                # Enhanced dimension classification
                'enhanced_resolution_category': data.get('enhanced_resolution_category', ''),
                'resolution_description': data.get('resolution_description', ''),
                'total_pixels': data.get('total_pixels', 0),
                'enhanced_orientation': data.get('enhanced_orientation', ''),
                'aspect_ratio_category': data.get('aspect_ratio_category', ''),

                # Enhanced content classification
                'enhanced_predicted_class': data.get('enhanced_predicted_class', ''),
                'enhanced_prediction_confidence': data.get('enhanced_prediction_confidence', 0),
                'enhanced_high_confidence': data.get('enhanced_high_confidence', False),
                'model_used': data.get('model_used', ''),

                # Object detection results
                'total_objects_detected': data.get('total_objects_detected', 0),
                'object_classification': data.get('object_classification', ''),
                'detection_model_used': data.get('detection_model_used', ''),
                'unique_object_classes': data.get('unique_object_classes', []),

                # Legacy content classification
                'predicted_class': data.get('predicted_class', ''),
                'prediction_confidence': data.get('prediction_confidence', 0),
                'high_confidence': data.get('high_confidence', False),
                
                # Dataset split
                'split': image_to_split.get(data['absolute_path'], 'unknown')
            }
            
            # Add custom category scores
            for key, value in data.items():
                if key.startswith('custom_') and key.endswith('_score'):
                    metadata_entry[key] = value
            
            metadata.append(metadata_entry)
        
        return metadata
    
    def _export_metadata(self, metadata: List[Dict], output_path: Path):
        """
        Export metadata in multiple formats.
        
        Args:
            metadata: List of metadata dictionaries
            output_path: Output directory path
        """
        self.logger.info("Exporting metadata in multiple formats")
        
        if 'csv' in self.export_formats:
            csv_path = output_path / 'metadata.csv'
            df = pd.DataFrame(metadata)
            df.to_csv(csv_path, index=False)
        
        if 'json' in self.export_formats:
            json_path = output_path / 'metadata.json'
            with open(json_path, 'w') as f:
                json.dump(metadata, f, indent=2, default=str)
        
        if 'yaml' in self.export_formats:
            yaml_path = output_path / 'metadata.yaml'
            with open(yaml_path, 'w') as f:
                yaml.dump(metadata, f, default_flow_style=False)
    
    def _generate_manifest(self, metadata: List[Dict], split_data: Dict) -> Dict:
        """
        Generate dataset manifest with statistics.
        
        Args:
            metadata: List of metadata dictionaries
            split_data: Split data structure
            
        Returns:
            Manifest dictionary
        """
        manifest = {
            'dataset_info': {
                'total_images': len(metadata),
                'categories': list(set(item['primary_category'] for item in metadata)),
                'splits': {
                    'train': self.stats['train_images'],
                    'val': self.stats['val_images'],
                    'test': self.stats['test_images']
                },
                'split_ratios': self.split_ratios
            },
            'statistics': self._calculate_statistics(metadata),
            'creation_info': {
                'config': self.config,
                'naming_pattern': self.naming_pattern
            }
        }
        
        return manifest
    
    def _export_manifest(self, manifest: Dict, output_path: Path):
        """
        Export dataset manifest.
        
        Args:
            manifest: Manifest dictionary
            output_path: Output directory path
        """
        manifest_path = output_path / 'dataset_manifest.json'
        with open(manifest_path, 'w') as f:
            json.dump(manifest, f, indent=2, default=str)
    
    def _calculate_statistics(self, metadata: List[Dict]) -> Dict:
        """
        Calculate comprehensive dataset statistics.
        
        Args:
            metadata: List of metadata dictionaries
            
        Returns:
            Statistics dictionary
        """
        stats = {}
        
        # Category distribution
        categories = [item['primary_category'] for item in metadata]
        stats['category_distribution'] = dict(Counter(categories))
        
        # Format distribution
        formats = [item['format'] for item in metadata]
        stats['format_distribution'] = dict(Counter(formats))
        
        # Size statistics
        sizes = [item['size_bytes'] for item in metadata]
        stats['size_statistics'] = {
            'min': min(sizes),
            'max': max(sizes),
            'mean': sum(sizes) / len(sizes),
            'total': sum(sizes)
        }
        
        # Dimension statistics
        widths = [item['original_width'] for item in metadata if item['original_width'] > 0]
        heights = [item['original_height'] for item in metadata if item['original_height'] > 0]
        
        if widths and heights:
            stats['dimension_statistics'] = {
                'width': {'min': min(widths), 'max': max(widths), 'mean': sum(widths) / len(widths)},
                'height': {'min': min(heights), 'max': max(heights), 'mean': sum(heights) / len(heights)}
            }
        
        return stats
    
    def _generate_analytics(self, metadata: List[Dict], output_path: Path):
        """
        Generate analytics and visualizations.
        
        Args:
            metadata: List of metadata dictionaries
            output_path: Output directory path
        """
        # This would generate plots and analytics
        # For now, we'll create a simple analytics summary
        analytics_path = output_path / 'analytics_summary.txt'
        
        with open(analytics_path, 'w') as f:
            f.write("Dataset Analytics Summary\n")
            f.write("=" * 30 + "\n\n")
            f.write(f"Total Images: {len(metadata)}\n")
            f.write(f"Categories: {self.stats['categories']}\n")
            f.write(f"Train Images: {self.stats['train_images']}\n")
            f.write(f"Validation Images: {self.stats['val_images']}\n")
            f.write(f"Test Images: {self.stats['test_images']}\n")
    
    def get_statistics(self) -> Dict:
        """
        Get dataset creation statistics.
        
        Returns:
            Dictionary containing dataset statistics
        """
        return self.stats.copy()
