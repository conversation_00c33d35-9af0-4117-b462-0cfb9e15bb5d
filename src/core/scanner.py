"""
Image Scanner Module
===================

Recursively scans directories for images with comprehensive error handling
and progress tracking.
"""

import logging
from pathlib import Path
from typing import List, Dict, Set, Tuple
from collections import defaultdict
import mimetypes
from tqdm import tqdm

from ..utils.exceptions import ScannerError


class ImageScanner:
    """
    Advanced image scanner with recursive directory traversal,
    format validation, and comprehensive statistics.
    """
    
    # Supported image formats
    SUPPORTED_FORMATS = {
        '.jpg', '.jpeg', '.png', '.tiff', '.tif', '.bmp',
        '.JPG', '.JPEG', '.PNG', '.TIFF', '.TIF', '.BMP'
    }
    
    # MIME type mapping for additional validation
    MIME_TYPES = {
        'image/jpeg', 'image/png', 'image/tiff', 'image/bmp'
    }
    
    def __init__(self, config: Dict):
        """
        Initialize the image scanner.
        
        Args:
            config: Configuration dictionary
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        self.stats = defaultdict(int)
        self.errors = []
        
        # Initialize mime types
        mimetypes.init()
    
    def scan_directory(self, root_path: Path) -> List[Dict]:
        """
        Recursively scan directory for images.
        
        Args:
            root_path: Root directory to scan
            
        Returns:
            List of image file information dictionaries
            
        Raises:
            ScannerError: If scanning fails
        """
        if not root_path.exists():
            raise ScannerError(f"Directory does not exist: {root_path}")
        
        if not root_path.is_dir():
            raise ScannerError(f"Path is not a directory: {root_path}")
        
        self.logger.info(f"Starting scan of directory: {root_path}")
        
        # Reset statistics
        self.stats.clear()
        self.errors.clear()
        
        # Collect all potential image files
        image_files = []
        all_files = list(self._get_all_files(root_path))
        
        self.logger.info(f"Found {len(all_files)} total files")
        
        # Process files with progress bar
        with tqdm(total=len(all_files), desc="Scanning images") as pbar:
            for file_path in all_files:
                try:
                    file_info = self._process_file(file_path, root_path)
                    if file_info:
                        image_files.append(file_info)
                        self.stats[f"valid_{file_info['format']}"] += 1
                    else:
                        self.stats['skipped'] += 1
                        
                except Exception as e:
                    self.errors.append({
                        'file': str(file_path),
                        'error': str(e)
                    })
                    self.stats['errors'] += 1
                    self.logger.warning(f"Error processing {file_path}: {e}")
                
                pbar.update(1)
        
        # Log statistics
        self._log_statistics(image_files)
        
        return image_files
    
    def _get_all_files(self, root_path: Path):
        """
        Recursively get all files in directory.
        
        Args:
            root_path: Root directory to traverse
            
        Yields:
            Path objects for all files
        """
        try:
            for item in root_path.rglob('*'):
                if item.is_file():
                    yield item
        except PermissionError as e:
            self.logger.warning(f"Permission denied accessing {root_path}: {e}")
        except Exception as e:
            self.logger.error(f"Error traversing {root_path}: {e}")
    
    def _process_file(self, file_path: Path, root_path: Path) -> Dict:
        """
        Process individual file and extract information.
        
        Args:
            file_path: Path to the file
            root_path: Root directory path
            
        Returns:
            File information dictionary or None if not a valid image
        """
        # Check file extension
        if file_path.suffix not in self.SUPPORTED_FORMATS:
            return None
        
        # Validate MIME type if enabled in config
        if self.config.get('validate_mime_type', True):
            mime_type, _ = mimetypes.guess_type(str(file_path))
            if mime_type not in self.MIME_TYPES:
                self.logger.debug(f"Invalid MIME type for {file_path}: {mime_type}")
                return None
        
        # Get file statistics
        try:
            stat = file_path.stat()
            file_size = stat.st_size
            
            # Skip empty files
            if file_size == 0:
                self.logger.debug(f"Skipping empty file: {file_path}")
                return None
            
            # Calculate relative path
            relative_path = file_path.relative_to(root_path)
            
            return {
                'absolute_path': str(file_path),
                'relative_path': str(relative_path),
                'filename': file_path.name,
                'format': file_path.suffix.lower().lstrip('.'),
                'size_bytes': file_size,
                'directory': str(file_path.parent.relative_to(root_path)),
                'depth': len(file_path.relative_to(root_path).parts) - 1
            }
            
        except (OSError, ValueError) as e:
            self.logger.warning(f"Error getting file stats for {file_path}: {e}")
            return None
    
    def _log_statistics(self, image_files: List[Dict]):
        """
        Log comprehensive scanning statistics.
        
        Args:
            image_files: List of processed image files
        """
        total_files = len(image_files)
        self.logger.info(f"Scan completed: {total_files} valid images found")
        
        if self.stats['errors'] > 0:
            self.logger.warning(f"Encountered {self.stats['errors']} errors during scanning")
        
        # Format statistics
        format_stats = {}
        for key, value in self.stats.items():
            if key.startswith('valid_'):
                format_name = key.replace('valid_', '')
                format_stats[format_name] = value
        
        if format_stats:
            self.logger.info("Format distribution:")
            for format_name, count in sorted(format_stats.items()):
                percentage = (count / total_files) * 100 if total_files > 0 else 0
                self.logger.info(f"  {format_name.upper()}: {count} ({percentage:.1f}%)")
        
        # Directory statistics
        if image_files:
            directories = set(img['directory'] for img in image_files)
            self.logger.info(f"Images found in {len(directories)} directories")
            
            # Size statistics
            sizes = [img['size_bytes'] for img in image_files]
            total_size = sum(sizes)
            avg_size = total_size / len(sizes)
            
            self.logger.info(f"Total size: {self._format_size(total_size)}")
            self.logger.info(f"Average size: {self._format_size(avg_size)}")
    
    def _format_size(self, size_bytes: int) -> str:
        """
        Format file size in human-readable format.
        
        Args:
            size_bytes: Size in bytes
            
        Returns:
            Formatted size string
        """
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size_bytes < 1024.0:
                return f"{size_bytes:.1f} {unit}"
            size_bytes /= 1024.0
        return f"{size_bytes:.1f} TB"
    
    def get_statistics(self) -> Dict:
        """
        Get scanning statistics.
        
        Returns:
            Dictionary containing scanning statistics
        """
        return dict(self.stats)
    
    def get_errors(self) -> List[Dict]:
        """
        Get list of errors encountered during scanning.
        
        Returns:
            List of error dictionaries
        """
        return self.errors.copy()
