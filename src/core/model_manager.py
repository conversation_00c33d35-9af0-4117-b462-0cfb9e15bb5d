"""
Model Manager Module
===================

Manages deep learning models for content classification and object detection.
"""

import logging
from typing import Dict, List, Tuple, Optional, Any
import numpy as np
from pathlib import Path

try:
    import torch
    import torchvision.transforms as transforms
    import torchvision.models as models
    import timm
    TORCH_AVAILABLE = True
except ImportError:
    TORCH_AVAILABLE = False

try:
    from ultralytics import YOLO
    ULTRALYTICS_AVAILABLE = True
except ImportError:
    ULTRALYTICS_AVAILABLE = False

from ..utils.exceptions import ClassifierError


class ModelManager:
    """
    Manages deep learning models for image classification and object detection.
    """

    def __init__(self, config: Dict):
        """
        Initialize the model manager.

        Args:
            config: Configuration dictionary
        """
        self.config = config
        self.logger = logging.getLogger(__name__)

        # Model storage
        self.content_model = None
        self.object_detection_model = None
        self.device = self._get_device()

        # ImageNet class labels (simplified)
        self.imagenet_labels = self._get_imagenet_labels()

        # Initialize models based on configuration
        self._initialize_models()

    def _get_device(self) -> str:
        """
        Get the best available device for model inference.

        Returns:
            Device string ('cuda' or 'cpu')
        """
        if TORCH_AVAILABLE and torch.cuda.is_available():
            device = 'cuda'
            self.logger.info(f"Using GPU: {torch.cuda.get_device_name(0)}")
        else:
            device = 'cpu'
            self.logger.info("Using CPU for model inference")

        return device

    def _initialize_models(self):
        """Initialize models based on configuration."""
        # Initialize content classification model
        content_config = self.config.get('content_classification', {})
        if content_config.get('enabled', True) and TORCH_AVAILABLE:
            model_name = self.config.get('model_name', 'resnet50')
            self._load_content_model(model_name)

        # Initialize object detection model
        obj_config = self.config.get('object_detection', {})
        if obj_config.get('enabled', False) and ULTRALYTICS_AVAILABLE:
            model_name = obj_config.get('model_name', 'yolov8n')
            self._load_object_detection_model(model_name)

    def _load_content_model(self, model_name: str):
        """
        Load content classification model.

        Args:
            model_name: Name of the model to load
        """
        try:
            self.logger.info(f"Loading content classification model: {model_name}")

            if model_name == 'resnet50':
                self.content_model = models.resnet50(pretrained=True)
            elif model_name == 'efficientnet-b0':
                self.content_model = timm.create_model('efficientnet_b0', pretrained=True)
            elif model_name == 'mobilenetv2':
                self.content_model = models.mobilenet_v2(pretrained=True)
            else:
                raise ClassifierError(f"Unsupported content model: {model_name}")

            self.content_model.eval()
            self.content_model.to(self.device)

            # Define preprocessing transforms
            self.content_transforms = transforms.Compose([
                transforms.Resize(256),
                transforms.CenterCrop(224),
                transforms.ToTensor(),
                transforms.Normalize(mean=[0.485, 0.456, 0.406],
                                   std=[0.229, 0.224, 0.225])
            ])

            self.logger.info(f"Content classification model loaded successfully")

        except Exception as e:
            self.logger.error(f"Failed to load content model {model_name}: {e}")
            self.content_model = None

    def _load_object_detection_model(self, model_name: str):
        """
        Load object detection model.

        Args:
            model_name: Name of the YOLO model to load
        """
        try:
            self.logger.info(f"Loading object detection model: {model_name}")

            # Load YOLO model
            self.object_detection_model = YOLO(f"{model_name}.pt")

            self.logger.info(f"Object detection model loaded successfully")

        except Exception as e:
            self.logger.error(f"Failed to load object detection model {model_name}: {e}")
            self.object_detection_model = None

    def classify_content(self, image_array: np.ndarray) -> Dict:
        """
        Classify image content using the loaded model.

        Args:
            image_array: Image array (H, W, C)

        Returns:
            Dictionary with classification results
        """
        if not TORCH_AVAILABLE or self.content_model is None:
            return self._get_placeholder_classification()

        try:
            from PIL import Image

            # Convert numpy array to PIL Image
            if len(image_array.shape) == 3:
                image = Image.fromarray(image_array.astype(np.uint8))
            else:
                image = Image.fromarray(image_array.astype(np.uint8)).convert('RGB')

            # Preprocess image
            input_tensor = self.content_transforms(image).unsqueeze(0).to(self.device)

            # Run inference
            with torch.no_grad():
                outputs = self.content_model(input_tensor)
                probabilities = torch.nn.functional.softmax(outputs[0], dim=0)

            # Get top predictions
            content_config = self.config.get('content_classification', {})
            max_predictions = content_config.get('max_predictions', 5)
            confidence_threshold = content_config.get('model_confidence_threshold', 0.3)

            top_probs, top_indices = torch.topk(probabilities, max_predictions)

            predictions = []
            for i, (prob, idx) in enumerate(zip(top_probs, top_indices)):
                confidence = float(prob)
                if confidence >= confidence_threshold:
                    predictions.append({
                        'class': self.imagenet_labels[idx] if idx < len(self.imagenet_labels) else f'class_{idx}',
                        'confidence': confidence,
                        'rank': i + 1
                    })

            if predictions:
                best_prediction = predictions[0]
                return {
                    'content_predictions': predictions,
                    'predicted_class': best_prediction['class'],
                    'prediction_confidence': best_prediction['confidence'],
                    'high_confidence': best_prediction['confidence'] >= self.config.get('confidence_threshold', 0.5),
                    'model_used': self.config.get('model_name', 'resnet50')
                }
            else:
                return {
                    'content_predictions': [],
                    'predicted_class': 'unknown',
                    'prediction_confidence': 0.0,
                    'high_confidence': False,
                    'model_used': self.config.get('model_name', 'resnet50')
                }

        except Exception as e:
            self.logger.error(f"Content classification failed: {e}")
            return self._get_placeholder_classification()

    def detect_objects(self, image_array: np.ndarray) -> Dict:
        """
        Detect objects in the image using YOLO.

        Args:
            image_array: Image array (H, W, C)

        Returns:
            Dictionary with object detection results
        """
        if not ULTRALYTICS_AVAILABLE or self.object_detection_model is None:
            return self._get_placeholder_detection()

        try:
            obj_config = self.config.get('object_detection', {})
            confidence_threshold = obj_config.get('confidence_threshold', 0.5)
            iou_threshold = obj_config.get('iou_threshold', 0.45)
            max_detections = obj_config.get('max_detections', 100)

            # Run object detection
            results = self.object_detection_model(
                image_array,
                conf=confidence_threshold,
                iou=iou_threshold,
                max_det=max_detections,
                verbose=False
            )

            detections = []
            total_objects = 0

            for result in results:
                if result.boxes is not None:
                    boxes = result.boxes
                    for i in range(len(boxes)):
                        detection = {
                            'class_id': int(boxes.cls[i]),
                            'class_name': result.names[int(boxes.cls[i])],
                            'confidence': float(boxes.conf[i]),
                            'bbox': boxes.xyxy[i].tolist(),  # [x1, y1, x2, y2]
                            'bbox_normalized': boxes.xywhn[i].tolist()  # [x_center, y_center, width, height] normalized
                        }
                        detections.append(detection)
                        total_objects += 1

            # Classify as contains objects or object-free
            object_categories = obj_config.get('object_categories', {})
            contains_objects_label = object_categories.get('contains_objects', 'Contains Objects')
            object_free_label = object_categories.get('object_free', 'Object-Free')

            if total_objects > 0:
                object_classification = contains_objects_label
            else:
                object_classification = object_free_label

            return {
                'object_detections': detections,
                'total_objects_detected': total_objects,
                'object_classification': object_classification,
                'detection_model_used': obj_config.get('model_name', 'yolov8n'),
                'detection_confidence_threshold': confidence_threshold,
                'unique_object_classes': list(set(d['class_name'] for d in detections))
            }

        except Exception as e:
            self.logger.error(f"Object detection failed: {e}")
            return self._get_placeholder_detection()

    def _get_placeholder_classification(self) -> Dict:
        """Get placeholder classification results when models are not available."""
        return {
            'content_predictions': [],
            'predicted_class': 'unknown',
            'prediction_confidence': 0.0,
            'high_confidence': False,
            'model_used': 'placeholder'
        }

    def _get_placeholder_detection(self) -> Dict:
        """Get placeholder detection results when models are not available."""
        return {
            'object_detections': [],
            'total_objects_detected': 0,
            'object_classification': 'Object-Free',
            'detection_model_used': 'placeholder',
            'detection_confidence_threshold': 0.5,
            'unique_object_classes': []
        }

    def _get_imagenet_labels(self) -> List[str]:
        """
        Get simplified ImageNet class labels.

        Returns:
            List of class labels
        """
        # Simplified set of common ImageNet classes
        return [
            'person', 'bicycle', 'car', 'motorcycle', 'airplane', 'bus', 'train', 'truck',
            'boat', 'traffic_light', 'fire_hydrant', 'stop_sign', 'parking_meter', 'bench',
            'bird', 'cat', 'dog', 'horse', 'sheep', 'cow', 'elephant', 'bear', 'zebra',
            'giraffe', 'backpack', 'umbrella', 'handbag', 'tie', 'suitcase', 'frisbee',
            'skis', 'snowboard', 'sports_ball', 'kite', 'baseball_bat', 'baseball_glove',
            'skateboard', 'surfboard', 'tennis_racket', 'bottle', 'wine_glass', 'cup',
            'fork', 'knife', 'spoon', 'bowl', 'banana', 'apple', 'sandwich', 'orange',
            'broccoli', 'carrot', 'hot_dog', 'pizza', 'donut', 'cake', 'chair', 'couch',
            'potted_plant', 'bed', 'dining_table', 'toilet', 'tv', 'laptop', 'mouse',
            'remote', 'keyboard', 'cell_phone', 'microwave', 'oven', 'toaster', 'sink',
            'refrigerator', 'book', 'clock', 'vase', 'scissors', 'teddy_bear', 'hair_drier',
            'toothbrush', 'building', 'landscape', 'nature', 'indoor', 'outdoor', 'food',
            'animal', 'vehicle', 'furniture', 'electronics', 'clothing', 'sports', 'tools'
        ]

    def is_content_classification_available(self) -> bool:
        """Check if content classification is available."""
        return TORCH_AVAILABLE and self.content_model is not None

    def is_object_detection_available(self) -> bool:
        """Check if object detection is available."""
        return ULTRALYTICS_AVAILABLE and self.object_detection_model is not None