"""
Image Classifier Module
======================

Multi-criteria classification system with pre-trained models integration
and confidence scoring.
"""

import logging
from typing import List, Dict, Tuple, Optional
import numpy as np
from pathlib import Path
import json
from collections import defaultdict
from tqdm import tqdm

from ..utils.exceptions import ClassifierError
from .model_manager import ModelManager


class ImageClassifier:
    """
    Advanced image classifier with multiple classification criteria.
    """
    
    def __init__(self, config: Dict):
        """
        Initialize the image classifier.
        
        Args:
            config: Configuration dictionary
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Classification parameters
        self.use_pretrained_model = config.get('use_pretrained_model', True)
        self.model_name = config.get('model_name', 'resnet50')
        self.confidence_threshold = config.get('confidence_threshold', 0.5)
        self.custom_categories = config.get('custom_categories', [])
        
        # Size-based classification thresholds
        self.size_thresholds = config.get('size_thresholds', {
            'small': 100000,    # < 100KB
            'medium': 1000000,  # < 1MB
            'large': 5000000    # < 5MB
        })
        
        # Dimension-based classification thresholds
        self.dimension_thresholds = config.get('dimension_thresholds', {
            'thumbnail': (150, 150),
            'small': (512, 512),
            'medium': (1024, 1024),
            'large': (2048, 2048)
        })
        
        # Initialize model manager for enhanced classification
        self.model_manager = ModelManager(config)

        # Initialize pretrained model if requested (legacy support)
        self.model = None
        self.class_labels = []
        if self.use_pretrained_model:
            self._initialize_pretrained_model()
        
        self.stats = {
            'classified': 0,
            'failed': 0,
            'low_confidence': 0
        }
    
    def classify_images(self, processed_data: List[Dict]) -> List[Dict]:
        """
        Classify a list of processed images.
        
        Args:
            processed_data: List of processed image data dictionaries
            
        Returns:
            List of classified image data dictionaries
            
        Raises:
            ClassifierError: If classification fails
        """
        if not processed_data:
            raise ClassifierError("No processed data provided for classification")
        
        self.logger.info(f"Starting classification of {len(processed_data)} images")
        
        classified_data = []
        
        for data in tqdm(processed_data, desc="Classifying images"):
            try:
                classified_info = self._classify_single_image(data)
                classified_data.append(classified_info)
                self.stats['classified'] += 1
                
            except Exception as e:
                self.logger.warning(f"Failed to classify {data['filename']}: {e}")
                # Add basic classification even if advanced classification fails
                classified_info = self._add_basic_classification(data)
                classified_data.append(classified_info)
                self.stats['failed'] += 1
        
        self.logger.info(f"Classification completed: {self.stats['classified']} successful, "
                        f"{self.stats['failed']} failed, {self.stats['low_confidence']} low confidence")
        
        return classified_data
    
    def _classify_single_image(self, data: Dict) -> Dict:
        """
        Classify a single image using multiple criteria.
        
        Args:
            data: Processed image data dictionary
            
        Returns:
            Classified image data dictionary
        """
        classified_info = data.copy()
        
        # Basic classifications
        classified_info.update(self._add_basic_classification(data))
        
        # Enhanced content-based classification
        if 'processed_array' in data:
            # Use enhanced content classification with deep learning models
            enhanced_content = self._classify_enhanced_content(data['processed_array'])
            classified_info.update(enhanced_content)

            # Object detection classification
            object_detection = self._classify_objects(data['processed_array'])
            classified_info.update(object_detection)

        # Legacy content-based classification using pretrained model
        if self.model is not None and 'processed_array' in data:
            content_classification = self._classify_content(data['processed_array'])
            classified_info.update(content_classification)

        # Custom category classification
        if self.custom_categories:
            custom_classification = self._classify_custom_categories(data)
            classified_info.update(custom_classification)
        
        return classified_info
    
    def _add_basic_classification(self, data: Dict) -> Dict:
        """
        Add basic classification based on file properties.

        Args:
            data: Image data dictionary

        Returns:
            Dictionary with basic classification information
        """
        classification = {}

        # Enhanced file size classification
        classification.update(self._classify_file_size(data))

        # Legacy size-based classification (for backward compatibility)
        file_size = data['size_bytes']
        size_category = 'extra_large'
        for category, threshold in sorted(self.size_thresholds.items()):
            if file_size < threshold:
                size_category = category
                break

        classification['size_category'] = size_category

        # Enhanced dimension classification
        if 'original_dimensions' in data:
            classification.update(self._classify_dimensions(data))

        # Legacy dimension-based classification (for backward compatibility)
        if 'original_dimensions' in data:
            width, height = data['original_dimensions']
            dimension_category = 'extra_large'

            for category, (max_w, max_h) in sorted(self.dimension_thresholds.items()):
                if width <= max_w and height <= max_h:
                    dimension_category = category
                    break

            classification['dimension_category'] = dimension_category
        
        # Format-based classification
        classification['format_category'] = data['format']
        
        # Directory-based classification (simple heuristic)
        directory = data.get('directory', '')
        if directory:
            directory_lower = directory.lower()
            if any(keyword in directory_lower for keyword in ['train', 'training']):
                classification['directory_hint'] = 'training'
            elif any(keyword in directory_lower for keyword in ['test', 'testing']):
                classification['directory_hint'] = 'testing'
            elif any(keyword in directory_lower for keyword in ['val', 'valid', 'validation']):
                classification['directory_hint'] = 'validation'
            else:
                classification['directory_hint'] = 'unknown'
        
        return classification

    def _classify_file_size(self, data: Dict) -> Dict:
        """
        Enhanced file size classification with configurable thresholds.

        Args:
            data: Image data dictionary

        Returns:
            Dictionary with enhanced file size classification
        """
        classification = {}

        # Check if enhanced file size classification is enabled
        file_size_config = self.config.get('file_size_classification', {})
        if not file_size_config.get('enabled', True):
            return classification

        file_size = data['size_bytes']
        thresholds = file_size_config.get('thresholds', {})

        small_threshold = thresholds.get('small', 512000)  # 500KB
        medium_threshold = thresholds.get('medium', 2097152)  # 2MB

        # Classify based on enhanced thresholds
        if file_size < small_threshold:
            enhanced_size_category = 'small'
            size_description = f'< {self._format_size(small_threshold)}'
        elif file_size < medium_threshold:
            enhanced_size_category = 'medium'
            size_description = f'{self._format_size(small_threshold)} - {self._format_size(medium_threshold)}'
        else:
            enhanced_size_category = 'large'
            size_description = f'> {self._format_size(medium_threshold)}'

        classification.update({
            'enhanced_size_category': enhanced_size_category,
            'size_description': size_description,
            'size_bytes_formatted': self._format_size(file_size),
            'size_classification_thresholds': {
                'small_threshold': small_threshold,
                'medium_threshold': medium_threshold
            }
        })

        return classification

    def _format_size(self, size_bytes: int) -> str:
        """
        Format file size in human-readable format.

        Args:
            size_bytes: Size in bytes

        Returns:
            Formatted size string
        """
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size_bytes < 1024.0:
                return f"{size_bytes:.1f} {unit}"
            size_bytes /= 1024.0
        return f"{size_bytes:.1f} TB"

    def _classify_dimensions(self, data: Dict) -> Dict:
        """
        Enhanced dimension classification with resolution categories and aspect ratio analysis.

        Args:
            data: Image data dictionary

        Returns:
            Dictionary with enhanced dimension classification
        """
        classification = {}

        # Check if enhanced dimension classification is enabled
        dim_config = self.config.get('dimension_classification', {})
        if not dim_config.get('enabled', True):
            return classification

        width, height = data['original_dimensions']
        total_pixels = width * height

        # Resolution-based classification
        res_thresholds = dim_config.get('resolution_thresholds', {})
        low_res = res_thresholds.get('low_resolution', [640, 480])
        standard_res = res_thresholds.get('standard_resolution', [1920, 1080])

        low_res_pixels = low_res[0] * low_res[1]
        standard_res_pixels = standard_res[0] * standard_res[1]

        if total_pixels < low_res_pixels:
            resolution_category = 'low_resolution'
            resolution_description = f'< {low_res[0]}x{low_res[1]} ({low_res_pixels:,} pixels)'
        elif total_pixels < standard_res_pixels:
            resolution_category = 'standard_resolution'
            resolution_description = f'{low_res[0]}x{low_res[1]} to {standard_res[0]}x{standard_res[1]} ({low_res_pixels:,} - {standard_res_pixels:,} pixels)'
        else:
            resolution_category = 'high_resolution'
            resolution_description = f'> {standard_res[0]}x{standard_res[1]} ({standard_res_pixels:,}+ pixels)'

        # Aspect ratio analysis
        aspect_ratio = width / height if height > 0 else 0
        aspect_ratio_tolerance = dim_config.get('aspect_ratio_tolerance', 0.1)

        if abs(aspect_ratio - 1.0) <= aspect_ratio_tolerance:
            orientation = 'square'
            aspect_ratio_category = 'square'
        elif aspect_ratio > 1.0:
            orientation = 'landscape'
            if aspect_ratio >= 2.0:
                aspect_ratio_category = 'ultra_wide'
            elif aspect_ratio >= 1.5:
                aspect_ratio_category = 'wide'
            else:
                aspect_ratio_category = 'standard_landscape'
        else:
            orientation = 'portrait'
            if aspect_ratio <= 0.5:
                aspect_ratio_category = 'ultra_tall'
            elif aspect_ratio <= 0.67:
                aspect_ratio_category = 'tall'
            else:
                aspect_ratio_category = 'standard_portrait'

        classification.update({
            'enhanced_resolution_category': resolution_category,
            'resolution_description': resolution_description,
            'total_pixels': total_pixels,
            'total_pixels_formatted': f"{total_pixels:,}",
            'enhanced_orientation': orientation,
            'aspect_ratio': round(aspect_ratio, 3),
            'aspect_ratio_category': aspect_ratio_category,
            'dimension_classification_thresholds': {
                'low_resolution_pixels': low_res_pixels,
                'standard_resolution_pixels': standard_res_pixels
            }
        })

        return classification

    def _classify_enhanced_content(self, image_array: np.ndarray) -> Dict:
        """
        Enhanced content classification using deep learning models.

        Args:
            image_array: Processed image array

        Returns:
            Dictionary with enhanced content classification results
        """
        try:
            # Use model manager for content classification
            content_results = self.model_manager.classify_content(image_array)

            # Add enhanced prefix to distinguish from legacy classification
            enhanced_results = {}
            for key, value in content_results.items():
                if key.startswith('predicted_') or key.startswith('prediction_'):
                    enhanced_results[f'enhanced_{key}'] = value
                else:
                    enhanced_results[key] = value

            return enhanced_results

        except Exception as e:
            self.logger.error(f"Enhanced content classification failed: {e}")
            return {
                'content_predictions': [],
                'enhanced_predicted_class': 'unknown',
                'enhanced_prediction_confidence': 0.0,
                'enhanced_high_confidence': False,
                'model_used': 'error'
            }

    def _classify_objects(self, image_array: np.ndarray) -> Dict:
        """
        Object detection classification.

        Args:
            image_array: Processed image array

        Returns:
            Dictionary with object detection results
        """
        try:
            # Use model manager for object detection
            detection_results = self.model_manager.detect_objects(image_array)

            return detection_results

        except Exception as e:
            self.logger.error(f"Object detection failed: {e}")
            return {
                'object_detections': [],
                'total_objects_detected': 0,
                'object_classification': 'Object-Free',
                'detection_model_used': 'error',
                'detection_confidence_threshold': 0.5,
                'unique_object_classes': []
            }

    def _initialize_pretrained_model(self):
        """
        Initialize pretrained model for content classification.
        Note: This is a placeholder implementation. In a real scenario,
        you would load actual pretrained models like ResNet, EfficientNet, etc.
        """
        try:
            self.logger.info(f"Initializing pretrained model: {self.model_name}")
            
            # Placeholder for model initialization
            # In a real implementation, you would use:
            # - torchvision.models for PyTorch
            # - tensorflow.keras.applications for TensorFlow
            # - timm for a wide variety of models
            
            # For now, we'll simulate with a simple classifier
            self.model = "placeholder_model"
            
            # Load ImageNet class labels (simplified)
            self.class_labels = self._get_imagenet_labels()
            
            self.logger.info("Pretrained model initialized successfully")
            
        except Exception as e:
            self.logger.warning(f"Failed to initialize pretrained model: {e}")
            self.model = None
    
    def _classify_content(self, image_array: np.ndarray) -> Dict:
        """
        Classify image content using pretrained model.
        
        Args:
            image_array: Processed image array
            
        Returns:
            Dictionary with content classification results
        """
        # Placeholder implementation
        # In a real scenario, this would run inference on the pretrained model
        
        # Simulate classification results
        np.random.seed(hash(str(image_array.shape)) % 2**32)
        
        # Generate random predictions (for demonstration)
        num_classes = len(self.class_labels)
        predictions = np.random.random(num_classes)
        predictions = predictions / predictions.sum()  # Normalize to probabilities
        
        # Get top predictions
        top_indices = np.argsort(predictions)[-5:][::-1]
        top_predictions = [
            {
                'class': self.class_labels[i],
                'confidence': float(predictions[i])
            }
            for i in top_indices
        ]
        
        # Determine if confidence is high enough
        max_confidence = top_predictions[0]['confidence']
        is_confident = max_confidence >= self.confidence_threshold
        
        if not is_confident:
            self.stats['low_confidence'] += 1
        
        return {
            'content_predictions': top_predictions,
            'predicted_class': top_predictions[0]['class'],
            'prediction_confidence': max_confidence,
            'high_confidence': is_confident
        }
    
    def _classify_custom_categories(self, data: Dict) -> Dict:
        """
        Classify image into custom user-defined categories.
        
        Args:
            data: Image data dictionary
            
        Returns:
            Dictionary with custom classification results
        """
        # Simple heuristic-based classification for custom categories
        # In a real implementation, this could use trained models or rules
        
        custom_classification = {}
        filename_lower = data['filename'].lower()
        directory_lower = data.get('directory', '').lower()
        
        for category in self.custom_categories:
            category_lower = category.lower()
            
            # Check if category name appears in filename or directory
            score = 0.0
            if category_lower in filename_lower:
                score += 0.8
            if category_lower in directory_lower:
                score += 0.6
            
            # Additional heuristics based on file properties
            if category_lower in ['photo', 'photograph'] and data['format'] in ['jpg', 'jpeg']:
                score += 0.3
            elif category_lower in ['graphic', 'design'] and data['format'] in ['png', 'bmp']:
                score += 0.3
            
            custom_classification[f'custom_{category_lower}_score'] = min(score, 1.0)
        
        # Determine best matching custom category
        if custom_classification:
            best_category = max(custom_classification.items(), key=lambda x: x[1])
            custom_classification['best_custom_category'] = best_category[0].replace('custom_', '').replace('_score', '')
            custom_classification['best_custom_score'] = best_category[1]
        
        return custom_classification
    
    def _get_imagenet_labels(self) -> List[str]:
        """
        Get simplified ImageNet class labels.
        
        Returns:
            List of class labels
        """
        # Simplified set of common ImageNet classes
        return [
            'person', 'bicycle', 'car', 'motorcycle', 'airplane', 'bus', 'train', 'truck',
            'boat', 'traffic_light', 'fire_hydrant', 'stop_sign', 'parking_meter', 'bench',
            'bird', 'cat', 'dog', 'horse', 'sheep', 'cow', 'elephant', 'bear', 'zebra',
            'giraffe', 'backpack', 'umbrella', 'handbag', 'tie', 'suitcase', 'frisbee',
            'skis', 'snowboard', 'sports_ball', 'kite', 'baseball_bat', 'baseball_glove',
            'skateboard', 'surfboard', 'tennis_racket', 'bottle', 'wine_glass', 'cup',
            'fork', 'knife', 'spoon', 'bowl', 'banana', 'apple', 'sandwich', 'orange',
            'broccoli', 'carrot', 'hot_dog', 'pizza', 'donut', 'cake', 'chair', 'couch',
            'potted_plant', 'bed', 'dining_table', 'toilet', 'tv', 'laptop', 'mouse',
            'remote', 'keyboard', 'cell_phone', 'microwave', 'oven', 'toaster', 'sink',
            'refrigerator', 'book', 'clock', 'vase', 'scissors', 'teddy_bear', 'hair_drier',
            'toothbrush', 'building', 'landscape', 'nature', 'indoor', 'outdoor'
        ]
    
    def get_statistics(self) -> Dict:
        """
        Get comprehensive classification statistics.

        Returns:
            Dictionary containing classification statistics
        """
        enhanced_stats = self.stats.copy()

        # Add model availability information
        enhanced_stats.update({
            'content_classification_available': self.model_manager.is_content_classification_available(),
            'object_detection_available': self.model_manager.is_object_detection_available(),
            'enhanced_classification_enabled': True
        })

        return enhanced_stats
