# 🚁 تطبيق الإنقاذ والبحث المتقدم
# Advanced Search and Rescue Application

[![Python](https://img.shields.io/badge/Python-3.8+-blue.svg)](https://python.org)
[![License](https://img.shields.io/badge/License-MIT-green.svg)](LICENSE)
[![Version](https://img.shields.io/badge/Version-2.0.0-orange.svg)](CHANGELOG.md)

## 🎯 نظرة عامة | Overview

تطبيق متخصص ومتقدم لمعالجة وتصنيف صور عمليات الإنقاذ والبحث باستخدام تقنيات الذكاء الاصطناعي المتطورة. يهدف التطبيق إلى مساعدة فرق الإنقاذ والبحث في تحليل الصور الجوية والأقمار الصناعية لاكتشاف الناجين والحطام والمعدات في البيئات المختلفة.

A specialized and advanced application for processing and classifying search and rescue operation images using cutting-edge artificial intelligence techniques. The application aims to assist search and rescue teams in analyzing aerial and satellite images to detect survivors, debris, and equipment in various environments.

## ✨ الميزات الرئيسية | Key Features

### 🔍 **المسح الذكي المتخصص | Intelligent Specialized Scanning**
- مسح متكرر ومتكيف للدلائل | Recursive and adaptive directory scanning
- دعم تنسيقات متنوعة (JPEG, PNG, TIFF, GeoTIFF) | Support for various formats
- استخراج البيانات الجغرافية المكانية | Geospatial data extraction
- معالجة أخطاء قوية مع تسجيل مفصل | Robust error handling with detailed logging

### 🖼️ **المعالجة المتقدمة للصور | Advanced Image Processing**
- تحسين الرؤية في الظروف الصعبة | Vision enhancement in difficult conditions
- إزالة الضباب والدخان (Dark Channel Prior) | Dehazing and smoke removal
- تحسين الإضاءة المنخفضة (CLAHE) | Low-light enhancement
- تصحيح الألوان وإزالة الوهج | Color correction and glare removal

### 🎯 **التصنيف المتخصص للإنقاذ | Specialized Rescue Classification**
- تصنيف البيئات (بحر، صحراء، ساحل، حضري) | Environment classification
- كشف أهداف الإنقاذ (ناجين، حطام، معدات) | Rescue target detection
- دعم نماذج YOLO متعددة | Multiple YOLO model support
- التحقق البشري المستند إلى الثقة | Confidence-based human verification

### 📊 **إدارة البيانات الذكية | Smart Data Management**
- اتفاقية تسمية متخصصة | Specialized naming convention
- تصدير مجموعة بيانات YOLO كاملة | Complete YOLO dataset export
- إزالة التكرار الذكية | Intelligent deduplication
- بيانات وصفية شاملة | Comprehensive metadata

### 📈 **التحليلات التكتيكية المتقدمة | Advanced Tactical Analytics**
- تحليل مكاني متقدم مع التجميع | Advanced spatial analysis with clustering
- تحليل زمني وكشف الأنماط | Temporal analysis and pattern detection
- توليد رؤى تكتيكية ذكية | Smart tactical insights generation
- تصور تفاعلي للنتائج | Interactive results visualization

### 🤖 **تدريب نماذج YOLO المدمج | Integrated YOLO Model Training**
- تدريب نماذج YOLO متخصصة | Specialized YOLO model training
- واجهة تدريب تفاعلية | Interactive training interface
- مراقبة التقدم في الوقت الفعلي | Real-time progress monitoring

### 🖥️ **واجهة مستخدم محسنة | Enhanced User Interface**
- واجهة رسومية متقدمة مع دعم العربية | Advanced GUI with Arabic support
- معاينة الصور والنتائج | Image and results preview
- تقارير تفاعلية | Interactive reports

## 🚀 التثبيت السريع | Quick Installation

### المتطلبات الأساسية | Prerequisites
- Python 3.8 أو أحدث | Python 3.8+
- 8 GB RAM (16 GB مستحسن | recommended)
- NVIDIA GPU مع CUDA (اختياري للتسريع | optional for acceleration)

### التثبيت | Installation
```bash
# استنساخ المستودع | Clone repository
git clone https://github.com/your-repo/rescue-search-app.git
cd rescue-search-app

# تثبيت المتطلبات | Install requirements
pip install -r rescue_requirements.txt

# تشغيل الواجهة الرسومية | Launch GUI
python launch_rescue_gui.py
```

## ⚡ الاستخدام السريع | Quick Usage

### الواجهة الرسومية | GUI Interface
```bash
python launch_rescue_gui.py
```

### سطر الأوامر | Command Line
```bash
# معالجة أساسية | Basic processing
python rescue_main.py --input ./rescue_images --output ./rescue_dataset

# معالجة مع تحليلات | Processing with analytics
python rescue_main.py --input ./images --output ./results --analytics --verbose
```

### تدريب YOLO | YOLO Training
```bash
# تدريب أساسي | Basic training
python train_yolo_rescue.py --dataset ./rescue_dataset/yolo_dataset --epochs 100

# واجهة تدريب رسومية | GUI training interface
python train_yolo_rescue.py --gui
```

## 🎯 البيئات المدعومة | Supported Environments

- **🌊 البحر | Sea**: كشف الناجين في المياه، قوارب النجاة
- **🏜️ الصحراء | Desert**: البحث في البيئات الصحراوية
- **🏖️ الساحل | Coast**: عمليات الإنقاذ الساحلية
- **🏙️ الحضري | Urban**: البحث في المناطق الحضرية
- **🌲 الغابات | Forest**: البحث في الغابات
- **⛰️ الجبال | Mountains**: عمليات الإنقاذ الجبلية

## 🎯 فئات الأهداف | Target Categories

1. **👤 الأشخاص | Persons**: كشف الناجين والأشخاص المفقودين
2. **🦺 سترات النجاة | Life Jackets**: كشف سترات النجاة في المياه
3. **🚤 قوارب النجاة | Life Boats**: كشف قوارب النجاة والطوافات
4. **🧱 الحطام | Debris**: كشف حطام المباني والمركبات
5. **✈️ حطام الطائرات | Aircraft Wreckage**: كشف حطام الطائرات
6. **🚗 حطام المركبات | Vehicle Wreckage**: كشف حطام السيارات
7. **🚨 إشارات الطوارئ | Emergency Signals**: كشف إشارات الاستغاثة
8. **🛠️ معدات الإنقاذ | Rescue Equipment**: كشف معدات الإنقاذ

## 📊 مثال على النتائج | Results Example

```
rescue_results/
├── processed_images/          # الصور المعالجة | Processed images
│   ├── sea/                  # صور البحر | Sea images
│   ├── desert/               # صور الصحراء | Desert images
│   └── coast/                # صور الساحل | Coast images
├── yolo_dataset/             # مجموعة بيانات YOLO | YOLO dataset
│   ├── train/
│   ├── val/
│   ├── test/
│   └── data.yaml
├── analytics/                # التحليلات والتقارير | Analytics and reports
│   ├── tactical_report.json
│   ├── visualizations/
│   └── insights_report.json
└── metadata/                 # البيانات الوصفية | Metadata
    ├── rescue_metadata.csv
    └── processing_log.json
```

## 🔧 التكوين | Configuration

### ملف التكوين الأساسي | Basic Configuration File
```yaml
# config.yaml
processing:
  target_size: [512, 512]
  batch_size: 16
  enable_analytics: true

specialized_classification:
  environment_confidence_threshold: 0.7
  rescue_confidence_threshold: 0.5

advanced_preprocessing:
  enable_dehazing: true
  enable_low_light_enhancement: true
  enable_color_correction: true
```

## 📚 التوثيق | Documentation

- **[الدليل الشامل | Comprehensive Guide](COMPREHENSIVE_GUIDE.md)**: دليل مفصل لجميع الميزات
- **[دليل API | API Guide](docs/api/README.md)**: توثيق واجهة البرمجة
- **[أمثلة | Examples](examples/)**: أمثلة عملية للاستخدام
- **[الأسئلة الشائعة | FAQ](docs/FAQ.md)**: الأسئلة الشائعة وحلولها

## 🤝 المساهمة | Contributing

نرحب بمساهماتكم! يرجى مراجعة [دليل المساهمة](CONTRIBUTING.md) للحصول على التفاصيل.

We welcome contributions! Please see the [Contributing Guide](CONTRIBUTING.md) for details.

## 📄 الترخيص | License

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 الدعم | Support

- **GitHub Issues**: [إبلاغ عن الأخطاء | Report bugs](https://github.com/your-repo/rescue-search-app/issues)
- **المنتدى | Forum**: [مناقشات المجتمع | Community discussions](https://forum.rescue-search-app.org)
- **البريد الإلكتروني | Email**: <EMAIL>

## 🏆 الشكر والتقدير | Acknowledgments

- فرق الإنقاذ والبحث حول العالم | Search and rescue teams worldwide
- مجتمع الذكاء الاصطناعي مفتوح المصدر | Open source AI community
- مطوري مكتبات Python العلمية | Scientific Python library developers

---

**حقوق الطبع والنشر © 2024 فريق تطبيق الإنقاذ والبحث المتقدم**
**Copyright © 2024 Advanced Search and Rescue Application Team**

*تم التطوير بـ ❤️ لخدمة الإنسانية | Developed with ❤️ to serve humanity*
