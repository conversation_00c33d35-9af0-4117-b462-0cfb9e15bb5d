#!/usr/bin/env python3
"""
GUI Readiness Test
==================

Test if the GUI can be launched in the current environment.
"""

import sys
import os
from pathlib import Path

def test_python_version():
    """Test Python version compatibility."""
    print("🐍 Testing Python version...")
    if sys.version_info >= (3, 8):
        print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor} - Compatible")
        return True
    else:
        print(f"❌ Python {sys.version_info.major}.{sys.version_info.minor} - Requires 3.8+")
        return False

def test_tkinter():
    """Test tkinter availability."""
    print("\n🖼️  Testing tkinter availability...")
    try:
        import tkinter as tk
        print("✅ tkinter module imported successfully")
        
        # Test basic functionality
        try:
            root = tk.Tk()
            root.withdraw()  # Hide window
            root.destroy()
            print("✅ tkinter GUI creation successful")
            return True
        except Exception as e:
            print(f"❌ tkinter GUI creation failed: {e}")
            print("   This might indicate no display server available")
            return False
            
    except ImportError:
        print("❌ tkinter not available")
        print("   Install with:")
        print("   Ubuntu/Debian: sudo apt-get install python3-tk")
        print("   CentOS/RHEL: sudo dnf install python3-tkinter")
        print("   macOS: brew install python-tk")
        return False

def test_core_dependencies():
    """Test core application dependencies."""
    print("\n📦 Testing core dependencies...")
    
    dependencies = [
        ('numpy', 'NumPy'),
        ('pandas', 'Pandas'),
        ('PIL', 'Pillow'),
        ('cv2', 'OpenCV'),
        ('yaml', 'PyYAML'),
        ('tqdm', 'tqdm')
    ]
    
    all_available = True
    for module, name in dependencies:
        try:
            __import__(module)
            print(f"✅ {name}")
        except ImportError:
            print(f"❌ {name} - Missing")
            all_available = False
    
    return all_available

def test_gui_files():
    """Test if GUI files exist and are valid."""
    print("\n📁 Testing GUI files...")
    
    gui_files = [
        'gui_main.py',
        'launch_gui.py',
        'src/gui/config_editor.py'
    ]
    
    all_present = True
    for file_path in gui_files:
        if Path(file_path).exists():
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path} - Missing")
            all_present = False
    
    return all_present

def test_display_environment():
    """Test display environment variables."""
    print("\n🖥️  Testing display environment...")
    
    if os.name == 'nt':  # Windows
        print("✅ Windows - GUI should work")
        return True
    elif sys.platform == 'darwin':  # macOS
        print("✅ macOS - GUI should work")
        return True
    else:  # Linux/Unix
        display = os.environ.get('DISPLAY')
        if display:
            print(f"✅ DISPLAY environment variable set: {display}")
            return True
        else:
            print("❌ No DISPLAY environment variable")
            print("   This indicates no X11 display server")
            print("   GUI may not work in this environment")
            return False

def test_gui_syntax():
    """Test GUI file syntax."""
    print("\n🔍 Testing GUI code syntax...")
    
    import ast
    
    gui_files = {
        'gui_main.py': 'Main GUI application',
        'launch_gui.py': 'GUI launcher',
        'src/gui/config_editor.py': 'Configuration editor'
    }
    
    all_valid = True
    for file_path, description in gui_files.items():
        if Path(file_path).exists():
            try:
                with open(file_path, 'r') as f:
                    code = f.read()
                ast.parse(code)
                print(f"✅ {description} - Syntax valid")
            except SyntaxError as e:
                print(f"❌ {description} - Syntax error: {e}")
                all_valid = False
        else:
            print(f"⚠️  {description} - File not found")
    
    return all_valid

def provide_recommendations(results):
    """Provide recommendations based on test results."""
    print("\n" + "="*60)
    print(" RECOMMENDATIONS ".center(60, "="))
    print("="*60)
    
    python_ok, tkinter_ok, deps_ok, files_ok, display_ok, syntax_ok = results
    
    if all(results):
        print("🎉 EXCELLENT! GUI is ready to launch!")
        print("\nTo start the GUI:")
        print("   python3 launch_gui.py")
        print("\nOr directly:")
        print("   python3 gui_main.py")
        
    elif python_ok and deps_ok and files_ok and syntax_ok and not tkinter_ok:
        print("⚠️  GUI code is ready but tkinter is not available")
        print("\nTo install tkinter:")
        print("   Ubuntu/Debian: sudo apt-get install python3-tk")
        print("   CentOS/RHEL: sudo dnf install python3-tkinter")
        print("   macOS: brew install python-tk")
        print("\nAfter installation, run:")
        print("   python3 launch_gui.py")
        
    elif python_ok and tkinter_ok and deps_ok and files_ok and syntax_ok and not display_ok:
        print("⚠️  GUI is ready but no display server detected")
        print("\nThis environment may not support GUI applications.")
        print("Consider:")
        print("   • Using X11 forwarding: ssh -X user@host")
        print("   • Running on a desktop environment")
        print("   • Using the command-line interface instead:")
        print("     python3 main.py --input ./images --output ./dataset")
        
    else:
        print("❌ Some issues need to be resolved:")
        
        if not python_ok:
            print("   • Upgrade Python to 3.8 or higher")
        if not deps_ok:
            print("   • Install missing dependencies: pip install -r requirements.txt")
        if not files_ok:
            print("   • Ensure all GUI files are present")
        if not syntax_ok:
            print("   • Fix syntax errors in GUI files")
        
        print("\nAfter resolving issues, run this test again.")

def main():
    """Main test function."""
    print("🧪 GUI Readiness Test")
    print("="*60)
    
    # Run all tests
    results = [
        test_python_version(),
        test_tkinter(),
        test_core_dependencies(),
        test_gui_files(),
        test_display_environment(),
        test_gui_syntax()
    ]
    
    # Provide recommendations
    provide_recommendations(results)
    
    # Summary
    passed = sum(results)
    total = len(results)
    
    print(f"\n📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎯 Status: READY TO LAUNCH GUI! 🚀")
        return 0
    elif passed >= total - 1:
        print("🎯 Status: ALMOST READY (minor issues)")
        return 1
    else:
        print("🎯 Status: NEEDS ATTENTION (multiple issues)")
        return 2

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
