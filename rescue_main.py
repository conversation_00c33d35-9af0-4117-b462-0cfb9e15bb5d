#!/usr/bin/env python3
"""
تطبيق متقدم لمعالجة وتصنيف الصور للإنقاذ والبحث
Advanced Image Processing and Classification Application for Search and Rescue Operations
=======================================================================================

تطبيق متخصص مصمم خصيصاً لدعم عمليات البحث والإنقاذ في البيئات البحرية والصحراوية
من خلال معالجة متقدمة للصور واكتشاف الناجين والحطام باستخدام الذكاء الاصطناعي.

الميزات الرئيسية:
- مسح ذكي للدلائل مع دعم البيانات الجغرافية المكانية
- معالجة متقدمة للصور في الظروف الصعبة
- تصنيف متخصص للبيئات واكتشاف أهداف الإنقاذ
- إدارة بيانات ذكية مع تصدير لتنسيق YOLO
- تحليلات تكتيكية شاملة
- وحدة تدريب YOLO مدمجة
"""

import os
import sys
import argparse
import logging
import json
import yaml
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Optional

# إضافة مسار src إلى Python path
sys.path.insert(0, str(Path(__file__).parent / 'src'))

# استيراد الوحدات المتخصصة للإنقاذ
from src.rescue.intelligent_scanner import IntelligentRescueScanner
from src.rescue.advanced_preprocessor import AdvancedRescuePreprocessor
from src.rescue.specialized_classifier import RescueSpecializedClassifier
from src.rescue.smart_data_manager import SmartRescueDataManager
from src.rescue.tactical_analytics import TacticalRescueAnalytics

# استيراد الوحدات المساعدة
from src.utils.config_manager import ConfigManager
from src.utils.exceptions import ApplicationError


class RescueImageProcessingApplication:
    """
    التطبيق الرئيسي لمعالجة وتصنيف صور الإنقاذ والبحث
    Main application for rescue and search image processing and classification
    """
    
    def __init__(self, config_path: str = 'rescue_config.yaml'):
        """
        تهيئة التطبيق
        
        Args:
            config_path: مسار ملف التكوين
        """
        self.config_path = config_path
        self.config = None
        self.logger = None
        
        # المكونات الرئيسية
        self.scanner = None
        self.preprocessor = None
        self.classifier = None
        self.data_manager = None
        self.analytics = None
        
        # إحصائيات التشغيل
        self.execution_stats = {
            'start_time': None,
            'end_time': None,
            'total_images_processed': 0,
            'successful_classifications': 0,
            'failed_operations': 0,
            'datasets_created': 0
        }
        
        # تهيئة التطبيق
        self._initialize_application()
    
    def _initialize_application(self):
        """تهيئة جميع مكونات التطبيق"""
        try:
            # تحميل التكوين
            self._load_configuration()
            
            # إعداد نظام التسجيل
            self._setup_logging()
            
            # تهيئة المكونات
            self._initialize_components()
            
            self.logger.info("تم تهيئة تطبيق الإنقاذ والبحث بنجاح")
            
        except Exception as e:
            print(f"فشل في تهيئة التطبيق: {e}")
            sys.exit(1)
    
    def _load_configuration(self):
        """تحميل ملف التكوين"""
        try:
            config_manager = ConfigManager(self.config_path)
            self.config = config_manager.load_config()
            
            # إضافة إعدادات افتراضية للإنقاذ إذا لم تكن موجودة
            self._add_rescue_defaults()
            
        except Exception as e:
            # إنشاء تكوين افتراضي إذا فشل التحميل
            self.config = self._create_default_rescue_config()
            print(f"تم إنشاء تكوين افتراضي: {e}")
    
    def _add_rescue_defaults(self):
        """إضافة الإعدادات الافتراضية المتخصصة للإنقاذ"""
        rescue_defaults = {
            # إعدادات المسح المتخصص
            'intelligent_scanning': {
                'enable_geospatial_analysis': True,
                'supported_formats': ['.jpg', '.jpeg', '.png', '.tiff', '.tif', '.bmp'],
                'log_level': 'INFO'
            },
            
            # إعدادات المعالجة المتقدمة
            'advanced_preprocessing': {
                'enable_dehazing': True,
                'enable_low_light_enhancement': True,
                'enable_color_correction': True,
                'enable_glare_removal': True,
                'target_size': [512, 512],
                'preserve_aspect_ratio': True
            },
            
            # إعدادات التصنيف المتخصص
            'specialized_classification': {
                'environment_model': 'resnet50',
                'rescue_detection_model': 'yolov8m',
                'environment_confidence_threshold': 0.7,
                'rescue_confidence_threshold': 0.5,
                'enable_human_verification': True,
                'human_verification_threshold': 0.6
            },
            
            # إعدادات إدارة البيانات الذكية
            'smart_data_management': {
                'create_yolo_dataset': True,
                'enable_deduplication': True,
                'enable_versioning': True,
                'export_formats': ['csv', 'json', 'yaml'],
                'naming_convention': '{environment}_{object_status}_{timestamp}_{index:06d}.{extension}'
            },
            
            # إعدادات التحليلات التكتيكية
            'tactical_analytics': {
                'enable_statistical_analysis': True,
                'enable_heatmaps': True,
                'enable_interactive_plots': True,
                'export_formats': ['png', 'pdf', 'html']
            }
        }
        
        # دمج الإعدادات الافتراضية مع التكوين الحالي
        for section, settings in rescue_defaults.items():
            if section not in self.config:
                self.config[section] = settings
            else:
                for key, value in settings.items():
                    if key not in self.config[section]:
                        self.config[section][key] = value
    
    def _create_default_rescue_config(self) -> Dict:
        """إنشاء تكوين افتراضي للإنقاذ والبحث"""
        return {
            'input_directory': './rescue_images',
            'output_directory': './rescue_dataset',
            'target_size': [512, 512],
            'train_ratio': 0.7,
            'val_ratio': 0.2,
            'test_ratio': 0.1,
            'batch_size': 16,
            'log_level': 'INFO',
            
            # الإعدادات المتخصصة
            'intelligent_scanning': {
                'enable_geospatial_analysis': True,
                'log_level': 'INFO'
            },
            'advanced_preprocessing': {
                'enable_dehazing': True,
                'enable_low_light_enhancement': True,
                'target_size': [512, 512]
            },
            'specialized_classification': {
                'environment_model': 'resnet50',
                'rescue_detection_model': 'yolov8m',
                'rescue_confidence_threshold': 0.5
            },
            'smart_data_management': {
                'create_yolo_dataset': True,
                'enable_deduplication': True
            },
            'tactical_analytics': {
                'enable_statistical_analysis': True,
                'enable_heatmaps': True
            }
        }
    
    def _setup_logging(self):
        """إعداد نظام التسجيل المتقدم"""
        log_level = self.config.get('log_level', 'INFO')
        
        # إنشاء مجلد السجلات
        log_dir = Path('logs')
        log_dir.mkdir(exist_ok=True)
        
        # إعداد التسجيل
        logging.basicConfig(
            level=getattr(logging, log_level),
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(
                    log_dir / f'rescue_app_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log',
                    encoding='utf-8'
                ),
                logging.StreamHandler(sys.stdout)
            ]
        )
        
        self.logger = logging.getLogger(__name__)
        self.logger.info("تم إعداد نظام التسجيل للتطبيق المتخصص للإنقاذ")
    
    def _initialize_components(self):
        """تهيئة جميع المكونات المتخصصة"""
        try:
            # تهيئة الماسح الذكي
            scanner_config = {**self.config, **self.config.get('intelligent_scanning', {})}
            self.scanner = IntelligentRescueScanner(scanner_config)
            
            # تهيئة المعالج المتقدم
            preprocessor_config = {**self.config, **self.config.get('advanced_preprocessing', {})}
            self.preprocessor = AdvancedRescuePreprocessor(preprocessor_config)
            
            # تهيئة المصنف المتخصص
            classifier_config = {**self.config, **self.config.get('specialized_classification', {})}
            self.classifier = RescueSpecializedClassifier(classifier_config)
            
            # تهيئة مدير البيانات الذكي
            data_manager_config = {**self.config, **self.config.get('smart_data_management', {})}
            self.data_manager = SmartRescueDataManager(data_manager_config)
            
            # تهيئة محلل البيانات التكتيكي
            analytics_config = {**self.config, **self.config.get('tactical_analytics', {})}
            self.analytics = TacticalRescueAnalytics(analytics_config)
            
            self.logger.info("تم تهيئة جميع المكونات المتخصصة بنجاح")
            
        except Exception as e:
            self.logger.error(f"فشل في تهيئة المكونات: {e}")
            raise ApplicationError(f"فشل في تهيئة المكونات: {e}")
    
    def process_rescue_images(self, input_directory: str, output_directory: str) -> Dict:
        """
        معالجة شاملة لصور الإنقاذ والبحث
        
        Args:
            input_directory: دليل الصور المدخلة
            output_directory: دليل الإخراج
            
        Returns:
            نتائج المعالجة الشاملة
        """
        try:
            self.execution_stats['start_time'] = datetime.now()
            self.logger.info(f"بدء معالجة صور الإنقاذ من: {input_directory}")
            
            # المرحلة 1: المسح الذكي
            self.logger.info("المرحلة 1: المسح الذكي للصور...")
            discovered_images = self.scanner.scan_directory(input_directory)
            self.execution_stats['total_images_processed'] = len(discovered_images)
            
            if not discovered_images:
                raise ApplicationError("لم يتم العثور على صور صالحة في الدليل المحدد")
            
            # المرحلة 2: المعالجة المتقدمة
            self.logger.info("المرحلة 2: المعالجة المتقدمة للصور...")
            processed_images = []
            
            for image_data in discovered_images:
                try:
                    processed_image = self.preprocessor.process_image(image_data)
                    processed_images.append(processed_image)
                except Exception as e:
                    self.logger.warning(f"فشل في معالجة الصورة {image_data.get('filename', 'unknown')}: {e}")
                    self.execution_stats['failed_operations'] += 1
            
            # المرحلة 3: التصنيف المتخصص
            self.logger.info("المرحلة 3: التصنيف المتخصص...")
            classified_images = []
            
            for processed_image in processed_images:
                try:
                    classified_image = self.classifier.classify_image(processed_image)
                    classified_images.append(classified_image)
                    self.execution_stats['successful_classifications'] += 1
                except Exception as e:
                    self.logger.warning(f"فشل في تصنيف الصورة {processed_image.get('filename', 'unknown')}: {e}")
                    self.execution_stats['failed_operations'] += 1
            
            # المرحلة 4: إدارة البيانات الذكية
            self.logger.info("المرحلة 4: إدارة البيانات وإنشاء مجموعة البيانات...")
            dataset_info = self.data_manager.create_rescue_dataset(classified_images, output_directory)
            self.execution_stats['datasets_created'] += 1
            
            # المرحلة 5: التحليلات التكتيكية
            self.logger.info("المرحلة 5: التحليلات التكتيكية...")
            analysis_results = self.analytics.analyze_rescue_dataset(
                output_directory, 
                dataset_info.get('metadata_files', [None])[0]
            )
            
            # توليد الرسوم البيانية
            visualizations = self.analytics.generate_tactical_visualizations(
                analysis_results, 
                Path(output_directory) / 'analytics'
            )
            
            # تصدير التقرير التكتيكي
            tactical_report = self.analytics.export_tactical_report(
                analysis_results, 
                output_directory
            )
            
            # إنهاء المعالجة
            self.execution_stats['end_time'] = datetime.now()
            
            # تجميع النتائج النهائية
            final_results = {
                'execution_summary': self._generate_execution_summary(),
                'dataset_info': dataset_info,
                'analysis_results': analysis_results,
                'visualizations': visualizations,
                'tactical_report': tactical_report,
                'scan_statistics': self.scanner.get_scan_statistics(),
                'processing_statistics': self.preprocessor.get_processing_statistics(),
                'classification_statistics': self.classifier.get_classification_statistics(),
                'data_management_statistics': self.data_manager.get_data_management_statistics()
            }
            
            self.logger.info("اكتملت معالجة صور الإنقاذ بنجاح")
            return final_results
            
        except Exception as e:
            self.logger.error(f"فشل في معالجة صور الإنقاذ: {e}")
            raise ApplicationError(f"فشل في المعالجة: {e}")
    
    def _generate_execution_summary(self) -> Dict:
        """توليد ملخص تنفيذ العملية"""
        duration = (self.execution_stats['end_time'] - self.execution_stats['start_time']).total_seconds()
        
        return {
            'execution_time_seconds': duration,
            'execution_time_formatted': f"{duration // 3600:.0f}:{(duration % 3600) // 60:02.0f}:{duration % 60:02.0f}",
            'total_images_found': self.execution_stats['total_images_processed'],
            'successful_classifications': self.execution_stats['successful_classifications'],
            'failed_operations': self.execution_stats['failed_operations'],
            'success_rate': (self.execution_stats['successful_classifications'] / 
                           max(self.execution_stats['total_images_processed'], 1)) * 100,
            'datasets_created': self.execution_stats['datasets_created'],
            'processing_speed_images_per_second': self.execution_stats['total_images_processed'] / max(duration, 1)
        }
    
    def export_configuration(self, output_path: str):
        """تصدير التكوين الحالي"""
        config_path = Path(output_path) / 'rescue_config_export.yaml'
        
        with open(config_path, 'w', encoding='utf-8') as f:
            yaml.dump(self.config, f, default_flow_style=False, allow_unicode=True)
        
        self.logger.info(f"تم تصدير التكوين إلى: {config_path}")
        return str(config_path)


def create_rescue_config_template():
    """إنشاء قالب ملف تكوين للإنقاذ والبحث"""
    template_config = {
        # الإعدادات الأساسية
        'input_directory': './rescue_images',
        'output_directory': './rescue_dataset',
        'log_level': 'INFO',
        'log_directory': './logs',

        # إعدادات المعالجة
        'target_size': [512, 512],
        'batch_size': 16,
        'preserve_aspect_ratio': True,

        # إعدادات تقسيم البيانات
        'train_ratio': 0.7,
        'val_ratio': 0.2,
        'test_ratio': 0.1,
        'stratified_split': True,

        # إعدادات المسح الذكي
        'intelligent_scanning': {
            'enable_geospatial_analysis': True,
            'supported_formats': ['.jpg', '.jpeg', '.png', '.tiff', '.tif', '.bmp', '.webp'],
            'log_level': 'INFO'
        },

        # إعدادات المعالجة المتقدمة
        'advanced_preprocessing': {
            'target_size': [512, 512],
            'preserve_aspect_ratio': True,
            'brightness_factor': 1.2,
            'contrast_factor': 1.3,
            'saturation_factor': 1.1,
            'sharpness_factor': 1.2,
            'enable_dehazing': True,
            'enable_low_light_enhancement': True,
            'enable_color_correction': True,
            'enable_glare_removal': True,
            'denoise_method': 'bilateral',
            'convert_to_grayscale': False,
            'normalize_images': True,
            'normalization_method': 'standard'
        },

        # إعدادات التصنيف المتخصص
        'specialized_classification': {
            'environment_model': 'resnet50',
            'environment_confidence_threshold': 0.7,
            'rescue_detection_model': 'yolov8m',
            'rescue_confidence_threshold': 0.5,
            'rescue_iou_threshold': 0.45,
            'max_detections': 100,
            'enable_human_verification': True,
            'human_verification_threshold': 0.6,
            'custom_rescue_categories': [],
            'device': 'auto',
            'enable_gpu_acceleration': True
        },

        # إعدادات إدارة البيانات الذكية
        'smart_data_management': {
            'naming_convention': '{environment}_{object_status}_{timestamp}_{index:06d}.{extension}',
            'create_yolo_dataset': True,
            'yolo_format_version': 'v8',
            'export_formats': ['csv', 'json', 'yaml'],
            'enable_deduplication': True,
            'hash_algorithm': 'phash',
            'similarity_threshold': 5,
            'enable_versioning': True,
            'copy_files': True,
            'include_geospatial_metadata': True
        },

        # إعدادات التحليلات التكتيكية
        'tactical_analytics': {
            'enable_statistical_analysis': True,
            'enable_heatmaps': True,
            'enable_interactive_plots': True,
            'figure_size': [12, 8],
            'dpi': 300,
            'export_formats': ['png', 'pdf', 'html'],
            'analyze_temporal_patterns': True,
            'analyze_spatial_distribution': True,
            'analyze_detection_patterns': True
        }
    }

    config_path = Path('rescue_config_template.yaml')
    with open(config_path, 'w', encoding='utf-8') as f:
        yaml.dump(template_config, f, default_flow_style=False, allow_unicode=True)

    print(f"تم إنشاء قالب التكوين: {config_path}")
    return str(config_path)


def main():
    """الدالة الرئيسية للتطبيق"""
    parser = argparse.ArgumentParser(
        description='تطبيق متقدم لمعالجة وتصنيف صور الإنقاذ والبحث',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
أمثلة الاستخدام:
================

1. معالجة أساسية:
   python rescue_main.py --input ./rescue_images --output ./rescue_dataset

2. معالجة مع تكوين مخصص:
   python rescue_main.py --input ./images --output ./dataset --config custom_config.yaml

3. إنشاء قالب تكوين:
   python rescue_main.py --create-config-template

4. معالجة مع تحليلات مفصلة:
   python rescue_main.py --input ./images --output ./dataset --verbose --analytics

5. معالجة مع تصدير YOLO:
   python rescue_main.py --input ./images --output ./dataset --yolo-export

الميزات المتخصصة:
==================
- مسح ذكي مع دعم البيانات الجغرافية المكانية (GeoTIFF)
- معالجة متقدمة للظروف الصعبة (ضباب، إضاءة منخفضة، وهج)
- تصنيف متخصص للبيئات (بحر، صحراء، ساحل)
- كشف أهداف الإنقاذ (ناجين، حطام، معدات طوارئ)
- إدارة بيانات ذكية مع إزالة التكرار
- تحليلات تكتيكية شاملة مع رسوم بيانية
- تصدير مجموعة بيانات YOLO جاهزة للتدريب
        """
    )

    # المعاملات الأساسية
    parser.add_argument(
        '--input', '-i',
        type=str,
        help='دليل الصور المدخلة'
    )

    parser.add_argument(
        '--output', '-o',
        type=str,
        help='دليل الإخراج لمجموعة البيانات'
    )

    parser.add_argument(
        '--config', '-c',
        type=str,
        default='rescue_config.yaml',
        help='مسار ملف التكوين (افتراضي: rescue_config.yaml)'
    )

    # خيارات متقدمة
    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='تفعيل الوضع المفصل للتسجيل'
    )

    parser.add_argument(
        '--analytics',
        action='store_true',
        help='تفعيل التحليلات التكتيكية المفصلة'
    )

    parser.add_argument(
        '--yolo-export',
        action='store_true',
        help='تفعيل تصدير مجموعة بيانات YOLO'
    )

    parser.add_argument(
        '--create-config-template',
        action='store_true',
        help='إنشاء قالب ملف تكوين'
    )

    parser.add_argument(
        '--dry-run',
        action='store_true',
        help='تشغيل تجريبي بدون معالجة فعلية'
    )

    # معاملات التحكم في الأداء
    parser.add_argument(
        '--batch-size',
        type=int,
        help='حجم الدفعة للمعالجة'
    )

    parser.add_argument(
        '--target-size',
        type=int,
        nargs=2,
        metavar=('WIDTH', 'HEIGHT'),
        help='الحجم المستهدف للصور (العرض الارتفاع)'
    )

    parser.add_argument(
        '--confidence-threshold',
        type=float,
        help='عتبة الثقة لكشف الأهداف'
    )

    args = parser.parse_args()

    try:
        # إنشاء قالب التكوين إذا طُلب
        if args.create_config_template:
            create_rescue_config_template()
            return 0

        # التحقق من المعاملات المطلوبة
        if not args.input or not args.output:
            parser.error("يجب تحديد دليل الإدخال والإخراج")

        # التحقق من وجود دليل الإدخال
        input_path = Path(args.input)
        if not input_path.exists():
            print(f"خطأ: دليل الإدخال غير موجود: {args.input}")
            return 1

        # إنشاء دليل الإخراج إذا لم يكن موجوداً
        output_path = Path(args.output)
        output_path.mkdir(parents=True, exist_ok=True)

        print("🚁 تطبيق معالجة وتصنيف صور الإنقاذ والبحث")
        print("=" * 60)
        print(f"📁 دليل الإدخال: {args.input}")
        print(f"📁 دليل الإخراج: {args.output}")
        print(f"⚙️  ملف التكوين: {args.config}")

        if args.dry_run:
            print("🧪 تشغيل تجريبي - لن يتم معالجة الصور فعلياً")
            return 0

        # تهيئة التطبيق
        print("\n🔧 تهيئة التطبيق...")
        app = RescueImageProcessingApplication(args.config)

        # تطبيق معاملات سطر الأوامر على التكوين
        if args.verbose:
            app.config['log_level'] = 'DEBUG'

        if args.batch_size:
            app.config['batch_size'] = args.batch_size

        if args.target_size:
            app.config['target_size'] = args.target_size
            app.config['advanced_preprocessing']['target_size'] = args.target_size

        if args.confidence_threshold:
            app.config['specialized_classification']['rescue_confidence_threshold'] = args.confidence_threshold

        if args.yolo_export:
            app.config['smart_data_management']['create_yolo_dataset'] = True

        if args.analytics:
            app.config['tactical_analytics']['enable_statistical_analysis'] = True
            app.config['tactical_analytics']['enable_heatmaps'] = True
            app.config['tactical_analytics']['enable_interactive_plots'] = True

        # بدء المعالجة
        print("\n🚀 بدء معالجة صور الإنقاذ والبحث...")
        results = app.process_rescue_images(args.input, args.output)

        # عرض النتائج
        print("\n✅ اكتملت المعالجة بنجاح!")
        print("=" * 60)

        execution_summary = results['execution_summary']
        print(f"⏱️  وقت التنفيذ: {execution_summary['execution_time_formatted']}")
        print(f"📊 إجمالي الصور: {execution_summary['total_images_found']}")
        print(f"✅ تصنيفات ناجحة: {execution_summary['successful_classifications']}")
        print(f"❌ عمليات فاشلة: {execution_summary['failed_operations']}")
        print(f"📈 معدل النجاح: {execution_summary['success_rate']:.1f}%")
        print(f"⚡ سرعة المعالجة: {execution_summary['processing_speed_images_per_second']:.2f} صورة/ثانية")

        # معلومات مجموعة البيانات
        dataset_info = results['dataset_info']
        print(f"\n📦 مجموعة البيانات المنشأة:")
        print(f"   📁 المسار: {dataset_info['output_directory']}")
        print(f"   📊 إجمالي الصور: {dataset_info['total_images']}")

        if dataset_info.get('yolo_dataset'):
            print(f"   🎯 مجموعة بيانات YOLO: متوفرة")
            print(f"   📄 ملف data.yaml: {dataset_info['yolo_dataset']['data_yaml']}")

        # معلومات التحليلات
        if 'tactical_report' in results:
            print(f"   📋 التقرير التكتيكي: {results['tactical_report']}")

        if 'visualizations' in results and results['visualizations']:
            print(f"   📈 الرسوم البيانية: {len(results['visualizations'])} رسم")

        # تصدير ملخص النتائج
        summary_path = output_path / 'execution_summary.json'
        with open(summary_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2, default=str)

        print(f"\n💾 تم حفظ ملخص النتائج: {summary_path}")
        print("\n🎉 تم إنجاز جميع العمليات بنجاح!")

        return 0

    except KeyboardInterrupt:
        print("\n⚠️ تم إيقاف العملية بواسطة المستخدم")
        return 1
    except Exception as e:
        print(f"\n❌ خطأ في التطبيق: {e}")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
