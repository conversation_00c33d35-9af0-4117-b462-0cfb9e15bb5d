{"dataset_info": {"total_images": 14, "categories": ["size_large"], "splits": {"train": 8, "val": 4, "test": 2}, "split_ratios": [0.6, 0.3, 0.1]}, "statistics": {"category_distribution": {"size_large": 14}, "format_distribution": {"png": 7, "jpg": 7}, "size_statistics": {"min": 69, "max": 192995, "mean": 34167.42857142857, "total": 478344}, "dimension_statistics": {"width": {"min": 1, "max": 2000, "mean": 541.5714285714286}, "height": {"min": 1, "max": 1500, "mean": 532.3571428571429}}}, "creation_info": {"config": {"validate_mime_type": true, "target_size": [224, 224], "maintain_aspect_ratio": true, "convert_to_grayscale": false, "normalize": true, "enhance_quality": true, "batch_size": 32, "brightness_factor": 1.0, "contrast_factor": 1.0, "sharpness_factor": 1.0, "noise_reduction": true, "use_pretrained_model": true, "model_name": "resnet50", "confidence_threshold": 0.5, "custom_categories": [], "size_thresholds": {"small": 100000, "medium": 1000000, "large": 5000000}, "dimension_thresholds": {"thumbnail": [150, 150], "small": [512, 512], "medium": [1024, 1024], "large": [2048, 2048]}, "split_ratios": [0.6, 0.3, 0.1], "naming_pattern": "{category}_{index:06d}", "preserve_original_names": false, "copy_images": true, "create_symlinks": false, "export_formats": ["csv", "json", "yaml"], "resume": false, "log_level": "INFO", "log_file": null, "input_dir": "test_images", "output_dir": "final_test_output"}, "naming_pattern": "{category}_{index:06d}"}}